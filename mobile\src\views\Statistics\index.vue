<template>
  <div class="statistics-page">
    <van-nav-bar 
      title="数据统计" 
      fixed
      placeholder
      safe-area-inset-top
    >
      <template #right>
        <van-icon name="setting-o" size="18" @click="showSettings = true" />
      </template>
    </van-nav-bar>

    <div class="page-container">
      <!-- 时间选择器 -->
      <div class="time-selector">
        <van-tabs v-model:active="activeTimeRange" @change="onTimeRangeChange">
          <van-tab title="本月" name="month" />
          <van-tab title="本季" name="quarter" />
          <van-tab title="本年" name="year" />
          <van-tab title="自定义" name="custom" />
        </van-tabs>
      </div>

      <!-- 统计卡片 -->
      <div class="stats-overview">
        <div class="stats-grid">
          <div class="stat-card primary">
            <div class="stat-icon">💰</div>
            <div class="stat-content">
              <div class="stat-label">总收入</div>
              <div class="stat-value">{{ formatCurrency(overview.totalIncome) }}</div>
              <div class="stat-change" :class="getChangeClass(overview.incomeChange)">
                {{ formatChange(overview.incomeChange) }}
              </div>
            </div>
          </div>

          <div class="stat-card danger">
            <div class="stat-icon">💸</div>
            <div class="stat-content">
              <div class="stat-label">总支出</div>
              <div class="stat-value">{{ formatCurrency(overview.totalExpense) }}</div>
              <div class="stat-change" :class="getChangeClass(overview.expenseChange, true)">
                {{ formatChange(overview.expenseChange) }}
              </div>
            </div>
          </div>

          <div class="stat-card success">
            <div class="stat-icon">📊</div>
            <div class="stat-content">
              <div class="stat-label">净收入</div>
              <div class="stat-value">{{ formatCurrency(overview.netIncome) }}</div>
              <div class="stat-change" :class="getChangeClass(overview.netIncomeChange)">
                {{ formatChange(overview.netIncomeChange) }}
              </div>
            </div>
          </div>

          <div class="stat-card info">
            <div class="stat-icon">📈</div>
            <div class="stat-content">
              <div class="stat-label">交易笔数</div>
              <div class="stat-value">{{ overview.transactionCount }}</div>
              <div class="stat-change" :class="getChangeClass(overview.countChange)">
                {{ formatChange(overview.countChange) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="charts-section">
        <!-- 收支趋势图 -->
        <div class="chart-container">
          <div class="chart-header">
            <h3>收支趋势</h3>
            <van-button size="mini" type="primary" plain @click="toggleTrendChart">
              {{ trendChartType === 'line' ? '柱状图' : '折线图' }}
            </van-button>
          </div>
          <div class="chart-content">
            <BaseChart 
              v-if="chartData.trend"
              :options="chartData.trend" 
              height="240px" 
            />
            <div v-else class="chart-loading">
              <van-loading size="24px" />
              <span>加载中...</span>
            </div>
          </div>
        </div>

        <!-- 支出分类饼图 -->
        <div class="chart-container">
          <div class="chart-header">
            <h3>支出分类</h3>
            <van-button size="mini" type="primary" plain @click="showCategoryDetail">
              详情
            </van-button>
          </div>
          <div class="chart-content">
            <BaseChart 
              v-if="chartData.expenseCategory"
              :options="chartData.expenseCategory" 
              height="240px" 
            />
            <div v-else class="chart-loading">
              <van-loading size="24px" />
              <span>加载中...</span>
            </div>
          </div>
        </div>

        <!-- 收入来源分析 -->
        <div class="chart-container">
          <div class="chart-header">
            <h3>收入来源</h3>
            <van-button size="mini" type="primary" plain @click="showIncomeDetail">
              详情
            </van-button>
          </div>
          <div class="chart-content">
            <BaseChart 
              v-if="chartData.incomeCategory"
              :options="chartData.incomeCategory" 
              height="240px" 
            />
            <div v-else class="chart-loading">
              <van-loading size="24px" />
              <span>加载中...</span>
            </div>
          </div>
        </div>

        <!-- 账户分布 -->
        <div class="chart-container">
          <div class="chart-header">
            <h3>账户分布</h3>
            <van-button size="mini" type="primary" plain @click="showAccountDetail">
              详情
            </van-button>
          </div>
          <div class="chart-content">
            <BaseChart 
              v-if="chartData.accountDistribution"
              :options="chartData.accountDistribution" 
              height="240px" 
            />
            <div v-else class="chart-loading">
              <van-loading size="24px" />
              <span>加载中...</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="quick-actions">
        <van-grid :column-num="2" :gutter="12">
          <van-grid-item @click="generateReport">
            <van-icon name="description" size="24" />
            <span>生成报告</span>
          </van-grid-item>
          <van-grid-item @click="exportData">
            <van-icon name="download" size="24" />
            <span>导出数据</span>
          </van-grid-item>
        </van-grid>
      </div>
    </div>

    <!-- 自定义时间选择 -->
    <van-popup v-model:show="showCustomTime" position="bottom" round>
      <van-datetime-picker
        v-model="customTimeRange"
        type="date"
        title="选择时间范围"
        @confirm="onCustomTimeConfirm"
        @cancel="showCustomTime = false"
      />
    </van-popup>

    <!-- 设置弹窗 -->
    <van-popup v-model:show="showSettings" position="bottom" round>
      <div class="settings-content">
        <div class="settings-header">
          <h3>统计设置</h3>
          <van-button type="primary" size="small" @click="showSettings = false">
            完成
          </van-button>
        </div>
        <van-cell-group>
          <van-cell title="默认货币" :value="defaultCurrency" is-link @click="showCurrencyPicker = true" />
          <van-cell title="图表主题" :value="chartTheme" is-link @click="showThemePicker = true" />
          <van-cell title="数据精度" :value="dataPrecision + '位小数'" is-link @click="showPrecisionPicker = true" />
        </van-cell-group>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useTransactionsStore } from '@/stores/transactions'
import { useAccountsStore } from '@/stores/accounts'
import BaseChart from '@/components/charts/BaseChart.vue'
import { formatCurrency, formatDate } from '@/utils/format'
import { showToast } from 'vant'

const router = useRouter()
const transactionsStore = useTransactionsStore()
const accountsStore = useAccountsStore()

// 响应式数据
const loading = ref(false)
const activeTimeRange = ref('month')
const trendChartType = ref('line')
const showCustomTime = ref(false)
const showSettings = ref(false)
const showCurrencyPicker = ref(false)
const showThemePicker = ref(false)
const showPrecisionPicker = ref(false)
const customTimeRange = ref(new Date())

// 设置选项
const defaultCurrency = ref('RM')
const chartTheme = ref('默认')
const dataPrecision = ref(2)

// 统计概览数据
const overview = reactive({
  totalIncome: 0,
  totalExpense: 0,
  netIncome: 0,
  transactionCount: 0,
  incomeChange: 0,
  expenseChange: 0,
  netIncomeChange: 0,
  countChange: 0
})

// 图表数据
const chartData = reactive({
  trend: null,
  expenseCategory: null,
  incomeCategory: null,
  accountDistribution: null
})

// 初始化
onMounted(async () => {
  await loadData()
})

// 监听时间范围变化
watch(activeTimeRange, () => {
  if (activeTimeRange.value === 'custom') {
    showCustomTime.value = true
  } else {
    loadData()
  }
})

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    await transactionsStore.fetchTransactions()
    await accountsStore.fetchAccounts()

    calculateOverview()
    generateCharts()
  } catch (error) {
    console.error('加载统计数据失败:', error)
    showToast('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 计算统计概览
const calculateOverview = () => {
  const transactions = getFilteredTransactions()
  const previousTransactions = getPreviousTransactions()

  // 当前期间统计
  const currentIncome = transactions.filter(t => t.type === 'income').reduce((sum, t) => sum + t.amount, 0)
  const currentExpense = transactions.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0)
  const currentNet = currentIncome - currentExpense
  const currentCount = transactions.length

  // 上期统计
  const previousIncome = previousTransactions.filter(t => t.type === 'income').reduce((sum, t) => sum + t.amount, 0)
  const previousExpense = previousTransactions.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0)
  const previousNet = previousIncome - previousExpense
  const previousCount = previousTransactions.length

  // 更新概览数据
  overview.totalIncome = currentIncome
  overview.totalExpense = currentExpense
  overview.netIncome = currentNet
  overview.transactionCount = currentCount

  // 计算变化百分比
  overview.incomeChange = calculatePercentageChange(previousIncome, currentIncome)
  overview.expenseChange = calculatePercentageChange(previousExpense, currentExpense)
  overview.netIncomeChange = calculatePercentageChange(previousNet, currentNet)
  overview.countChange = calculatePercentageChange(previousCount, currentCount)
}

// 获取筛选后的交易
const getFilteredTransactions = () => {
  const now = new Date()
  let startDate, endDate

  switch (activeTimeRange.value) {
    case 'month':
      startDate = new Date(now.getFullYear(), now.getMonth(), 1)
      endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0)
      break
    case 'quarter':
      const quarter = Math.floor(now.getMonth() / 3)
      startDate = new Date(now.getFullYear(), quarter * 3, 1)
      endDate = new Date(now.getFullYear(), quarter * 3 + 3, 0)
      break
    case 'year':
      startDate = new Date(now.getFullYear(), 0, 1)
      endDate = new Date(now.getFullYear(), 11, 31)
      break
    default:
      startDate = new Date(now.getFullYear(), now.getMonth(), 1)
      endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0)
  }

  return transactionsStore.transactions.filter(t => {
    const date = new Date(t.date)
    return date >= startDate && date <= endDate
  })
}

// 获取上期交易数据
const getPreviousTransactions = () => {
  const now = new Date()
  let startDate, endDate

  switch (activeTimeRange.value) {
    case 'month':
      startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1)
      endDate = new Date(now.getFullYear(), now.getMonth(), 0)
      break
    case 'quarter':
      const quarter = Math.floor(now.getMonth() / 3)
      startDate = new Date(now.getFullYear(), (quarter - 1) * 3, 1)
      endDate = new Date(now.getFullYear(), quarter * 3, 0)
      break
    case 'year':
      startDate = new Date(now.getFullYear() - 1, 0, 1)
      endDate = new Date(now.getFullYear() - 1, 11, 31)
      break
    default:
      startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1)
      endDate = new Date(now.getFullYear(), now.getMonth(), 0)
  }

  return transactionsStore.transactions.filter(t => {
    const date = new Date(t.date)
    return date >= startDate && date <= endDate
  })
}

// 计算百分比变化
const calculatePercentageChange = (oldValue, newValue) => {
  if (oldValue === 0) {
    return newValue > 0 ? 100 : 0
  }
  return Math.round(((newValue - oldValue) / oldValue) * 100)
}

// 生成图表
const generateCharts = () => {
  const transactions = getFilteredTransactions()

  // 生成收支趋势图
  generateTrendChart(transactions)

  // 生成支出分类饼图
  generateExpenseCategoryChart(transactions)

  // 生成收入来源图
  generateIncomeCategoryChart(transactions)

  // 生成账户分布图
  generateAccountDistributionChart(transactions)
}

// 生成收支趋势图
const generateTrendChart = (transactions) => {
  const groupedData = groupTransactionsByDate(transactions)
  const dates = Object.keys(groupedData).sort()

  const incomeData = dates.map(date => {
    const dayTransactions = groupedData[date]
    return dayTransactions.filter(t => t.type === 'income').reduce((sum, t) => sum + t.amount, 0)
  })

  const expenseData = dates.map(date => {
    const dayTransactions = groupedData[date]
    return dayTransactions.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0)
  })

  chartData.trend = {
    title: {
      text: '收支趋势',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach(param => {
          result += `${param.seriesName}: ${formatCurrency(param.value)}<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['收入', '支出'],
      bottom: 10
    },
    xAxis: {
      type: 'category',
      data: dates.map(date => formatDate(date, 'MM-DD'))
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => formatCurrency(value, '', 0)
      }
    },
    series: [
      {
        name: '收入',
        type: trendChartType.value,
        data: incomeData,
        itemStyle: {
          color: '#07c160'
        },
        smooth: true
      },
      {
        name: '支出',
        type: trendChartType.value,
        data: expenseData,
        itemStyle: {
          color: '#ee0a24'
        },
        smooth: true
      }
    ]
  }
}

// 生成支出分类饼图
const generateExpenseCategoryChart = (transactions) => {
  const expenseTransactions = transactions.filter(t => t.type === 'expense')
  const categoryData = {}

  expenseTransactions.forEach(t => {
    categoryData[t.category] = (categoryData[t.category] || 0) + t.amount
  })

  const pieData = Object.entries(categoryData).map(([category, amount]) => ({
    name: category,
    value: amount
  })).sort((a, b) => b.value - a.value)

  chartData.expenseCategory = {
    title: {
      text: '支出分类',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: pieData.map(item => item.name)
    },
    series: [
      {
        name: '支出分类',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        data: pieData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
}

// 生成收入来源图
const generateIncomeCategoryChart = (transactions) => {
  const incomeTransactions = transactions.filter(t => t.type === 'income')
  const categoryData = {}

  incomeTransactions.forEach(t => {
    categoryData[t.category] = (categoryData[t.category] || 0) + t.amount
  })

  const pieData = Object.entries(categoryData).map(([category, amount]) => ({
    name: category,
    value: amount
  })).sort((a, b) => b.value - a.value)

  chartData.incomeCategory = {
    title: {
      text: '收入来源',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: pieData.map(item => item.name)
    },
    series: [
      {
        name: '收入来源',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        data: pieData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
}

// 生成账户分布图
const generateAccountDistributionChart = (transactions) => {
  const accountData = {}

  transactions.forEach(t => {
    const account = accountsStore.accounts.find(acc => acc.id === t.account)
    const accountName = account ? account.name : '未知账户'
    accountData[accountName] = (accountData[accountName] || 0) + t.amount
  })

  const barData = Object.entries(accountData).map(([account, amount]) => ({
    name: account,
    value: amount
  })).sort((a, b) => b.value - a.value)

  chartData.accountDistribution = {
    title: {
      text: '账户分布',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        return `${params[0].axisValue}: ${formatCurrency(params[0].value)}`
      }
    },
    xAxis: {
      type: 'category',
      data: barData.map(item => item.name),
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => formatCurrency(value, '', 0)
      }
    },
    series: [
      {
        name: '金额',
        type: 'bar',
        data: barData.map(item => item.value),
        itemStyle: {
          color: '#1989fa'
        }
      }
    ]
  }
}

// 按日期分组交易
const groupTransactionsByDate = (transactions) => {
  const grouped = {}
  transactions.forEach(t => {
    const date = t.date.split('T')[0] // 只取日期部分
    if (!grouped[date]) {
      grouped[date] = []
    }
    grouped[date].push(t)
  })
  return grouped
}

// 工具方法
const formatChange = (change) => {
  if (change > 0) {
    return `+${change}%`
  } else if (change < 0) {
    return `${change}%`
  } else {
    return '0%'
  }
}

const getChangeClass = (change, isExpense = false) => {
  if (change > 0) {
    return isExpense ? 'negative' : 'positive'
  } else if (change < 0) {
    return isExpense ? 'positive' : 'negative'
  } else {
    return 'neutral'
  }
}

// 事件处理
const onTimeRangeChange = () => {
  loadData()
}

const onCustomTimeConfirm = () => {
  showCustomTime.value = false
  // 这里可以根据自定义时间范围重新加载数据
  loadData()
}

const toggleTrendChart = () => {
  trendChartType.value = trendChartType.value === 'line' ? 'bar' : 'line'
  generateTrendChart(getFilteredTransactions())
}

const showCategoryDetail = () => {
  router.push('/statistics/category')
}

const showIncomeDetail = () => {
  router.push('/statistics/income')
}

const showAccountDetail = () => {
  router.push('/statistics/account')
}

const generateReport = () => {
  router.push('/statistics/report')
}

const exportData = () => {
  showToast('导出功能开发中...')
}
</script>

<style scoped>
.statistics-page {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.page-container {
  padding-bottom: 20px;
}

.time-selector {
  background: white;
  margin-bottom: 12px;
}

.stats-overview {
  margin: 0 16px 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border-left: 4px solid;
}

.stat-card.primary {
  border-left-color: #1989fa;
}

.stat-card.danger {
  border-left-color: #ee0a24;
}

.stat-card.success {
  border-left-color: #07c160;
}

.stat-card.info {
  border-left-color: #909399;
}

.stat-icon {
  font-size: 24px;
  margin-right: 12px;
}

.stat-content {
  flex: 1;
}

.stat-label {
  font-size: 12px;
  color: #969799;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 2px;
}

.stat-change {
  font-size: 12px;
}

.stat-change.positive {
  color: #07c160;
}

.stat-change.negative {
  color: #ee0a24;
}

.stat-change.neutral {
  color: #969799;
}

.charts-section {
  margin: 0 16px;
}

.chart-container {
  background: white;
  border-radius: 12px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 16px 0;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

.chart-content {
  padding: 16px;
}

.chart-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 240px;
  color: #969799;
}

.chart-loading span {
  margin-top: 8px;
  font-size: 14px;
}

.quick-actions {
  margin: 16px;
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.settings-content {
  padding: 20px;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.settings-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}
</style>
