import{a as c,r as j,l as i,h as O,m as Q,s,g as Z,c as D,e as l,d as S,w as u,f as o,p as ee,o as g,i as te}from"./index--MNqwREY.js";import{useAccountsStore as ae}from"./accounts-bk1VVrk3.js";import{_ as ne}from"./_plugin-vue_export-helper-DlAUqK2U.js";const le={class:"edit-account-page"},oe={class:"page-container"},se={key:0,class:"loading-container"},ue={__name:"Edit",setup(re){const h=Z(),Y=O(),v=ae(),m=c(!1),x=c(!1),d=c(!1),p=c(!1),C=c(!1),y=c(!1),t=j({name:"",type:"",currency:"MYR",initial_balance:"0",description:""}),V=[{text:"🏦 银行账户",value:"bank"},{text:"💵 现金",value:"cash"},{text:"💰 投资账户",value:"investment"},{text:"🏪 电子钱包",value:"ewallet"}],B=[{text:"MYR - 马来西亚林吉特",value:"MYR"},{text:"USD - 美元",value:"USD"},{text:"CNY - 人民币",value:"CNY"},{text:"EUR - 欧元",value:"EUR"},{text:"GBP - 英镑",value:"GBP"},{text:"JPY - 日元",value:"JPY"},{text:"SGD - 新加坡元",value:"SGD"}],G={MYR:{bank:[{text:"Maybank",value:"Maybank"},{text:"HLB",value:"HLB"},{text:"PBB",value:"PBB"},{text:"GXBank",value:"GXBank"}],ewallet:[{text:"TNG eWallet",value:"TNG eWallet"}],investment:[{text:"KDI",value:"KDI"},{text:"moomoo",value:"moomoo"},{text:"EPF",value:"EPF"}],cash:[{text:"Cash",value:"Cash"}]},USD:{bank:[{text:"ABA",value:"ABA"}],ewallet:[{text:"Cash",value:"Cash"}],investment:[{text:"Cash",value:"Cash"}],cash:[{text:"Cash",value:"Cash"}]},CNY:{bank:[{text:"Cash",value:"Cash"}],ewallet:[{text:"Cash",value:"Cash"}],investment:[{text:"Cash",value:"Cash"}],cash:[{text:"Cash",value:"Cash"}]},EUR:{bank:[{text:"Cash",value:"Cash"}],ewallet:[{text:"Cash",value:"Cash"}],investment:[{text:"Cash",value:"Cash"}],cash:[{text:"Cash",value:"Cash"}]},GBP:{bank:[{text:"Cash",value:"Cash"}],ewallet:[{text:"Cash",value:"Cash"}],investment:[{text:"Cash",value:"Cash"}],cash:[{text:"Cash",value:"Cash"}]},JPY:{bank:[{text:"Cash",value:"Cash"}],ewallet:[{text:"Cash",value:"Cash"}],investment:[{text:"Cash",value:"Cash"}],cash:[{text:"Cash",value:"Cash"}]},SGD:{bank:[{text:"Cash",value:"Cash"}],ewallet:[{text:"Cash",value:"Cash"}],investment:[{text:"Cash",value:"Cash"}],cash:[{text:"Cash",value:"Cash"}]}},R=i(()=>V),T=i(()=>B),$=i(()=>{var a;return!t.currency||!t.type?[]:((a=G[t.currency])==null?void 0:a[t.type])||[]}),N=i(()=>{const a=V.find(e=>e.value===t.type);return a?a.text:""}),U=i(()=>{const a=B.find(e=>e.value===t.currency);return a?a.text:""}),P=i(()=>t.name),b=i(()=>Y.params.id);Q(async()=>{await E()});const E=async()=>{x.value=!0;try{v.accounts.length===0&&await v.fetchAccounts();const a=v.accounts.find(e=>e.id===b.value);a?(t.name=a.name,t.type=a.type,t.currency=a.currency,t.initial_balance=a.initial_balance.toString(),t.description=a.description||""):(s({message:"账户不存在",type:"fail"}),h.back())}catch(a){console.error("加载账户失败:",a),s({message:"加载账户失败",type:"fail"})}finally{x.value=!1}},M=({selectedOptions:a})=>{var r;const e=((r=a[0])==null?void 0:r.value)||"";e!==t.type&&(t.type=e,t.name=""),d.value=!1},F=({selectedOptions:a})=>{var r;const e=((r=a[0])==null?void 0:r.value)||"MYR";e!==t.currency&&(t.currency=e,t.type="",t.name=""),p.value=!1},q=({selectedOptions:a})=>{var e;t.name=((e=a[0])==null?void 0:e.value)||"",C.value=!1},I=()=>t.name.trim()?t.type?t.currency?t.initial_balance===""||isNaN(parseFloat(t.initial_balance))?(s("请输入有效的初始余额"),!1):!0:(s("请选择货币"),!1):(s("请选择账户类型"),!1):(s("请输入账户名称"),!1),A=async()=>{if(I()){m.value=!0;try{const a={name:t.name.trim(),type:t.type,currency:t.currency,initial_balance:parseFloat(t.initial_balance),description:t.description.trim()};await v.updateAccount(b.value,a),s({message:"账户更新成功",type:"success"}),h.back()}catch(a){console.error("更新账户失败:",a),s({message:a.message||"更新账户失败",type:"fail"})}finally{m.value=!1}}},L=async()=>{m.value=!0;try{await v.deleteAccount(b.value),s({message:"账户删除成功",type:"success"}),h.replace("/accounts")}catch(a){console.error("删除账户失败:",a),s({message:a.message||"删除账户失败",type:"fail"})}finally{m.value=!1,y.value=!1}};return(a,e)=>{const r=o("van-button"),z=o("van-nav-bar"),J=o("van-loading"),f=o("van-field"),w=o("van-cell-group"),H=o("van-icon"),K=o("van-cell"),W=o("van-form"),_=o("van-picker"),k=o("van-popup"),X=o("van-dialog");return g(),D("div",le,[l(z,{title:"编辑账户","left-arrow":"",onClickLeft:e[0]||(e[0]=n=>a.$router.back()),fixed:"",placeholder:""},{right:u(()=>[l(r,{type:"primary",size:"small",loading:m.value,onClick:A},{default:u(()=>e[17]||(e[17]=[te(" 保存 ")])),_:1,__:[17]},8,["loading"])]),_:1}),S("div",oe,[x.value?(g(),D("div",se,[l(J,{size:"24px"}),e[18]||(e[18]=S("span",null,"加载中...",-1))])):(g(),ee(W,{key:1,onSubmit:A,class:"account-form"},{default:u(()=>[l(w,{inset:"",title:"基本信息"},{default:u(()=>[l(f,{modelValue:U.value,"onUpdate:modelValue":e[1]||(e[1]=n=>U.value=n),label:"货币",placeholder:"选择货币",readonly:"","is-link":"",onClick:e[2]||(e[2]=n=>p.value=!0),rules:[{required:!0,message:"请选择货币"}]},null,8,["modelValue"]),l(f,{modelValue:N.value,"onUpdate:modelValue":e[3]||(e[3]=n=>N.value=n),label:"账户类型",placeholder:"选择账户类型",readonly:"","is-link":"",onClick:e[4]||(e[4]=n=>d.value=!0),rules:[{required:!0,message:"请选择账户类型"}]},null,8,["modelValue"]),l(f,{modelValue:P.value,"onUpdate:modelValue":e[5]||(e[5]=n=>P.value=n),label:"账户名称",placeholder:"选择账户名称",readonly:"","is-link":"",onClick:e[6]||(e[6]=n=>C.value=!0),rules:[{required:!0,message:"请选择账户名称"}]},null,8,["modelValue"]),l(f,{modelValue:t.initial_balance,"onUpdate:modelValue":e[7]||(e[7]=n=>t.initial_balance=n),label:"初始余额",type:"number",placeholder:"0.00",rules:[{required:!0,message:"请输入初始余额"}]},null,8,["modelValue"])]),_:1}),l(w,{inset:"",title:"附加信息"},{default:u(()=>[l(f,{modelValue:t.description,"onUpdate:modelValue":e[8]||(e[8]=n=>t.description=n),label:"描述",type:"textarea",placeholder:"添加账户描述（可选）",rows:"3",autosize:"",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1}),l(w,{inset:"",title:"危险操作"},{default:u(()=>[l(K,{title:"删除账户","is-link":"",onClick:e[9]||(e[9]=n=>y.value=!0),"title-class":"delete-text"},{icon:u(()=>[l(H,{name:"delete-o",color:"#ee0a24"})]),_:1})]),_:1})]),_:1}))]),l(k,{show:d.value,"onUpdate:show":e[11]||(e[11]=n=>d.value=n),position:"bottom"},{default:u(()=>[l(_,{columns:R.value,title:"选择账户类型",onConfirm:M,onCancel:e[10]||(e[10]=n=>d.value=!1)},null,8,["columns"])]),_:1},8,["show"]),l(k,{show:p.value,"onUpdate:show":e[13]||(e[13]=n=>p.value=n),position:"bottom"},{default:u(()=>[l(_,{columns:T.value,title:"选择货币",onConfirm:F,onCancel:e[12]||(e[12]=n=>p.value=!1)},null,8,["columns"])]),_:1},8,["show"]),l(k,{show:C.value,"onUpdate:show":e[15]||(e[15]=n=>C.value=n),position:"bottom"},{default:u(()=>[l(_,{columns:$.value,title:"选择账户名称",onConfirm:q,onCancel:e[14]||(e[14]=n=>C.value=!1)},null,8,["columns"])]),_:1},8,["show"]),l(X,{show:y.value,"onUpdate:show":e[16]||(e[16]=n=>y.value=n),title:"确认删除",message:"删除账户后，相关交易记录将转移到默认账户。此操作不可撤销，确定要删除吗？","show-cancel-button":"",onConfirm:L},null,8,["show"])])}}},me=ne(ue,[["__scopeId","data-v-c9604b7c"]]);export{me as default};
