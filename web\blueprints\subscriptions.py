"""
订阅管理API蓝图
"""
import uuid
import sqlite3
import sys
import os
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, session
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.database_config import MAIN_DB_PATH

logger = logging.getLogger(__name__)

bp = Blueprint('subscriptions', __name__)

def get_db_connection():
    """获取数据库连接"""
    conn = sqlite3.connect(MAIN_DB_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def login_required(f):
    """登录验证装饰器"""
    from functools import wraps

    @wraps(f)
    def decorated(*args, **kwargs):
        if not session.get('logged_in') or not session.get('user_id'):
            logger.warning(f"未登录用户访问API: {request.path}")
            return jsonify({'success': False, 'error': '请先登录'}), 401
        return f(*args, **kwargs)
    return decorated

def get_current_user_id():
    """获取当前登录用户ID"""
    return session.get('user_id')

def calculate_next_billing_date(start_date, billing_cycle):
    """计算下次账单日期"""
    try:
        start = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        
        if billing_cycle == 'monthly':
            # 每月
            if start.month == 12:
                next_date = start.replace(year=start.year + 1, month=1)
            else:
                next_date = start.replace(month=start.month + 1)
        elif billing_cycle == 'quarterly':
            # 每季度（3个月）
            next_date = start + timedelta(days=90)
        elif billing_cycle == 'yearly':
            # 每年
            next_date = start.replace(year=start.year + 1)
        elif billing_cycle == 'weekly':
            # 每周
            next_date = start + timedelta(weeks=1)
        else:
            # 默认每月
            next_date = start + timedelta(days=30)
            
        return next_date.isoformat()
    except Exception as e:
        logger.error(f"计算下次账单日期失败: {e}")
        # 默认返回一个月后
        return (datetime.now() + timedelta(days=30)).isoformat()

@bp.route('/', methods=['GET'])
@login_required
def get_subscriptions():
    """获取所有订阅"""
    try:
        user_id = get_current_user_id()
        logger.info(f"用户 {user_id} 获取所有订阅")
        
        conn = get_db_connection()
        subscriptions = conn.execute('''
            SELECT * FROM subscriptions 
            WHERE user_id = ? 
            ORDER BY next_billing_date ASC
        ''', (user_id,)).fetchall()
        
        result = []
        for sub in subscriptions:
            sub_dict = dict(sub)
            # 计算距离下次账单的天数
            try:
                next_billing = datetime.fromisoformat(sub_dict['next_billing_date'].replace('Z', '+00:00'))
                days_until = (next_billing - datetime.now()).days
                sub_dict['days_until_billing'] = days_until
                sub_dict['is_due_soon'] = days_until <= 7  # 7天内到期
            except:
                sub_dict['days_until_billing'] = 0
                sub_dict['is_due_soon'] = False
            
            result.append(sub_dict)
        
        return jsonify({
            'success': True,
            'subscriptions': result
        })
        
    except Exception as e:
        logger.error(f"获取订阅列表失败: {e}")
        return jsonify({'success': False, 'error': '获取订阅列表失败'}), 500

@bp.route('/<subscription_id>', methods=['GET'])
@login_required
def get_subscription(subscription_id):
    """获取单个订阅详情"""
    try:
        user_id = get_current_user_id()
        logger.info(f"用户 {user_id} 获取订阅详情: {subscription_id}")
        
        conn = get_db_connection()
        subscription = conn.execute('''
            SELECT * FROM subscriptions 
            WHERE id = ? AND user_id = ?
        ''', (subscription_id, user_id)).fetchone()
        
        if not subscription:
            return jsonify({'success': False, 'error': '订阅不存在'}), 404
        
        sub_dict = dict(subscription)
        
        # 计算距离下次账单的天数
        try:
            next_billing = datetime.fromisoformat(sub_dict['next_billing_date'].replace('Z', '+00:00'))
            days_until = (next_billing - datetime.now()).days
            sub_dict['days_until_billing'] = days_until
            sub_dict['is_due_soon'] = days_until <= 7
        except:
            sub_dict['days_until_billing'] = 0
            sub_dict['is_due_soon'] = False
        
        return jsonify({
            'success': True,
            'subscription': sub_dict
        })
        
    except Exception as e:
        logger.error(f"获取订阅详情失败: {e}")
        return jsonify({'success': False, 'error': '获取订阅详情失败'}), 500

@bp.route('/', methods=['POST'])
@login_required
def create_subscription():
    """创建新订阅"""
    try:
        user_id = get_current_user_id()
        data = request.json
        logger.info(f"用户 {user_id} 创建订阅: {data.get('name', '未知')}")
        
        # 验证必填字段
        required_fields = ['name', 'service_type', 'amount', 'currency', 'billing_cycle', 'start_date']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'success': False, 'error': f'缺少必填字段: {field}'}), 400
        
        # 生成ID和时间戳
        subscription_id = str(uuid.uuid4())
        now = datetime.now().isoformat()
        
        # 计算下次账单日期
        next_billing_date = calculate_next_billing_date(data['start_date'], data['billing_cycle'])
        
        conn = get_db_connection()
        conn.execute('''
            INSERT INTO subscriptions (
                id, user_id, name, service_type, amount, currency, 
                billing_cycle, start_date, next_billing_date, 
                description, icon, is_active, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            subscription_id, user_id, data['name'], data['service_type'],
            data['amount'], data['currency'], data['billing_cycle'],
            data['start_date'], next_billing_date,
            data.get('description', ''), data.get('icon', ''),
            data.get('is_active', True), now, now
        ))
        conn.commit()
        
        return jsonify({
            'success': True,
            'message': '订阅创建成功',
            'subscription_id': subscription_id
        })
        
    except Exception as e:
        logger.error(f"创建订阅失败: {e}")
        return jsonify({'success': False, 'error': '创建订阅失败'}), 500

@bp.route('/<subscription_id>', methods=['PUT'])
@login_required
def update_subscription(subscription_id):
    """更新订阅"""
    try:
        user_id = get_current_user_id()
        data = request.json
        logger.info(f"用户 {user_id} 更新订阅: {subscription_id}")
        
        conn = get_db_connection()
        
        # 检查订阅是否存在
        existing = conn.execute('''
            SELECT * FROM subscriptions 
            WHERE id = ? AND user_id = ?
        ''', (subscription_id, user_id)).fetchone()
        
        if not existing:
            return jsonify({'success': False, 'error': '订阅不存在'}), 404
        
        # 构建更新字段
        update_fields = []
        update_values = []
        
        allowed_fields = ['name', 'service_type', 'amount', 'currency', 'billing_cycle', 
                         'start_date', 'description', 'icon', 'is_active']
        
        for field in allowed_fields:
            if field in data:
                update_fields.append(f"{field} = ?")
                update_values.append(data[field])
        
        # 如果更新了账单周期或开始日期，重新计算下次账单日期
        if 'billing_cycle' in data or 'start_date' in data:
            start_date = data.get('start_date', existing['start_date'])
            billing_cycle = data.get('billing_cycle', existing['billing_cycle'])
            next_billing_date = calculate_next_billing_date(start_date, billing_cycle)
            update_fields.append("next_billing_date = ?")
            update_values.append(next_billing_date)
        
        if not update_fields:
            return jsonify({'success': False, 'error': '没有要更新的字段'}), 400
        
        # 添加更新时间
        update_fields.append("updated_at = ?")
        update_values.append(datetime.now().isoformat())
        
        # 添加WHERE条件的值
        update_values.extend([subscription_id, user_id])
        
        # 执行更新
        sql = f"UPDATE subscriptions SET {', '.join(update_fields)} WHERE id = ? AND user_id = ?"
        conn.execute(sql, update_values)
        conn.commit()
        
        return jsonify({
            'success': True,
            'message': '订阅更新成功'
        })
        
    except Exception as e:
        logger.error(f"更新订阅失败: {e}")
        return jsonify({'success': False, 'error': '更新订阅失败'}), 500

@bp.route('/<subscription_id>', methods=['DELETE'])
@login_required
def delete_subscription(subscription_id):
    """删除订阅"""
    try:
        user_id = get_current_user_id()
        logger.info(f"用户 {user_id} 删除订阅: {subscription_id}")
        
        conn = get_db_connection()
        
        # 检查订阅是否存在
        existing = conn.execute('''
            SELECT * FROM subscriptions 
            WHERE id = ? AND user_id = ?
        ''', (subscription_id, user_id)).fetchone()
        
        if not existing:
            return jsonify({'success': False, 'error': '订阅不存在'}), 404
        
        # 删除相关的提醒记录
        conn.execute('''
            DELETE FROM subscription_reminders 
            WHERE subscription_id = ? AND user_id = ?
        ''', (subscription_id, user_id))
        
        # 删除订阅
        conn.execute('''
            DELETE FROM subscriptions 
            WHERE id = ? AND user_id = ?
        ''', (subscription_id, user_id))
        
        conn.commit()
        
        return jsonify({
            'success': True,
            'message': '订阅删除成功'
        })

    except Exception as e:
        logger.error(f"删除订阅失败: {e}")
        return jsonify({'success': False, 'error': '删除订阅失败'}), 500

@bp.route('/stats', methods=['GET'])
@login_required
def get_subscription_stats():
    """获取订阅统计信息"""
    try:
        user_id = get_current_user_id()
        logger.info(f"用户 {user_id} 获取订阅统计")

        conn = get_db_connection()

        # 获取所有活跃订阅
        subscriptions = conn.execute('''
            SELECT * FROM subscriptions
            WHERE user_id = ? AND is_active = 1
        ''', (user_id,)).fetchall()

        # 计算统计数据
        total_subscriptions = len(subscriptions)
        monthly_cost = 0
        yearly_cost = 0
        due_soon_count = 0

        service_types = {}

        for sub in subscriptions:
            amount = sub['amount']
            cycle = sub['billing_cycle']
            service_type = sub['service_type']

            # 统计服务类型
            if service_type not in service_types:
                service_types[service_type] = {'count': 0, 'cost': 0}
            service_types[service_type]['count'] += 1

            # 转换为月费用
            if cycle == 'monthly':
                monthly_amount = amount
                service_types[service_type]['cost'] += amount
            elif cycle == 'quarterly':
                monthly_amount = amount / 3
                service_types[service_type]['cost'] += amount / 3
            elif cycle == 'yearly':
                monthly_amount = amount / 12
                service_types[service_type]['cost'] += amount / 12
            elif cycle == 'weekly':
                monthly_amount = amount * 4.33  # 平均每月4.33周
                service_types[service_type]['cost'] += amount * 4.33
            else:
                monthly_amount = amount
                service_types[service_type]['cost'] += amount

            monthly_cost += monthly_amount

            # 检查是否即将到期
            try:
                next_billing = datetime.fromisoformat(sub['next_billing_date'].replace('Z', '+00:00'))
                days_until = (next_billing - datetime.now()).days
                if days_until <= 7:
                    due_soon_count += 1
            except:
                pass

        yearly_cost = monthly_cost * 12

        return jsonify({
            'success': True,
            'stats': {
                'total_subscriptions': total_subscriptions,
                'monthly_cost': round(monthly_cost, 2),
                'yearly_cost': round(yearly_cost, 2),
                'due_soon_count': due_soon_count,
                'service_types': service_types
            }
        })

    except Exception as e:
        logger.error(f"获取订阅统计失败: {e}")
        return jsonify({'success': False, 'error': '获取订阅统计失败'}), 500

@bp.route('/due-soon', methods=['GET'])
@login_required
def get_due_soon_subscriptions():
    """获取即将到期的订阅"""
    try:
        user_id = get_current_user_id()
        days = request.args.get('days', 7, type=int)  # 默认7天内
        logger.info(f"用户 {user_id} 获取{days}天内到期的订阅")

        conn = get_db_connection()
        subscriptions = conn.execute('''
            SELECT * FROM subscriptions
            WHERE user_id = ? AND is_active = 1
            ORDER BY next_billing_date ASC
        ''', (user_id,)).fetchall()

        due_soon = []
        now = datetime.now()

        for sub in subscriptions:
            try:
                next_billing = datetime.fromisoformat(sub['next_billing_date'].replace('Z', '+00:00'))
                days_until = (next_billing - now).days

                if days_until <= days:
                    sub_dict = dict(sub)
                    sub_dict['days_until_billing'] = days_until
                    sub_dict['is_overdue'] = days_until < 0
                    due_soon.append(sub_dict)
            except:
                continue

        return jsonify({
            'success': True,
            'subscriptions': due_soon
        })

    except Exception as e:
        logger.error(f"获取即将到期订阅失败: {e}")
        return jsonify({'success': False, 'error': '获取即将到期订阅失败'}), 500

@bp.route('/<subscription_id>/renew', methods=['POST'])
@login_required
def renew_subscription(subscription_id):
    """续费订阅（更新下次账单日期）"""
    try:
        user_id = get_current_user_id()
        logger.info(f"用户 {user_id} 续费订阅: {subscription_id}")

        conn = get_db_connection()

        # 获取订阅信息
        subscription = conn.execute('''
            SELECT * FROM subscriptions
            WHERE id = ? AND user_id = ?
        ''', (subscription_id, user_id)).fetchone()

        if not subscription:
            return jsonify({'success': False, 'error': '订阅不存在'}), 404

        # 计算新的下次账单日期
        current_next_billing = subscription['next_billing_date']
        billing_cycle = subscription['billing_cycle']

        # 从当前的下次账单日期开始计算
        new_next_billing = calculate_next_billing_date(current_next_billing, billing_cycle)

        # 更新下次账单日期
        conn.execute('''
            UPDATE subscriptions
            SET next_billing_date = ?, updated_at = ?
            WHERE id = ? AND user_id = ?
        ''', (new_next_billing, datetime.now().isoformat(), subscription_id, user_id))

        conn.commit()

        return jsonify({
            'success': True,
            'message': '订阅续费成功',
            'next_billing_date': new_next_billing
        })

    except Exception as e:
        logger.error(f"续费订阅失败: {e}")
        return jsonify({'success': False, 'error': '续费订阅失败'}), 500
