#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
预算检查器
检查预算使用情况并发送通知
"""

import requests
import logging
import sqlite3
from datetime import datetime, timedelta
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from bot.config.bot_config import config

logger = logging.getLogger(__name__)

class BudgetChecker:
    """预算检查器类"""

    def __init__(self):
        self.api_base_url = config.WEB_API_BASE_URL
        self.api_token = config.WEB_API_TOKEN
        self.warning_threshold = config.BUDGET_WARNING_THRESHOLD
        self.danger_threshold = config.BUDGET_DANGER_THRESHOLD

    def check_all_budgets(self):
        """
        检查所有预算

        Returns:
            list: 需要通知的预算列表
        """
        notifications = []

        try:
            # 获取所有预算
            budgets = self._get_budgets()
            if not budgets:
                return notifications

            # 获取所有交易
            transactions = self._get_transactions()
            if transactions is None:
                return notifications

            # 检查每个预算
            for budget in budgets:
                notification = self._check_budget(budget, transactions)
                if notification:
                    notifications.append(notification)

        except Exception as e:
            logger.error(f"检查预算时出错: {str(e)}")

        return notifications

    def check_budget_after_transaction(self, transaction_data):
        """
        在添加交易后检查相关预算（优化版本，减少API调用）

        Args:
            transaction_data (dict): 交易数据

        Returns:
            dict: 通知信息，如果不需要通知则返回None
        """
        try:
            # 只检查支出交易
            if transaction_data.get('type') != 'expense':
                return None

            category = transaction_data.get('category')
            if not category:
                return None

            # 使用本地数据库连接直接查询，避免HTTP请求
            notification = self._check_budget_direct(transaction_data)
            return notification

        except Exception as e:
            logger.error(f"检查交易后预算时出错: {str(e)}")

        return None

    def _check_budget_direct(self, transaction_data):
        """
        直接通过数据库查询检查预算（避免HTTP请求）

        Args:
            transaction_data (dict): 交易数据

        Returns:
            dict: 通知信息，如果不需要通知则返回None
        """
        try:
            # 确定正确的数据库路径
            # __file__ 是 bot/utils/budget_checker.py
            # 需要回到项目根目录，然后进入data目录: ../../data/finance.db
            current_dir = os.path.dirname(__file__)  # bot/utils
            bot_dir = os.path.dirname(current_dir)   # bot
            project_dir = os.path.dirname(bot_dir)   # 项目根目录
            db_path = os.path.join(project_dir, 'data', 'finance.db')
            logger.info(f"预算检查 - 尝试连接数据库: {db_path}")

            if not os.path.exists(db_path):
                logger.error(f"数据库文件不存在: {db_path}")
                return None

            # 连接数据库
            conn = sqlite3.connect(db_path)
            conn.row_factory = sqlite3.Row

            # 获取该项目的预算 - 改为按项目名称查找
            project_name = transaction_data.get('description', '')
            if not project_name:
                conn.close()
                return None

            budgets = conn.execute(
                'SELECT * FROM budgets WHERE project_name = ?',
                (project_name,)
            ).fetchall()

            if not budgets:
                conn.close()
                return None

            # 优化查询：只获取相关时间范围内的交易
            # 先获取预算的时间范围
            budget_dict = dict(budgets[0]) if budgets else None
            if not budget_dict:
                conn.close()
                return None

            # 获取该项目在预算时间范围内的交易 - 精确匹配交易描述和预算项目名称
            transactions = conn.execute('''
                SELECT * FROM transactions
                WHERE type = "expense"
                AND description = ?
                AND date >= ?
                AND date <= ?
                ORDER BY date DESC
            ''', (budget_dict['project_name'], budget_dict['start_date'], budget_dict['end_date'])).fetchall()

            conn.close()

            # 转换为字典格式
            budget_dicts = [dict(budget) for budget in budgets]
            transaction_dicts = [dict(tx) for tx in transactions]

            # 检查每个预算
            for budget in budget_dicts:
                notification = self._check_budget(budget, transaction_dicts)
                if notification:
                    return notification

        except Exception as e:
            logger.error(f"直接检查预算时出错: {str(e)}")

        return None

    def _get_budgets(self):
        """获取所有预算"""
        try:
            headers = {}
            if self.api_token:
                headers['X-Auth-Token'] = self.api_token

            response = requests.get(f"{self.api_base_url}/budgets", headers=headers)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"获取预算失败: {str(e)}")
            return []

    def _get_budgets_by_category(self, category):
        """根据类别获取预算"""
        budgets = self._get_budgets()
        return [b for b in budgets if b.get('category') == category]

    def _get_transactions(self):
        """获取所有交易"""
        try:
            headers = {}
            if self.api_token:
                headers['X-Auth-Token'] = self.api_token

            response = requests.get(f"{self.api_base_url}/transactions", headers=headers)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"获取交易失败: {str(e)}")
            return None

    def _check_budget(self, budget, transactions):
        """
        检查单个预算

        Args:
            budget (dict): 预算信息
            transactions (list): 交易列表

        Returns:
            dict: 通知信息，如果不需要通知则返回None
        """
        try:
            # 计算预算使用情况
            usage = self._calculate_budget_usage(budget, transactions)

            # 判断是否需要通知
            if usage['percentage'] >= self.danger_threshold:
                return {
                    'type': 'danger',
                    'budget': budget,
                    'usage': usage,
                    'message': self._format_danger_message(budget, usage)
                }
            elif usage['percentage'] >= self.warning_threshold:
                return {
                    'type': 'warning',
                    'budget': budget,
                    'usage': usage,
                    'message': self._format_warning_message(budget, usage)
                }

        except Exception as e:
            logger.error(f"检查预算 {budget.get('id')} 时出错: {str(e)}")

        return None

    def _calculate_budget_usage(self, budget, transactions):
        """计算预算使用情况"""
        start_date = datetime.fromisoformat(budget['start_date'])
        end_date = datetime.fromisoformat(budget['end_date'])
        category = budget['category']
        budget_amount = float(budget['amount'])

        # 筛选相关交易
        relevant_transactions = []
        for tx in transactions:
            tx_date = datetime.fromisoformat(tx['date'])
            if (tx['type'] == 'expense' and
                tx['category'] == category and
                start_date <= tx_date <= end_date):
                relevant_transactions.append(tx)

        # 计算总支出
        total_spent = sum(float(tx['amount']) for tx in relevant_transactions)

        # 计算使用百分比
        percentage = total_spent / budget_amount if budget_amount > 0 else 0

        return {
            'total_spent': total_spent,
            'budget_amount': budget_amount,
            'remaining': budget_amount - total_spent,
            'percentage': percentage,
            'transaction_count': len(relevant_transactions)
        }

    def _format_warning_message(self, budget, usage):
        """格式化预警消息"""
        percentage = int(usage['percentage'] * 100)
        return f"""
⚠️ 预算预警通知

📊 预算类别：{budget['category']}
💰 预算金额：{budget['amount']} {budget['currency']}
💸 已使用：{usage['total_spent']:.2f} {budget['currency']}
📈 使用比例：{percentage}%
💳 剩余额度：{usage['remaining']:.2f} {budget['currency']}

⏰ 预算周期：{budget['start_date']} 至 {budget['end_date']}

💡 提醒：您的{budget['category']}支出已达到预算的{percentage}%，请注意控制消费。
        """.strip()

    def _format_danger_message(self, budget, usage):
        """格式化危险消息"""
        percentage = int(usage['percentage'] * 100)
        over_amount = usage['total_spent'] - usage['budget_amount']

        if over_amount > 0:
            return f"""
🚨 预算超支警告

📊 预算类别：{budget['category']}
💰 预算金额：{budget['amount']} {budget['currency']}
💸 已使用：{usage['total_spent']:.2f} {budget['currency']}
📈 使用比例：{percentage}%
⚠️ 超支金额：{over_amount:.2f} {budget['currency']}

⏰ 预算周期：{budget['start_date']} 至 {budget['end_date']}

🚨 警告：您的{budget['category']}支出已超出预算{over_amount:.2f} {budget['currency']}，请立即控制支出！
            """.strip()
        else:
            return f"""
🚨 预算即将超支

📊 预算类别：{budget['category']}
💰 预算金额：{budget['amount']} {budget['currency']}
💸 已使用：{usage['total_spent']:.2f} {budget['currency']}
📈 使用比例：{percentage}%
💳 剩余额度：{usage['remaining']:.2f} {budget['currency']}

⏰ 预算周期：{budget['start_date']} 至 {budget['end_date']}

🚨 警告：您的{budget['category']}支出已达到预算上限，请控制支出！
            """.strip()

# 创建预算检查器实例
budget_checker = BudgetChecker()
