import{K as s,J as a}from"./index--MNqwREY.js";const r={getAccounts(){return s.get("/accounts/")},getAccount(t){return s.get(`/accounts/${t}`)},createAccount(t){return s.post("/accounts/",t)},updateAccount(t,c){return s.put(`/accounts/${t}`,c)},deleteAccount(t){return s.delete(`/accounts/${t}`)},getAccountBalance(t){return s.get(`/accounts/${t}/balance`)},getAllBalances(){return s.get("/accounts/balances")}},o=a("accounts",{state:()=>({accounts:[],loading:!1}),getters:{totalAssets:t=>t.accounts.reduce((c,e)=>c+(e.current_balance||e.initial_balance||0),0),accountsByType:t=>{const c={};return t.accounts.forEach(e=>{c[e.type]||(c[e.type]=[]),c[e.type].push(e)}),c},cashAssets:t=>t.accounts.filter(c=>c.type==="cash").reduce((c,e)=>c+(e.current_balance||e.initial_balance||0),0),bankAssets:t=>t.accounts.filter(c=>c.type==="bank").reduce((c,e)=>c+(e.current_balance||e.initial_balance||0),0)},actions:{async fetchAccounts(){this.loading=!0;try{const t=await r.getAccounts();return this.accounts=Array.isArray(t)?t:[],this.accounts}catch(t){throw console.error("获取账户列表失败:",t),this.accounts=[],t}finally{this.loading=!1}},async fetchAccountsWithBalances(){this.loading=!0;try{const t=await r.getAllBalances();return this.accounts=Array.isArray(t.accounts)?t.accounts:[],this.accounts}catch(t){throw console.error("获取账户余额失败:",t),this.accounts=[],t}finally{this.loading=!1}},async createAccount(t){try{const c=await r.createAccount(t);return c&&c.success&&await this.fetchAccountsWithBalances(),c}catch(c){throw console.error("创建账户失败:",c),c}},async updateAccount(t,c){try{const e=await r.updateAccount(t,c);return e&&e.success&&await this.fetchAccountsWithBalances(),e}catch(e){throw console.error("更新账户失败:",e),e}},async deleteAccount(t){try{const c=await r.deleteAccount(t);return c&&c.success&&await this.fetchAccountsWithBalances(),c}catch(c){throw console.error("删除账户失败:",c),c}}}});export{o as useAccountsStore};
