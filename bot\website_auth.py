#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
网站用户验证模块
用于验证网站用户名和获取用户信息
"""

import sqlite3
import sys
import os
import logging
from typing import Optional, Dict

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.database_config import MAIN_DB_PATH

logger = logging.getLogger(__name__)

class WebsiteUserAuth:
    """网站用户验证类"""

    def __init__(self, db_path: str = None):
        if db_path is None:
            db_path = MAIN_DB_PATH
        self.db_path = db_path
    
    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def verify_username(self, username: str) -> Optional[Dict]:
        """验证用户名是否存在并返回用户信息"""
        conn = self.get_connection()
        try:
            user = conn.execute('''
                SELECT id, username, email, display_name, is_active
                FROM users 
                WHERE username = ? AND is_active = 1
            ''', (username,)).fetchone()
            
            if user:
                return {
                    'id': user['id'],
                    'username': user['username'],
                    'email': user['email'],
                    'display_name': user['display_name'],
                    'is_active': user['is_active']
                }
            return None
            
        except Exception as e:
            logger.error(f"验证用户名失败: {e}")
            return None
        finally:
            conn.close()
    
    def get_user_by_id(self, user_id: str) -> Optional[Dict]:
        """根据用户ID获取用户信息"""
        conn = self.get_connection()
        try:
            user = conn.execute('''
                SELECT id, username, email, display_name, is_active
                FROM users 
                WHERE id = ? AND is_active = 1
            ''', (user_id,)).fetchone()
            
            if user:
                return {
                    'id': user['id'],
                    'username': user['username'],
                    'email': user['email'],
                    'display_name': user['display_name'],
                    'is_active': user['is_active']
                }
            return None
            
        except Exception as e:
            logger.error(f"获取用户信息失败: {e}")
            return None
        finally:
            conn.close()
    
    def is_admin_user(self, user_id: str) -> bool:
        """检查用户是否为管理员（这里可以根据需要扩展管理员逻辑）"""
        # 目前简单实现：检查是否为第一个注册的用户或特定用户
        conn = self.get_connection()
        try:
            # 获取第一个注册的用户（通常是管理员）
            first_user = conn.execute('''
                SELECT id FROM users 
                WHERE is_active = 1 
                ORDER BY created_at ASC 
                LIMIT 1
            ''').fetchone()
            
            return first_user and first_user['id'] == user_id
            
        except Exception as e:
            logger.error(f"检查管理员权限失败: {e}")
            return False
        finally:
            conn.close()

# 全局实例
website_auth = WebsiteUserAuth()
