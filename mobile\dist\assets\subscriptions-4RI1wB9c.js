import{K as e,J as n}from"./index--MNqwREY.js";const o={getSubscriptions(){return e.get("/subscriptions/")},getSubscription(s){return e.get(`/subscriptions/${s}`)},createSubscription(s){return e.post("/subscriptions/",s)},updateSubscription(s,r){return e.put(`/subscriptions/${s}`,r)},deleteSubscription(s){return e.delete(`/subscriptions/${s}`)},getStats(){return e.get("/subscriptions/stats")},getDueSoon(s=7){return e.get("/subscriptions/due-soon",{params:{days:s}})},renewSubscription(s){return e.post(`/subscriptions/${s}/renew`)}},a=n("subscriptions",{state:()=>({subscriptions:[],stats:{total_subscriptions:0,monthly_cost:0,yearly_cost:0,due_soon_count:0,service_types:{}},dueSoonSubscriptions:[],loading:!1,error:null}),getters:{activeSubscriptions:s=>s.subscriptions.filter(r=>r.is_active),dueSoonCount:s=>s.subscriptions.filter(r=>r.is_due_soon).length,subscriptionsByType:s=>{const r={};return s.subscriptions.forEach(t=>{r[t.service_type]||(r[t.service_type]=[]),r[t.service_type].push(t)}),r},totalMonthlyCost:s=>s.subscriptions.reduce((r,t)=>{if(!t.is_active)return r;let i=0;switch(t.billing_cycle){case"monthly":i=t.amount;break;case"quarterly":i=t.amount/3;break;case"yearly":i=t.amount/12;break;case"weekly":i=t.amount*4.33;break;default:i=t.amount}return r+i},0)},actions:{async fetchSubscriptions(){this.loading=!0,this.error=null;try{const s=await o.getSubscriptions();if(s.success)this.subscriptions=s.subscriptions||[];else throw new Error(s.error||"获取订阅列表失败")}catch(s){throw this.error=s.message,console.error("获取订阅列表失败:",s),s}finally{this.loading=!1}},async fetchStats(){try{const s=await o.getStats();s.success&&(this.stats=s.stats)}catch(s){console.error("获取订阅统计失败:",s)}},async fetchDueSoon(s=7){try{const r=await o.getDueSoon(s);r.success&&(this.dueSoonSubscriptions=r.subscriptions||[])}catch(r){console.error("获取即将到期订阅失败:",r)}},async createSubscription(s){this.loading=!0,this.error=null;try{const r=await o.createSubscription(s);if(r.success)return await this.fetchSubscriptions(),await this.fetchStats(),r;throw new Error(r.error||"创建订阅失败")}catch(r){throw this.error=r.message,console.error("创建订阅失败:",r),r}finally{this.loading=!1}},async updateSubscription(s,r){this.loading=!0,this.error=null;try{const t=await o.updateSubscription(s,r);if(t.success)return await this.fetchSubscriptions(),await this.fetchStats(),t;throw new Error(t.error||"更新订阅失败")}catch(t){throw this.error=t.message,console.error("更新订阅失败:",t),t}finally{this.loading=!1}},async deleteSubscription(s){this.loading=!0,this.error=null;try{const r=await o.deleteSubscription(s);if(r.success)return await this.fetchSubscriptions(),await this.fetchStats(),r;throw new Error(r.error||"删除订阅失败")}catch(r){throw this.error=r.message,console.error("删除订阅失败:",r),r}finally{this.loading=!1}},async renewSubscription(s){try{const r=await o.renewSubscription(s);if(r.success)return await this.fetchSubscriptions(),await this.fetchStats(),r;throw new Error(r.error||"续费失败")}catch(r){throw console.error("续费订阅失败:",r),r}},async getSubscription(s){try{const r=await o.getSubscription(s);if(r.success)return r.subscription;throw new Error(r.error||"获取订阅详情失败")}catch(r){throw console.error("获取订阅详情失败:",r),r}},clearError(){this.error=null},reset(){this.subscriptions=[],this.stats={total_subscriptions:0,monthly_cost:0,yearly_cost:0,due_soon_count:0,service_types:{}},this.dueSoonSubscriptions=[],this.loading=!1,this.error=null}}});export{a as u};
