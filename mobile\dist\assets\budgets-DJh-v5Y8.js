import{K as n,J as l}from"./index--MNqwREY.js";import{t as h}from"./transactions-CrdnwW_k.js";const u={getBudgets(){return n.get("/budgets/")},getBudget(e){return n.get(`/budgets/${e}`)},createBudget(e){return n.post("/budgets/",e)},updateBudget(e,t){return n.put(`/budgets/${e}`,t)},deleteBudget(e){return n.delete(`/budgets/${e}`)},getBudgetStats(e={}){return n.get("/budgets/stats",{params:e})},checkBudgetStatus(e){return n.get(`/budgets/check/${e}`)}},p=l("budgets",{state:()=>({budgets:[],loading:!1,currentBudget:null}),getters:{budgetsByStatus:e=>{const t={normal:[],warning:[],danger:[],exceeded:[]};return e.budgets.forEach(r=>{const s=r.status||"normal";t[s]?t[s].push(r):t.normal.push(r)}),t},budgetsByCategory:e=>{const t={};return e.budgets.forEach(r=>{t[r.category]||(t[r.category]=[]),t[r.category].push(r)}),t},activeBudgets:e=>{const t=new Date().toISOString().split("T")[0];return e.budgets.filter(r=>r.start_date<=t&&r.end_date>=t)},totalBudgetAmount:e=>e.budgets.reduce((t,r)=>t+(r.amount||0),0),totalUsedAmount:e=>e.budgets.reduce((t,r)=>t+(r.used_amount||0),0),totalUsagePercentage:e=>{const t=e.budgets.reduce((s,a)=>s+(a.amount||0),0),r=e.budgets.reduce((s,a)=>s+(a.used_amount||0),0);return t>0?r/t*100:0},warningBudgets:e=>e.budgets.filter(t=>{const r=t.amount>0?t.used_amount/t.amount*100:0;return r>=80&&r<100}),exceededBudgets:e=>e.budgets.filter(t=>(t.amount>0?t.used_amount/t.amount*100:0)>=100)},actions:{async fetchBudgets(){this.loading=!0;try{const e=await u.getBudgets(),t=Array.isArray(e)?e:e.data||[],r=await Promise.all(t.map(async s=>{try{const a=await this.calculateBudgetUsage(s);return{...s,...a}}catch(a){return console.error(`计算预算 ${s.id} 使用情况失败:`,a),{...s,used_amount:0,remaining_amount:s.amount,usage_percentage:0,status:"normal"}}}));return this.budgets=r,this.budgets}catch(e){throw console.error("获取预算列表失败:",e),this.budgets=[],e}finally{this.loading=!1}},async fetchBudget(e){try{const t=await u.getBudget(e),r=await this.calculateBudgetUsage(t);return this.currentBudget={...t,...r},this.currentBudget}catch(t){throw console.error("获取预算详情失败:",t),t}},async createBudget(e){try{const t=await u.createBudget(e);return t&&t.success&&await this.fetchBudgets(),t}catch(t){throw console.error("创建预算失败:",t),t}},async updateBudget(e,t){try{const r=await u.updateBudget(e,t);return r&&r.success&&await this.fetchBudgets(),r}catch(r){throw console.error("更新预算失败:",r),r}},async deleteBudget(e){try{const t=await u.deleteBudget(e);return t&&t.success&&await this.fetchBudgets(),t}catch(t){throw console.error("删除预算失败:",t),t}},async calculateBudgetUsage(e){try{const t=await h.getTransactions({type:"expense",start_date:e.start_date,end_date:e.end_date}),s=(Array.isArray(t)?t:t.data||[]).filter(g=>g.description===e.project_name),a=s.reduce((g,i)=>g+(i.amount||0),0),d=e.amount-a,o=e.amount>0?a/e.amount*100:0;let c="normal";return o>=100?c="exceeded":o>=90?c="danger":o>=80&&(c="warning"),{used_amount:a,remaining_amount:d,usage_percentage:o,status:c,transaction_count:s.length}}catch(t){return console.error("计算预算使用情况失败:",t),{used_amount:0,remaining_amount:e.amount,usage_percentage:0,status:"normal",transaction_count:0}}},async checkBudgetStatus(e){try{return await u.checkBudgetStatus(e)}catch(t){throw console.error("检查预算状态失败:",t),t}},clearCurrentBudget(){this.currentBudget=null}}});export{p as useBudgetsStore};
