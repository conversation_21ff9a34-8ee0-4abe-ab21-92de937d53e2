import api from './index'

export const authAPI = {
  // 用户登录
  login(credentials) {
    return api.post('/auth/login', credentials)
  },

  // 用户注册
  register(userData) {
    return api.post('/auth/register', userData)
  },

  // 检查登录状态
  check() {
    return api.get('/auth/check', { showLoading: false })
  },

  // 获取用户信息
  getProfile() {
    return api.get('/auth/profile')
  },

  // 用户登出
  logout() {
    return api.post('/auth/logout')
  }
}
