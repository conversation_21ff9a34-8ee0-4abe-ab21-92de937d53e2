/*******************************************
 *          设置页面样式
 *******************************************/

/* 设置容器 */
.settings-container {
  padding: var(--spacing-lg);
  background-color: var(--color-background);
}

/* 设置标题 */
.settings-header {
  margin-bottom: var(--spacing-lg);
}

.settings-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-text);
}

/* 设置部分 */
.settings-section {
  background-color: var(--color-card);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.settings-section h3 {
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-md);
  color: var(--color-text);
}

/* 语言选择器 */
.language-selector {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.language-button {
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s;
}

.language-button.active {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.language-button:hover {
  background-color: var(--color-hover);
}

/* 存储信息 */
.storage-info {
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-sm);
  color: var(--color-text);
}

/* 设置按钮 */
.settings-button {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s;
  font-size: var(--font-size-sm);
  color: var(--color-text);
  margin-bottom: var(--spacing-md);
}

.settings-button:hover {
  background-color: var(--color-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.settings-button .settings-icon {
  font-size: var(--font-size-md);
  margin-right: var(--spacing-sm);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .language-selector {
    flex-direction: column;
  }
}
