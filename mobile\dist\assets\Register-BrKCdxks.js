import{u as w,r as b,a as V,c as y,d,e as s,f as l,w as u,g as k,o as h,i as x,s as m}from"./index--MNqwREY.js";import{_ as P}from"./_plugin-vue_export-helper-DlAUqK2U.js";const q={class:"register-page"},R={class:"header"},U={class:"form-container"},B={class:"button-group"},C={__name:"Register",setup(N){const i=k(),p=w(),e=b({username:"",email:"",password:"",confirmPassword:""}),n=V(!1),c=async()=>{if(e.password!==e.confirmPassword){m("两次输入的密码不一致");return}n.value=!0;try{await p.register({username:e.username,email:e.email,password:e.password,display_name:e.username}),m({message:"注册成功，请登录",type:"success"}),i.push("/login")}catch(r){console.error("注册失败:",r),m({message:r.message||"注册失败",type:"fail"})}finally{n.value=!1}};return(r,a)=>{const f=l("van-nav-bar"),t=l("van-field"),_=l("van-cell-group"),v=l("van-button"),g=l("van-form");return h(),y("div",q,[d("div",R,[s(f,{title:"注册账户","left-arrow":"",onClickLeft:a[0]||(a[0]=o=>r.$router.back()),fixed:"",placeholder:""})]),d("div",U,[s(g,{onSubmit:c},{default:u(()=>[s(_,{inset:""},{default:u(()=>[s(t,{modelValue:e.username,"onUpdate:modelValue":a[1]||(a[1]=o=>e.username=o),name:"username",label:"用户名",placeholder:"请输入用户名","left-icon":"user-o",rules:[{required:!0,message:"请输入用户名"}],clearable:""},null,8,["modelValue"]),s(t,{modelValue:e.email,"onUpdate:modelValue":a[2]||(a[2]=o=>e.email=o),name:"email",label:"邮箱",placeholder:"请输入邮箱","left-icon":"envelop-o",rules:[{required:!0,message:"请输入邮箱"}],clearable:""},null,8,["modelValue"]),s(t,{modelValue:e.password,"onUpdate:modelValue":a[3]||(a[3]=o=>e.password=o),type:"password",name:"password",label:"密码",placeholder:"请输入密码","left-icon":"lock",rules:[{required:!0,message:"请输入密码"}],clearable:""},null,8,["modelValue"]),s(t,{modelValue:e.confirmPassword,"onUpdate:modelValue":a[4]||(a[4]=o=>e.confirmPassword=o),type:"password",name:"confirmPassword",label:"确认密码",placeholder:"请再次输入密码","left-icon":"lock",rules:[{required:!0,message:"请确认密码"}],clearable:""},null,8,["modelValue"])]),_:1}),d("div",B,[s(v,{round:"",block:"",type:"primary","native-type":"submit",loading:n.value,"loading-text":"注册中...",size:"large"},{default:u(()=>a[5]||(a[5]=[x(" 注册 ")])),_:1,__:[5]},8,["loading"])])]),_:1})])])}}},$=P(C,[["__scopeId","data-v-1bf4776a"]]);export{$ as default};
