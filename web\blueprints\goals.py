#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
目标相关API蓝图
"""

from flask import Blueprint, request, jsonify, g, session
import datetime
import uuid
import logging
import sqlite3
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.database_config import MAIN_DB_PATH

# 创建蓝图
bp = Blueprint('goals', __name__)
logger = logging.getLogger(__name__)

def get_db_connection():
    """获取数据库连接"""
    if 'db' not in g:
        g.db = sqlite3.connect(MAIN_DB_PATH)
        g.db.row_factory = sqlite3.Row
    return g.db

def login_required(f):
    """登录验证装饰器"""
    from functools import wraps

    @wraps(f)
    def decorated(*args, **kwargs):
        if not session.get('logged_in') or not session.get('user_id'):
            logger.warning(f"未登录用户访问API: {request.path}")
            return jsonify({'success': False, 'error': '请先登录'}), 401
        return f(*args, **kwargs)
    return decorated

def get_current_user_id():
    """获取当前登录用户ID"""
    return session.get('user_id')

@bp.route('/', methods=['GET'])
@login_required
def get_goals():
    """获取所有目标"""
    user_id = get_current_user_id()
    logger.info(f"用户 {user_id} 获取所有目标")
    conn = get_db_connection()
    goals = conn.execute('SELECT * FROM goals WHERE user_id = ? ORDER BY name', (user_id,)).fetchall()
    return jsonify([dict(goal) for goal in goals])

@bp.route('/<goal_id>', methods=['GET'])
@login_required
def get_goal(goal_id):
    """获取单个目标"""
    user_id = get_current_user_id()
    logger.info(f"用户 {user_id} 获取目标: {goal_id}")
    conn = get_db_connection()
    goal = conn.execute('SELECT * FROM goals WHERE id = ? AND user_id = ?', (goal_id, user_id)).fetchone()

    if goal is None:
        return jsonify({'error': '目标不存在'}), 404

    return jsonify(dict(goal))

@bp.route('/', methods=['POST'])
@login_required
def create_goal():
    """创建目标"""
    user_id = get_current_user_id()
    data = request.json
    logger.info(f"用户 {user_id} 创建/更新目标: {data.get('id', '新目标')}")

    # 验证必填字段
    required_fields = ['name', 'target_amount', 'current_amount', 'target_date', 'currency']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'缺少必填字段: {field}'}), 400

    # 生成ID和时间戳
    goal_id = data.get('id') or f"goal_{uuid.uuid4()}"
    now = datetime.datetime.now().isoformat()

    conn = get_db_connection()

    # 检查是否存在相同ID的目标（只检查当前用户的目标）
    existing = conn.execute('SELECT id FROM goals WHERE id = ? AND user_id = ?', (goal_id, user_id)).fetchone()

    if existing:
        # 更新现有目标
        conn.execute('''
        UPDATE goals
        SET name = ?, target_amount = ?, current_amount = ?, target_date = ?, currency = ?,
            description = ?, icon = ?, updated_at = ?
        WHERE id = ? AND user_id = ?
        ''', (
            data['name'], data['target_amount'], data['current_amount'], data['target_date'],
            data['currency'], data.get('description', ''), data.get('icon', ''), now, goal_id, user_id
        ))
        logger.info(f"用户 {user_id} 更新目标: {goal_id}")
    else:
        # 创建新目标
        conn.execute('''
        INSERT INTO goals (id, user_id, name, target_amount, current_amount, target_date, currency,
                        description, icon, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            goal_id, user_id, data['name'], data['target_amount'], data['current_amount'], data['target_date'],
            data['currency'], data.get('description', ''), data.get('icon', ''), now, now
        ))
        logger.info(f"用户 {user_id} 创建新目标: {goal_id}")

    conn.commit()

    return jsonify({'id': goal_id, 'success': True})

@bp.route('/<goal_id>', methods=['DELETE'])
@login_required
def delete_goal(goal_id):
    """删除目标"""
    user_id = get_current_user_id()
    logger.info(f"用户 {user_id} 删除目标: {goal_id}")
    conn = get_db_connection()

    # 检查目标是否存在且属于当前用户
    goal = conn.execute('SELECT id FROM goals WHERE id = ? AND user_id = ?', (goal_id, user_id)).fetchone()
    if not goal:
        return jsonify({'error': '目标不存在'}), 404

    conn.execute('DELETE FROM goals WHERE id = ? AND user_id = ?', (goal_id, user_id))
    conn.commit()

    return jsonify({'success': True})
