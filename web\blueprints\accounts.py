#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
账户相关API蓝图
"""

from flask import Blueprint, request, jsonify, g, session
import datetime
import uuid
import logging
import sqlite3
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.database_config import MAIN_DB_PATH

# 创建蓝图
bp = Blueprint('accounts', __name__)
logger = logging.getLogger(__name__)

def get_db_connection():
    """获取数据库连接"""
    if 'db' not in g:
        g.db = sqlite3.connect(MAIN_DB_PATH)
        g.db.row_factory = sqlite3.Row
    return g.db

def login_required(f):
    """登录验证装饰器"""
    from functools import wraps

    @wraps(f)
    def decorated(*args, **kwargs):
        if not session.get('logged_in') or not session.get('user_id'):
            logger.warning(f"未登录用户访问API: {request.path}")
            return jsonify({'success': False, 'error': '请先登录'}), 401
        return f(*args, **kwargs)
    return decorated

def get_current_user_id():
    """获取当前登录用户ID"""
    return session.get('user_id')

@bp.route('/', methods=['GET'])
@login_required
def get_accounts():
    """获取所有账户"""
    user_id = get_current_user_id()
    logger.info(f"用户 {user_id} 获取所有账户")
    conn = get_db_connection()
    accounts = conn.execute('SELECT * FROM accounts WHERE user_id = ? ORDER BY name', (user_id,)).fetchall()
    return jsonify([dict(acc) for acc in accounts])

@bp.route('/<account_id>', methods=['GET'])
@login_required
def get_account(account_id):
    """获取单个账户"""
    user_id = get_current_user_id()
    logger.info(f"用户 {user_id} 获取账户: {account_id}")
    conn = get_db_connection()
    account = conn.execute('SELECT * FROM accounts WHERE id = ? AND user_id = ?', (account_id, user_id)).fetchone()

    if account is None:
        return jsonify({'error': '账户不存在'}), 404

    return jsonify(dict(account))

@bp.route('/', methods=['POST'])
@login_required
def create_account():
    """创建账户"""
    user_id = get_current_user_id()
    data = request.json
    logger.info(f"用户 {user_id} 创建/更新账户: {data.get('id', '新账户')}")

    # 验证必填字段
    required_fields = ['name', 'type', 'currency', 'initial_balance']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'缺少必填字段: {field}'}), 400

    # 生成ID和时间戳
    account_id = data.get('id') or f"account_{uuid.uuid4()}"
    now = datetime.datetime.now().isoformat()

    conn = get_db_connection()

    # 检查是否存在相同ID的账户（只检查当前用户的账户）
    existing = conn.execute('SELECT id FROM accounts WHERE id = ? AND user_id = ?', (account_id, user_id)).fetchone()

    if existing:
        # 更新现有账户
        conn.execute('''
        UPDATE accounts
        SET name = ?, type = ?, currency = ?, initial_balance = ?, description = ?, icon = ?, updated_at = ?
        WHERE id = ? AND user_id = ?
        ''', (
            data['name'], data['type'], data['currency'], data['initial_balance'],
            data.get('description', ''), data.get('icon', ''), now, account_id, user_id
        ))
        logger.info(f"用户 {user_id} 更新账户: {account_id}")
    else:
        # 创建新账户
        conn.execute('''
        INSERT INTO accounts (id, user_id, name, type, currency, initial_balance, description, icon, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            account_id, user_id, data['name'], data['type'], data['currency'], data['initial_balance'],
            data.get('description', ''), data.get('icon', ''), now, now
        ))
        logger.info(f"用户 {user_id} 创建新账户: {account_id}")

    conn.commit()

    return jsonify({'id': account_id, 'success': True})

@bp.route('/<account_id>', methods=['PUT'])
@login_required
def update_account(account_id):
    """更新账户"""
    user_id = get_current_user_id()
    data = request.json
    logger.info(f"用户 {user_id} 更新账户: {account_id}")

    # 验证必填字段
    required_fields = ['name', 'type', 'currency', 'initial_balance']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'缺少必填字段: {field}'}), 400

    conn = get_db_connection()

    # 检查账户是否存在且属于当前用户
    account = conn.execute('SELECT id FROM accounts WHERE id = ? AND user_id = ?', (account_id, user_id)).fetchone()
    if not account:
        return jsonify({'error': '账户不存在'}), 404

    # 更新账户
    now = datetime.datetime.now().isoformat()
    conn.execute('''
    UPDATE accounts
    SET name = ?, type = ?, currency = ?, initial_balance = ?, description = ?, icon = ?, updated_at = ?
    WHERE id = ? AND user_id = ?
    ''', (
        data['name'], data['type'], data['currency'], data['initial_balance'],
        data.get('description', ''), data.get('icon', ''), now, account_id, user_id
    ))

    conn.commit()

    return jsonify({'success': True})

@bp.route('/<account_id>/balance', methods=['GET'])
@login_required
def get_account_balance(account_id):
    """获取账户余额"""
    user_id = get_current_user_id()
    logger.info(f"用户 {user_id} 获取账户余额: {account_id}")

    conn = get_db_connection()

    # 检查账户是否存在且属于当前用户
    account = conn.execute('SELECT * FROM accounts WHERE id = ? AND user_id = ?', (account_id, user_id)).fetchone()
    if not account:
        return jsonify({'error': '账户不存在'}), 404

    # 计算账户余额：初始余额 + 收入 - 支出
    income = conn.execute('''
        SELECT COALESCE(SUM(amount), 0) FROM transactions
        WHERE account = ? AND user_id = ? AND type = 'income'
    ''', (account_id, user_id)).fetchone()[0]

    expense = conn.execute('''
        SELECT COALESCE(SUM(amount), 0) FROM transactions
        WHERE account = ? AND user_id = ? AND type = 'expense'
    ''', (account_id, user_id)).fetchone()[0]

    current_balance = account['initial_balance'] + income - expense

    return jsonify({
        'account_id': account_id,
        'initial_balance': account['initial_balance'],
        'current_balance': current_balance,
        'income': income,
        'expense': expense
    })

@bp.route('/balances', methods=['GET'])
@login_required
def get_all_balances():
    """获取所有账户余额汇总"""
    user_id = get_current_user_id()
    logger.info(f"用户 {user_id} 获取所有账户余额")

    conn = get_db_connection()

    # 获取用户所有账户
    accounts = conn.execute('SELECT * FROM accounts WHERE user_id = ? ORDER BY name', (user_id,)).fetchall()

    balances = []
    total_balance = 0

    for account in accounts:
        account_id = account['id']

        # 计算每个账户的余额
        income = conn.execute('''
            SELECT COALESCE(SUM(amount), 0) FROM transactions
            WHERE account = ? AND user_id = ? AND type = 'income'
        ''', (account_id, user_id)).fetchone()[0]

        expense = conn.execute('''
            SELECT COALESCE(SUM(amount), 0) FROM transactions
            WHERE account = ? AND user_id = ? AND type = 'expense'
        ''', (account_id, user_id)).fetchone()[0]

        current_balance = account['initial_balance'] + income - expense
        total_balance += current_balance

        balances.append({
            'id': account_id,
            'name': account['name'],
            'type': account['type'],
            'currency': account['currency'],
            'initial_balance': account['initial_balance'],
            'current_balance': current_balance,
            'income': income,
            'expense': expense
        })

    return jsonify({
        'accounts': balances,
        'total_balance': total_balance
    })

@bp.route('/<account_id>', methods=['DELETE'])
@login_required
def delete_account(account_id):
    """删除账户"""
    user_id = get_current_user_id()
    logger.info(f"用户 {user_id} 删除账户: {account_id}")

    # 不允许删除默认账户
    if account_id == 'default':
        return jsonify({'error': '不能删除默认账户'}), 400

    conn = get_db_connection()

    # 检查账户是否存在且属于当前用户
    account = conn.execute('SELECT id FROM accounts WHERE id = ? AND user_id = ?', (account_id, user_id)).fetchone()
    if not account:
        return jsonify({'error': '账户不存在'}), 404

    # 删除账户
    conn.execute('DELETE FROM accounts WHERE id = ? AND user_id = ?', (account_id, user_id))

    # 将该账户的交易转移到默认账户（只转移当前用户的交易）
    conn.execute('UPDATE transactions SET account = ? WHERE account = ? AND user_id = ?', ('default', account_id, user_id))

    conn.commit()

    return jsonify({'success': True})
