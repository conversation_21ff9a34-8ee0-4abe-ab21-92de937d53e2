# 预算重复检测逻辑测试示例

## 修改前后对比

### 修改前的问题
**检测规则**：`(period, category)` 组合
**问题场景**：
```
现有预算：
- 项目A, 2025-01, 办公用品, 1000元
- 项目B, 2025-01, 办公用品, 2000元  ← 会被错误跳过

导入数据：
- 项目B, 2025-01, 办公用品, 2000元  ← 被误判为重复
```

### 修改后的正确逻辑
**检测规则**：`(project_name, period, category)` 组合
**正确处理**：
```
现有预算：
- 项目A, 2025-01, 办公用品, 1000元
- 项目B, 2025-01, 办公用品, 2000元  ← 正常导入

导入数据：
- 项目B, 2025-01, 办公用品, 2000元  ← 正确识别为重复并跳过
- 项目C, 2025-01, 办公用品, 1500元  ← 正常导入（不同项目）
```

## 测试用例

### 测试用例1：不同项目相同类别
```
现有数据：
项目名称: "营销推广", 期间: "2025-01", 类别: "广告费"

导入数据：
项目名称: "产品开发", 期间: "2025-01", 类别: "广告费"

预期结果: 成功导入（不同项目，允许相同类别）
```

### 测试用例2：相同项目相同类别
```
现有数据：
项目名称: "营销推广", 期间: "2025-01", 类别: "广告费"

导入数据：
项目名称: "营销推广", 期间: "2025-01", 类别: "广告费"

预期结果: 跳过导入（完全相同的组合）
```

### 测试用例3：相同项目不同期间
```
现有数据：
项目名称: "营销推广", 期间: "2025-01", 类别: "广告费"

导入数据：
项目名称: "营销推广", 期间: "2025-02", 类别: "广告费"

预期结果: 成功导入（不同期间，允许导入）
```

### 测试用例4：相同项目相同期间不同类别
```
现有数据：
项目名称: "营销推广", 期间: "2025-01", 类别: "广告费"

导入数据：
项目名称: "营销推广", 期间: "2025-01", 类别: "宣传费"

预期结果: 成功导入（不同类别，允许导入）
```

## 业务场景验证

### 场景1：多项目管理
```
公司同时运行多个项目：
- 项目A：新产品开发
- 项目B：市场推广
- 项目C：系统升级

每个项目在同一期间都可能有相同类别的预算：
- 项目A, 2025-01, 办公用品, 5000元
- 项目B, 2025-01, 办公用品, 3000元
- 项目C, 2025-01, 办公用品, 2000元

修改后的逻辑正确支持这种场景。
```

### 场景2：项目预算调整
```
如果需要调整某个项目的预算，应该：
1. 先删除或修改现有预算记录
2. 再导入新的预算数据

重复检测机制防止意外创建重复预算。
```

## 代码变更总结

### 1. 函数签名更新
```python
# 修改前
def get_existing_budgets(user_id):
    """获取现有预算的期间-类别组合集合"""
    budgets = cursor.execute('SELECT period, category FROM budgets WHERE user_id = ?', (user_id,)).fetchall()
    return {(budget[0], budget[1]) for budget in budgets}

# 修改后  
def get_existing_budgets(user_id):
    """获取现有预算的项目名称-期间-类别组合集合"""
    budgets = cursor.execute('SELECT project_name, period, category FROM budgets WHERE user_id = ?', (user_id,)).fetchall()
    return {(budget[0], budget[1], budget[2]) for budget in budgets}
```

### 2. 重复检测逻辑更新
```python
# 修改前
if (period, category) in existing_budgets:
    logger.warning(f"预算已存在，跳过: {period} - {category}")

# 修改后
if (project_name, period, category) in existing_budgets:
    logger.warning(f"预算已存在，跳过: {project_name} - {period} - {category}")
```

### 3. 缓存更新逻辑
```python
# 修改前
existing_budgets.add((period, category))

# 修改后
existing_budgets.add((project_name, period, category))
```

这些修改确保了预算数据的唯一性检测更加准确，符合实际业务需求。
