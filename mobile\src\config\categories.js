/**
 * 分类配置
 * 统一管理交易和预算的分类选项
 */

// 支出分类
export const expenseCategories = [
  '储蓄', '固定', '流动', '债务'
]

// 收入分类
export const incomeCategories = [
  '工资', '奖金', '投资收益', '兼职', '礼金',
  '退款', '利息', '租金收入', '其他收入'
]

// KDI专用收入分类
export const kdiIncomeCategories = [
  '入金', '收益'
]

// KDI专用支出分类
export const kdiExpenseCategories = [
  '出金'
]

// 预算分类（目前与支出分类相同）
export const budgetCategories = [
  '储蓄', '固定', '流动', '债务'
]

// 分类选择器格式化函数
export const formatCategoriesForPicker = (categories) => {
  return categories.map(cat => ({ text: cat, value: cat }))
}

// 获取分类选项
export const getCategoryOptions = (type = 'expense', accountName = '') => {
  // 如果是KDI账户，使用专用分类
  if (accountName === 'KDI') {
    switch (type) {
      case 'income':
        return formatCategoriesForPicker(kdiIncomeCategories)
      case 'expense':
        return formatCategoriesForPicker(kdiExpenseCategories)
      default:
        return formatCategoriesForPicker(kdiExpenseCategories)
    }
  }

  // 普通账户使用标准分类
  switch (type) {
    case 'income':
      return formatCategoriesForPicker(incomeCategories)
    case 'expense':
      return formatCategoriesForPicker(expenseCategories)
    case 'budget':
      return formatCategoriesForPicker(budgetCategories)
    default:
      return formatCategoriesForPicker(expenseCategories)
  }
}

// 分类图标映射（可选，用于UI显示）
export const categoryIcons = {
  '储蓄': '💰',
  '固定': '🏠',
  '流动': '🛒',
  '债务': '💳',
  '工资': '💼',
  '奖金': '🎁',
  '投资收益': '📈',
  '兼职': '⚡',
  '礼金': '🎉',
  '退款': '↩️',
  '利息': '🏦',
  '租金收入': '🏘️',
  '其他收入': '💵',
  // KDI专用分类图标
  '入金': '💰',
  '收益': '📈',
  '出金': '💸'
}

// 分类颜色映射（可选，用于图表显示）
export const categoryColors = {
  '储蓄': '#4CAF50',
  '固定': '#FF9800',
  '流动': '#2196F3',
  '债务': '#F44336',
  '工资': '#9C27B0',
  '奖金': '#E91E63',
  '投资收益': '#00BCD4',
  '兼职': '#FFEB3B',
  '礼金': '#FF5722',
  '退款': '#795548',
  '利息': '#607D8B',
  '租金收入': '#3F51B5',
  '其他收入': '#8BC34A',
  // KDI专用分类颜色
  '入金': '#4CAF50',
  '收益': '#00BCD4',
  '出金': '#FF5722'
}
