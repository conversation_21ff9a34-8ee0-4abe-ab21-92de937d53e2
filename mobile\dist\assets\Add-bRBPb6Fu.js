import{a as v,r as X,l as U,c as h,e as a,d as s,f as c,w as l,g as Z,o as V,v as ee,p as te,t as m,i as oe,n as S,s as Y}from"./index--MNqwREY.js";import{u as ae}from"./subscriptions-4RI1wB9c.js";import{c as M,S as P,g as ne}from"./ServiceLogo-DKV-znac.js";import{_ as se}from"./_plugin-vue_export-helper-DlAUqK2U.js";const le={class:"add-subscription-page"},re={class:"page-container"},ie={key:1,class:"service-icon"},ce={class:"currency-symbol"},ue={class:"field-icon"},de={class:"field-icon"},ve={class:"field-icon"},me={class:"field-icon"},pe={key:0,class:"preview-section"},_e={class:"preview-card"},ye={class:"preview-header"},fe={class:"preview-service"},ge={class:"service-details"},we={class:"service-name"},be={class:"service-type"},Ce={class:"preview-price"},ke={class:"amount"},Ve={class:"cycle"},xe={class:"preview-footer"},he={class:"start-date"},Se={class:"submit-section"},$e={__name:"Add",setup(De){const N=Z(),R=ae(),x=v(!1),p=v(!1),_=v(!1),y=v(!1),f=v(!1),t=X({name:"",service_type:"",amount:"",currency:"MYR",billing_cycle:"",start_date:"",description:""}),q=U(()=>t.service_type&&t.amount&&t.currency&&t.billing_cycle&&t.start_date&&/^\d+(\.\d{1,2})?$/.test(t.amount)),w=v([]),B=new Date(2020,0,1),F=new Date(2030,11,31),g=v([]),T=U(()=>{var u;if(g.value.length===0)return"";const[n,e]=g.value,i=M.find(d=>d.value===n);if(!i)return"";const r=(u=i.children)==null?void 0:u.find(d=>d.value===e);return r?`${i.text} ${r.text}`:i.text}),E=[{text:"月付",value:"monthly"},{text:"季付",value:"quarterly"},{text:"年付",value:"yearly"},{text:"周付",value:"weekly"}],I=[{text:"人民币 (CNY)",value:"CNY"},{text:"美元 (USD)",value:"USD"},{text:"马来西亚令吉 (MYR)",value:"MYR"},{text:"欧元 (EUR)",value:"EUR"},{text:"英镑 (GBP)",value:"GBP"},{text:"日元 (JPY)",value:"JPY"}],b=n=>n?ne(n).name:"未选择",L=n=>({weekly:"/周",monthly:"/月",quarterly:"/季",yearly:"/年"})[n]||"/月",$=n=>({CNY:"¥",USD:"$",MYR:"RM",EUR:"€",GBP:"£",JPY:"¥"})[n]||"¥",z=n=>{if(!n)return"未选择";try{const e=new Date(n);return`${e.getFullYear()}年${e.getMonth()+1}月${e.getDate()}日`}catch{return"未知"}},G=({selectedOptions:n})=>{const e=n.map(i=>i.value);if(g.value=e,t.service_type=e[e.length-1],n.length>0){const i=n[0].text.split(" ").slice(1).join(" "),r=n.length>1?n[1].text:"";t.name=r?`${i} ${r}`:i}p.value=!1},J=({selectedOptions:n})=>{t.billing_cycle=n[0].value,_.value=!1},O=({selectedOptions:n})=>{t.currency=n[0].value,y.value=!1},j=()=>{const[n,e,i]=w.value,r=new Date(n,e-1,i);t.start_date=r.toISOString().split("T")[0],f.value=!1},A=async()=>{x.value=!0;try{const n={...t,amount:parseFloat(t.amount),start_date:new Date(t.start_date).toISOString()};await R.createSubscription(n),Y({message:"订阅添加成功",type:"success"}),N.back()}catch(n){Y({message:n.message||"添加失败",type:"fail"})}finally{x.value=!1}},C=new Date;return t.start_date=C.toISOString().split("T")[0],w.value=[C.getFullYear(),C.getMonth()+1,C.getDate()],(n,e)=>{const i=c("van-nav-bar"),r=c("van-icon"),u=c("van-field"),d=c("van-cell-group"),H=c("van-button"),K=c("van-form"),Q=c("van-cascader"),k=c("van-popup"),D=c("van-picker"),W=c("van-date-picker");return V(),h("div",le,[a(i,{title:"添加订阅","left-arrow":"",fixed:"",placeholder:"","safe-area-inset-top":"",onClickLeft:e[0]||(e[0]=o=>n.$router.back())}),s("div",re,[e[22]||(e[22]=s("div",{class:"page-header"},[s("h1",{class:"page-title"},"创建新订阅"),s("p",{class:"page-description"},"添加您的订阅服务，让我们帮您管理和提醒")],-1)),a(K,{onSubmit:A},{default:l(()=>[a(d,{inset:"",title:"🎯 选择服务"},{default:l(()=>[a(u,{"model-value":T.value,name:"service_type",label:"服务套餐",placeholder:"请选择服务和套餐",readonly:"","is-link":"",onClick:e[1]||(e[1]=o=>p.value=!0),rules:[{required:!0,message:"请选择服务套餐"}]},{"left-icon":l(()=>[t.service_type?(V(),te(P,{key:0,"service-name":b(t.service_type),"service-type":t.service_type,size:"small"},null,8,["service-name","service-type"])):(V(),h("div",ie," 📱 "))]),"right-icon":l(()=>[a(r,{name:"arrow",class:"arrow-icon"})]),_:1},8,["model-value"])]),_:1}),a(d,{inset:"",title:"📝 基本信息"},{default:l(()=>[a(u,{modelValue:t.amount,"onUpdate:modelValue":e[2]||(e[2]=o=>t.amount=o),name:"amount",label:"费用",placeholder:"0.00",type:"number",rules:[{required:!0,message:"请输入费用"},{pattern:/^\d+(\.\d{1,2})?$/,message:"请输入正确的金额格式"}]},{"left-icon":l(()=>[s("span",ce,m($(t.currency)),1)]),_:1},8,["modelValue"]),a(u,{modelValue:t.currency,"onUpdate:modelValue":e[3]||(e[3]=o=>t.currency=o),name:"currency",label:"货币",placeholder:"请选择货币",readonly:"","is-link":"",onClick:e[4]||(e[4]=o=>y.value=!0),rules:[{required:!0,message:"请选择货币"}]},{"left-icon":l(()=>[s("div",ue,[a(r,{name:"gold-coin-o"})])]),"right-icon":l(()=>[a(r,{name:"arrow",class:"arrow-icon"})]),_:1},8,["modelValue"]),a(u,{modelValue:t.billing_cycle,"onUpdate:modelValue":e[5]||(e[5]=o=>t.billing_cycle=o),name:"billing_cycle",label:"计费周期",placeholder:"请选择计费周期",readonly:"","is-link":"",onClick:e[6]||(e[6]=o=>_.value=!0),rules:[{required:!0,message:"请选择计费周期"}]},{"left-icon":l(()=>[s("div",de,[a(r,{name:"clock-o"})])]),"right-icon":l(()=>[a(r,{name:"arrow",class:"arrow-icon"})]),_:1},8,["modelValue"]),a(u,{modelValue:t.start_date,"onUpdate:modelValue":e[7]||(e[7]=o=>t.start_date=o),name:"start_date",label:"开始日期",placeholder:"请选择开始日期",readonly:"","is-link":"",onClick:e[8]||(e[8]=o=>f.value=!0),rules:[{required:!0,message:"请选择开始日期"}]},{"left-icon":l(()=>[s("div",ve,[a(r,{name:"calendar-o"})])]),"right-icon":l(()=>[a(r,{name:"arrow",class:"arrow-icon"})]),_:1},8,["modelValue"])]),_:1}),a(d,{inset:"",title:"💭 可选信息"},{default:l(()=>[a(u,{modelValue:t.description,"onUpdate:modelValue":e[9]||(e[9]=o=>t.description=o),name:"description",label:"备注",placeholder:"添加一些备注信息（可选）",type:"textarea",rows:"3",autosize:""},{"left-icon":l(()=>[s("div",me,[a(r,{name:"notes-o"})])]),_:1},8,["modelValue"])]),_:1}),t.service_type&&t.amount&&t.billing_cycle?(V(),h("div",pe,[e[20]||(e[20]=s("h3",{class:"preview-title"},"📋 预览",-1)),s("div",_e,[s("div",ye,[s("div",fe,[a(P,{"service-name":t.name||b(t.service_type),"service-type":t.service_type,size:"small"},null,8,["service-name","service-type"]),s("div",ge,[s("h4",we,m(t.name||b(t.service_type)),1),s("p",be,m(b(t.service_type)),1)])]),s("div",Ce,[s("span",ke,m($(t.currency))+m(t.amount),1),s("span",Ve,m(L(t.billing_cycle)),1)])]),s("div",xe,[s("span",he,"开始日期: "+m(z(t.start_date)),1)])])])):ee("",!0),s("div",Se,[a(H,{type:"primary",block:"","native-type":"submit",loading:x.value,disabled:!q.value},{default:l(()=>[a(r,{name:"plus"}),e[21]||(e[21]=oe(" 添加订阅 "))]),_:1,__:[21]},8,["loading","disabled"])])]),_:1})]),a(k,{show:p.value,"onUpdate:show":e[12]||(e[12]=o=>p.value=o),position:"bottom",round:""},{default:l(()=>[a(Q,{modelValue:g.value,"onUpdate:modelValue":e[10]||(e[10]=o=>g.value=o),title:"选择服务套餐",options:S(M),onClose:e[11]||(e[11]=o=>p.value=!1),onFinish:G},null,8,["modelValue","options"])]),_:1},8,["show"]),a(k,{show:_.value,"onUpdate:show":e[14]||(e[14]=o=>_.value=o),position:"bottom",round:""},{default:l(()=>[a(D,{columns:E,onConfirm:J,onCancel:e[13]||(e[13]=o=>_.value=!1)})]),_:1},8,["show"]),a(k,{show:y.value,"onUpdate:show":e[16]||(e[16]=o=>y.value=o),position:"bottom",round:""},{default:l(()=>[a(D,{columns:I,onConfirm:O,onCancel:e[15]||(e[15]=o=>y.value=!1)})]),_:1},8,["show"]),a(k,{show:f.value,"onUpdate:show":e[19]||(e[19]=o=>f.value=o),position:"bottom",round:""},{default:l(()=>[a(W,{modelValue:w.value,"onUpdate:modelValue":e[17]||(e[17]=o=>w.value=o),onConfirm:j,onCancel:e[18]||(e[18]=o=>f.value=!1),"min-date":S(B),"max-date":S(F)},null,8,["modelValue","min-date","max-date"])]),_:1},8,["show"])])}}},Ne=se($e,[["__scopeId","data-v-cde0eaab"]]);export{Ne as default};
