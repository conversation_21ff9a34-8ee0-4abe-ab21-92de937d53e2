import { defineStore } from 'pinia'
import { budgetsAPI } from '@/api/budgets'
import { transactionsAPI } from '@/api/transactions'

export const useBudgetsStore = defineStore('budgets', {
  state: () => ({
    budgets: [],
    loading: false,
    currentBudget: null
  }),

  getters: {
    // 按状态分组的预算
    budgetsByStatus: (state) => {
      const grouped = {
        normal: [],
        warning: [],
        danger: [],
        exceeded: []
      }
      
      state.budgets.forEach(budget => {
        const status = budget.status || 'normal'
        if (grouped[status]) {
          grouped[status].push(budget)
        } else {
          grouped.normal.push(budget)
        }
      })
      
      return grouped
    },

    // 按类别分组的预算
    budgetsByCategory: (state) => {
      const grouped = {}
      state.budgets.forEach(budget => {
        if (!grouped[budget.category]) {
          grouped[budget.category] = []
        }
        grouped[budget.category].push(budget)
      })
      return grouped
    },

    // 活跃预算（当前时间在预算期间内）
    activeBudgets: (state) => {
      const now = new Date().toISOString().split('T')[0]
      return state.budgets.filter(budget => 
        budget.start_date <= now && budget.end_date >= now
      )
    },

    // 预算总金额
    totalBudgetAmount: (state) => {
      return state.budgets.reduce((total, budget) => total + (budget.amount || 0), 0)
    },

    // 预算使用总金额
    totalUsedAmount: (state) => {
      return state.budgets.reduce((total, budget) => total + (budget.used_amount || 0), 0)
    },

    // 预算使用率
    totalUsagePercentage: (state) => {
      const total = state.budgets.reduce((sum, budget) => sum + (budget.amount || 0), 0)
      const used = state.budgets.reduce((sum, budget) => sum + (budget.used_amount || 0), 0)
      return total > 0 ? (used / total) * 100 : 0
    },

    // 需要警告的预算
    warningBudgets: (state) => {
      return state.budgets.filter(budget => {
        const percentage = budget.amount > 0 ? (budget.used_amount / budget.amount) * 100 : 0
        return percentage >= 80 && percentage < 100
      })
    },

    // 超支的预算
    exceededBudgets: (state) => {
      return state.budgets.filter(budget => {
        const percentage = budget.amount > 0 ? (budget.used_amount / budget.amount) * 100 : 0
        return percentage >= 100
      })
    }
  },

  actions: {
    // 获取预算列表
    async fetchBudgets() {
      this.loading = true
      try {
        const response = await budgetsAPI.getBudgets()
        const budgets = Array.isArray(response) ? response : (response.data || [])

        // 为每个预算计算使用情况
        const budgetsWithUsage = await Promise.all(
          budgets.map(async (budget) => {
            try {
              const usage = await this.calculateBudgetUsage(budget)
              return {
                ...budget,
                ...usage
              }
            } catch (error) {
              console.error(`计算预算 ${budget.id} 使用情况失败:`, error)
              return {
                ...budget,
                used_amount: 0,
                remaining_amount: budget.amount,
                usage_percentage: 0,
                status: 'normal'
              }
            }
          })
        )

        this.budgets = budgetsWithUsage
        return this.budgets
      } catch (error) {
        console.error('获取预算列表失败:', error)
        this.budgets = []
        throw error
      } finally {
        this.loading = false
      }
    },

    // 获取单个预算
    async fetchBudget(id) {
      try {
        const budget = await budgetsAPI.getBudget(id)
        const usage = await this.calculateBudgetUsage(budget)
        
        this.currentBudget = {
          ...budget,
          ...usage
        }
        
        return this.currentBudget
      } catch (error) {
        console.error('获取预算详情失败:', error)
        throw error
      }
    },

    // 创建预算
    async createBudget(budgetData) {
      try {
        const response = await budgetsAPI.createBudget(budgetData)
        
        // 创建成功后重新获取预算列表
        if (response && response.success) {
          await this.fetchBudgets()
        }
        
        return response
      } catch (error) {
        console.error('创建预算失败:', error)
        throw error
      }
    },

    // 更新预算
    async updateBudget(id, budgetData) {
      try {
        const response = await budgetsAPI.updateBudget(id, budgetData)
        
        // 更新成功后重新获取预算列表
        if (response && response.success) {
          await this.fetchBudgets()
        }
        
        return response
      } catch (error) {
        console.error('更新预算失败:', error)
        throw error
      }
    },

    // 删除预算
    async deleteBudget(id) {
      try {
        const response = await budgetsAPI.deleteBudget(id)
        
        // 删除成功后重新获取预算列表
        if (response && response.success) {
          await this.fetchBudgets()
        }
        
        return response
      } catch (error) {
        console.error('删除预算失败:', error)
        throw error
      }
    },

    // 计算预算使用情况 - 使用精确匹配交易描述和预算项目名称
    async calculateBudgetUsage(budget) {
      try {
        // 获取预算期间内精确匹配项目名称的支出交易
        const response = await transactionsAPI.getTransactions({
          type: 'expense',
          start_date: budget.start_date,
          end_date: budget.end_date
        })

        const transactions = Array.isArray(response) ? response : (response.data || [])

        // 精确匹配交易描述和预算项目名称
        const matchingTransactions = transactions.filter(transaction =>
          transaction.description === budget.project_name
        )

        const usedAmount = matchingTransactions.reduce((sum, transaction) => {
          return sum + (transaction.amount || 0)
        }, 0)

        const remainingAmount = budget.amount - usedAmount
        const usagePercentage = budget.amount > 0 ? (usedAmount / budget.amount) * 100 : 0

        // 确定预算状态
        let status = 'normal'
        if (usagePercentage >= 100) {
          status = 'exceeded'
        } else if (usagePercentage >= 90) {
          status = 'danger'
        } else if (usagePercentage >= 80) {
          status = 'warning'
        }

        return {
          used_amount: usedAmount,
          remaining_amount: remainingAmount,
          usage_percentage: usagePercentage,
          status: status,
          transaction_count: matchingTransactions.length
        }
      } catch (error) {
        console.error('计算预算使用情况失败:', error)
        return {
          used_amount: 0,
          remaining_amount: budget.amount,
          usage_percentage: 0,
          status: 'normal',
          transaction_count: 0
        }
      }
    },

    // 检查预算状态
    async checkBudgetStatus(category) {
      try {
        const response = await budgetsAPI.checkBudgetStatus(category)
        return response
      } catch (error) {
        console.error('检查预算状态失败:', error)
        throw error
      }
    },

    // 清除当前预算
    clearCurrentBudget() {
      this.currentBudget = null
    }
  }
})
