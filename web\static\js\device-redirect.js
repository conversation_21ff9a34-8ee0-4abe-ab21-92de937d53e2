/**
 * 网页版设备检测和自适应重定向
 */

(function() {
    'use strict';
    
    // 检测是否为移动设备
    function isMobile() {
        const userAgent = navigator.userAgent.toLowerCase();
        const mobileKeywords = [
            'android', 'iphone', 'ipad', 'ipod', 'blackberry',
            'windows phone', 'mobile', 'webos', 'opera mini'
        ];

        // 检查User Agent中的移动设备关键词
        const hasMobileUA = mobileKeywords.some(keyword => userAgent.includes(keyword));

        // 检查是否为开发者工具的设备模拟
        const isDevToolsEmulation = window.navigator.userAgentData?.mobile ||
                                    /Mobile|Android/i.test(navigator.userAgent);

        return hasMobileUA || isDevToolsEmulation;
    }

    // 检测屏幕尺寸
    function isSmallScreen() {
        return window.innerWidth <= 768;
    }

    // 判断是否应该使用手机版
    function shouldUseMobileVersion() {
        const screenWidth = window.innerWidth;
        const touchDevice = isTouchDevice();
        const mobileDevice = isMobile();

        console.log('网页版设备检测:', {
            screenWidth,
            touchDevice,
            mobileDevice,
            userAgent: navigator.userAgent
        });

        // 明确的移动设备
        if (mobileDevice) return true;

        // 小屏幕 + 触摸设备 (开发者工具模拟)
        if (screenWidth <= 768 && touchDevice) return true;

        // 非常小的屏幕 (强制使用手机版)
        if (screenWidth <= 480) return true;

        return false;
    }
    
    // 检测是否支持触摸
    function isTouchDevice() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    }
    
    // 获取设备信息
    function getDeviceInfo() {
        return {
            userAgent: navigator.userAgent,
            isMobile: isMobile(),
            isSmallScreen: isSmallScreen(),
            isTouchDevice: isTouchDevice(),
            screenWidth: window.innerWidth,
            screenHeight: window.innerHeight,
            currentPort: window.location.port,
            currentHost: window.location.hostname
        };
    }
    
    // 智能重定向到手机版
    function redirectToMobile() {
        const currentHost = window.location.hostname;
        const mobileUrl = `http://${currentHost}:3000${window.location.pathname}${window.location.search}`;
        
        console.log('检测到移动设备，重定向到手机版:', mobileUrl);
        console.log('设备信息:', getDeviceInfo());
        
        // 显示重定向提示
        const redirectNotice = document.createElement('div');
        redirectNotice.innerHTML = `
            <div style="
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                z-index: 9999;
                font-family: Arial, sans-serif;
            ">
                <div style="text-align: center; padding: 20px;">
                    <h2>📱 检测到移动设备</h2>
                    <p>正在为您跳转到手机版...</p>
                    <div style="margin: 20px 0;">
                        <div style="
                            width: 40px;
                            height: 40px;
                            border: 4px solid #f3f3f3;
                            border-top: 4px solid #3498db;
                            border-radius: 50%;
                            animation: spin 1s linear infinite;
                            margin: 0 auto;
                        "></div>
                    </div>
                    <p style="font-size: 14px; opacity: 0.8;">
                        如果没有自动跳转，请点击：
                        <a href="${mobileUrl}" style="color: #3498db;">手机版链接</a>
                    </p>
                </div>
            </div>
            <style>
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
        `;
        
        document.body.appendChild(redirectNotice);
        
        // 延迟跳转，让用户看到提示
        setTimeout(() => {
            window.location.href = mobileUrl;
        }, 1500);
    }
    
    // 检查是否需要重定向
    function checkRedirect() {
        // 如果已经在手机版端口，不需要重定向
        if (window.location.port === '3000') {
            return;
        }

        // 检查是否应该使用手机版
        if (shouldUseMobileVersion()) {
            console.log('检测到应该使用手机版，准备重定向');
            // 检查手机版是否可用
            fetch('http://' + window.location.hostname + ':3000/api/test', {
                method: 'GET',
                mode: 'no-cors',
                timeout: 3000
            }).then(() => {
                redirectToMobile();
            }).catch(() => {
                console.log('手机版服务器未启动，继续使用网页版');
                showMobileVersionNotice();
            });
        } else {
            console.log('检测到桌面设备，使用网页版');
            console.log('设备信息:', getDeviceInfo());
        }
    }
    
    // 显示手机版未启动提示
    function showMobileVersionNotice() {
        const notice = document.createElement('div');
        notice.innerHTML = `
            <div style="
                position: fixed;
                top: 20px;
                right: 20px;
                background: #ff9800;
                color: white;
                padding: 12px 16px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                z-index: 1000;
                font-size: 14px;
                max-width: 300px;
            ">
                📱 检测到移动设备，但手机版未启动。<br>
                建议启动手机版以获得更好的体验。
                <button onclick="this.parentElement.remove()" style="
                    background: none;
                    border: none;
                    color: white;
                    float: right;
                    cursor: pointer;
                    font-size: 16px;
                    margin-left: 10px;
                ">×</button>
            </div>
        `;
        
        document.body.appendChild(notice);
        
        // 5秒后自动消失
        setTimeout(() => {
            if (notice.parentElement) {
                notice.remove();
            }
        }, 5000);
    }
    
    // 页面加载完成后执行检查
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', checkRedirect);
    } else {
        checkRedirect();
    }
    
    // 监听窗口大小变化
    let resizeTimer;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(function() {
            // 如果窗口变小且支持触摸，提示切换到手机版
            if (isSmallScreen() && isTouchDevice() && window.location.port !== '3000') {
                console.log('窗口变小，建议使用手机版');
            }
        }, 250);
    });
    
})();
