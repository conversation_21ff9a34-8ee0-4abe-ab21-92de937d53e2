import { defineStore } from 'pinia'
import { authAPI } from '@/api/auth'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    isLoggedIn: false,
    loading: false
  }),

  getters: {
    userInfo: (state) => state.user,
    isAuthenticated: (state) => state.isLoggedIn && !!state.user
  },

  actions: {
    // 用户登录
    async login(credentials) {
      this.loading = true
      try {
        const response = await authAPI.login(credentials)
        
        if (response.success) {
          this.user = response.user
          this.isLoggedIn = true
          
          // 保存登录状态到本地存储
          localStorage.setItem('isLoggedIn', 'true')
          localStorage.setItem('user', JSON.stringify(response.user))
          
          return response
        } else {
          throw new Error(response.error || '登录失败')
        }
      } catch (error) {
        this.user = null
        this.isLoggedIn = false
        localStorage.removeItem('isLoggedIn')
        localStorage.removeItem('user')
        throw error
      } finally {
        this.loading = false
      }
    },

    // 用户注册
    async register(userData) {
      this.loading = true
      try {
        const response = await authAPI.register(userData)
        return response
      } finally {
        this.loading = false
      }
    },

    // 检查登录状态
    async checkAuth() {
      // 先检查本地存储
      const localIsLoggedIn = localStorage.getItem('isLoggedIn')
      const localUser = localStorage.getItem('user')
      
      if (localIsLoggedIn === 'true' && localUser) {
        this.user = JSON.parse(localUser)
        this.isLoggedIn = true
      }

      try {
        const response = await authAPI.check()
        
        if (response.logged_in && response.user) {
          this.user = response.user
          this.isLoggedIn = true
          
          // 更新本地存储
          localStorage.setItem('isLoggedIn', 'true')
          localStorage.setItem('user', JSON.stringify(response.user))
        } else {
          this.clearAuth()
        }
        
        return this.isLoggedIn
      } catch (error) {
        console.error('检查登录状态失败:', error)
        this.clearAuth()
        return false
      }
    },

    // 获取用户信息
    async fetchProfile() {
      try {
        const response = await authAPI.getProfile()
        
        if (response.success && response.user) {
          this.user = response.user
          localStorage.setItem('user', JSON.stringify(response.user))
        }
        
        return response
      } catch (error) {
        console.error('获取用户信息失败:', error)
        throw error
      }
    },

    // 用户登出
    async logout() {
      try {
        await authAPI.logout()
      } catch (error) {
        console.error('登出请求失败:', error)
      } finally {
        this.clearAuth()
      }
    },

    // 清除认证信息
    clearAuth() {
      this.user = null
      this.isLoggedIn = false
      localStorage.removeItem('isLoggedIn')
      localStorage.removeItem('user')
    }
  }
})
