"""
预算检查API蓝图
"""
import logging
import sys
import os
from flask import Blueprint, request, jsonify, g
import sqlite3
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.database_config import MAIN_DB_PATH

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

bp = Blueprint('budget_check', __name__, url_prefix='/api')

# 预算阈值配置
WARNING_THRESHOLD = 0.8  # 80%预警
DANGER_THRESHOLD = 1.0   # 100%危险

def get_db_connection():
    """获取数据库连接"""
    if 'db' not in g:
        g.db = sqlite3.connect(MAIN_DB_PATH)
        g.db.row_factory = sqlite3.Row
    return g.db

@bp.route('/budget-check', methods=['POST'])
def check_budget_after_transaction():
    """检查交易后的预算情况"""
    try:
        data = request.json
        logger.info(f"检查预算: {data}")

        # 验证必填字段
        required_fields = ['type', 'category', 'amount', 'currency', 'date']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必填字段: {field}'}), 400

        # 只检查支出交易
        if data['type'] != 'expense':
            return jsonify({'success': True, 'notification': None})

        # 检查预算
        notification = _check_budget_direct(data['category'], data)

        return jsonify({
            'success': True,
            'notification': notification
        })

    except Exception as e:
        logger.error(f"检查预算时出错: {str(e)}")
        return jsonify({'error': str(e)}), 500

def _check_budget_direct(category, transaction_data):
    """
    直接通过数据库查询检查预算

    Args:
        category (str): 交易类别
        transaction_data (dict): 交易数据

    Returns:
        dict: 通知信息，如果不需要通知则返回None
    """
    try:
        # 连接数据库
        conn = get_db_connection()

        # 获取该类别的预算
        budgets = conn.execute(
            'SELECT * FROM budgets WHERE category = ?',
            (category,)
        ).fetchall()

        if not budgets:
            conn.close()
            return None

        # 获取预算信息
        budget_dict = dict(budgets[0]) if budgets else None
        if not budget_dict:
            conn.close()
            return None

        # 获取该类别在预算时间范围内的交易
        transactions = conn.execute('''
            SELECT * FROM transactions
            WHERE type = "expense"
            AND category = ?
            AND date >= ?
            AND date <= ?
            ORDER BY date DESC
        ''', (category, budget_dict['start_date'], budget_dict['end_date'])).fetchall()

        conn.close()

        # 转换为字典格式
        transaction_dicts = [dict(tx) for tx in transactions]

        # 检查预算
        notification = _check_budget(budget_dict, transaction_dicts)
        return notification

    except Exception as e:
        logger.error(f"直接检查预算时出错: {str(e)}")
        return None

def _check_budget(budget, transactions):
    """
    检查单个预算

    Args:
        budget (dict): 预算信息
        transactions (list): 交易列表

    Returns:
        dict: 通知信息，如果不需要通知则返回None
    """
    try:
        # 计算预算使用情况
        usage = _calculate_budget_usage(budget, transactions)

        # 判断是否需要通知
        if usage['percentage'] >= DANGER_THRESHOLD:
            return {
                'type': 'danger',
                'budget': budget,
                'usage': usage,
                'message': _format_danger_message(budget, usage)
            }
        elif usage['percentage'] >= WARNING_THRESHOLD:
            return {
                'type': 'warning',
                'budget': budget,
                'usage': usage,
                'message': _format_warning_message(budget, usage)
            }

    except Exception as e:
        logger.error(f"检查预算 {budget.get('id')} 时出错: {str(e)}")

    return None

def _calculate_budget_usage(budget, transactions):
    """计算预算使用情况"""
    start_date = datetime.fromisoformat(budget['start_date'])
    end_date = datetime.fromisoformat(budget['end_date'])
    category = budget['category']
    budget_amount = float(budget['amount'])

    # 筛选相关交易
    relevant_transactions = []
    for tx in transactions:
        tx_date = datetime.fromisoformat(tx['date'])
        if (tx['type'] == 'expense' and
            tx['category'] == category and
            start_date <= tx_date <= end_date):
            relevant_transactions.append(tx)

    # 计算总支出
    total_spent = sum(float(tx['amount']) for tx in relevant_transactions)

    # 计算使用百分比
    percentage = total_spent / budget_amount if budget_amount > 0 else 0

    return {
        'total_spent': total_spent,
        'budget_amount': budget_amount,
        'remaining': budget_amount - total_spent,
        'percentage': percentage,
        'transaction_count': len(relevant_transactions)
    }

def _format_warning_message(budget, usage):
    """格式化预警消息"""
    percentage = int(usage['percentage'] * 100)
    return f"""⚠️ 预算预警通知

类别: {budget['category']}
预算金额: {budget['amount']} {budget['currency']}
已使用: {usage['total_spent']:.2f} {budget['currency']}
使用率: {percentage}%

您的预算使用已达到{percentage}%，请注意控制支出。"""

def _format_danger_message(budget, usage):
    """格式化危险消息"""
    percentage = int(usage['percentage'] * 100)
    over_amount = usage['total_spent'] - usage['budget_amount']
    return f"""🚨 预算超出警告

类别: {budget['category']}
预算金额: {budget['amount']} {budget['currency']}
已使用: {usage['total_spent']:.2f} {budget['currency']}
超出金额: {over_amount:.2f} {budget['currency']}
使用率: {percentage}%

您的预算已超出{over_amount:.2f} {budget['currency']}，建议立即调整支出计划。"""
