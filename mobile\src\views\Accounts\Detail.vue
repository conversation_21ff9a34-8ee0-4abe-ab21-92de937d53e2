<template>
  <div class="account-detail-page">
    <van-nav-bar
      :title="account?.name || '账户详情'"
      left-arrow
      @click-left="$router.back()"
      fixed
      placeholder
    >
      <template #right>
        <van-icon name="edit" size="18" @click="editAccount" />
      </template>
    </van-nav-bar>

    <div class="page-container">
      <div v-if="loading" class="loading-container">
        <van-loading size="24px" />
        <span>加载中...</span>
      </div>

      <div v-else-if="account">
        <!-- 账户信息卡片 -->
        <div class="account-info-card">
          <div class="account-header">
            <div class="account-icon" :class="account.type">
              {{ getAccountIcon(account.type) }}
            </div>
            <div class="account-details">
              <h3>{{ account.name }}</h3>
              <p>{{ formatAccountType(account.type) }} • {{ account.currency }}</p>
            </div>
          </div>
          
          <div class="balance-info">
            <div class="balance-item">
              <span class="label">当前余额</span>
              <span class="amount current">{{ formatCurrency(account.current_balance, account.currency) }}</span>
            </div>
            <div class="balance-item">
              <span class="label">初始余额</span>
              <span class="amount">{{ formatCurrency(account.initial_balance, account.currency) }}</span>
            </div>
          </div>

          <div class="stats-row">
            <div class="stat-item">
              <span class="value income">+{{ formatCurrency(account.income || 0, account.currency) }}</span>
              <span class="label">总收入</span>
            </div>
            <div class="stat-item">
              <span class="value expense">-{{ formatCurrency(account.expense || 0, account.currency) }}</span>
              <span class="label">总支出</span>
            </div>
          </div>
        </div>

        <!-- 账户描述 -->
        <van-cell-group v-if="account.description" inset title="账户描述">
          <van-cell :value="account.description" />
        </van-cell-group>

        <!-- 最近交易 -->
        <van-cell-group inset title="最近交易">
          <div v-if="transactionsLoading" class="loading-container small">
            <van-loading size="16px" />
            <span>加载交易记录...</span>
          </div>
          
          <div v-else-if="recentTransactions.length === 0" class="empty-transactions">
            <p>暂无交易记录</p>
          </div>
          
          <van-cell
            v-else
            v-for="transaction in recentTransactions"
            :key="transaction.id"
            :title="transaction.description || transaction.category"
            :label="formatDate(transaction.date)"
            :value="formatTransactionAmount(transaction)"
            is-link
            @click="viewTransaction(transaction)"
          >
            <template #icon>
              <div class="transaction-icon" :class="transaction.type">
                {{ transaction.type === 'income' ? '💰' : '💸' }}
              </div>
            </template>
          </van-cell>
          
          <van-cell
            v-if="recentTransactions.length > 0"
            title="查看全部交易"
            is-link
            @click="viewAllTransactions"
          />
        </van-cell-group>
      </div>

      <div v-else class="error-container">
        <div class="error-icon">❌</div>
        <p>账户不存在</p>
        <van-button type="primary" size="small" @click="$router.back()">
          返回
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAccountsStore } from '@/stores/accounts'
import { useTransactionsStore } from '@/stores/transactions'
import { formatCurrency } from '@/utils/format'
import { showToast } from 'vant'

const router = useRouter()
const route = useRoute()
const accountsStore = useAccountsStore()
const transactionsStore = useTransactionsStore()

// 响应式数据
const loading = ref(false)
const transactionsLoading = ref(false)
const recentTransactions = ref([])

// 计算属性
const accountId = computed(() => route.params.id)
const account = computed(() => 
  accountsStore.accounts.find(acc => acc.id === accountId.value)
)

// 初始化
onMounted(async () => {
  await loadAccountDetail()
  await loadRecentTransactions()
})

// 加载账户详情
const loadAccountDetail = async () => {
  loading.value = true
  try {
    // 如果store中没有账户数据，先获取
    if (accountsStore.accounts.length === 0) {
      await accountsStore.fetchAccountsWithBalances()
    }
    
    // 如果还是找不到账户，可能是无效ID
    if (!account.value) {
      showToast({
        message: '账户不存在',
        type: 'fail'
      })
    }
  } catch (error) {
    console.error('加载账户详情失败:', error)
    showToast({
      message: '加载账户详情失败',
      type: 'fail'
    })
  } finally {
    loading.value = false
  }
}

// 加载最近交易
const loadRecentTransactions = async () => {
  if (!accountId.value) return
  
  transactionsLoading.value = true
  try {
    // 获取该账户的最近5笔交易
    const transactions = await transactionsStore.fetchTransactions({
      account: accountId.value,
      limit: 5
    })
    recentTransactions.value = transactions || []
  } catch (error) {
    console.error('加载交易记录失败:', error)
    recentTransactions.value = []
  } finally {
    transactionsLoading.value = false
  }
}

// 格式化账户类型
const formatAccountType = (type) => {
  const typeMap = {
    bank: '银行账户',
    cash: '现金',
    credit: '信用卡',
    investment: '投资账户',
    ewallet: '电子钱包',
    other: '其他'
  }
  return typeMap[type] || type
}

// 获取账户图标
const getAccountIcon = (type) => {
  const icons = {
    bank: '🏦',
    cash: '💵',
    credit: '💳',
    investment: '📈',
    ewallet: '🏪',
    other: '💰'
  }
  return icons[type] || '💰'
}

// 格式化日期
const formatDate = (dateStr) => {
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  })
}

// 格式化交易金额
const formatTransactionAmount = (transaction) => {
  const amount = formatCurrency(transaction.amount, transaction.currency)
  return transaction.type === 'income' ? `+${amount}` : `-${amount}`
}

// 编辑账户
const editAccount = () => {
  if (account.value) {
    router.push(`/account/edit/${account.value.id}`)
  }
}

// 查看交易详情
const viewTransaction = (transaction) => {
  router.push(`/transaction/detail/${transaction.id}`)
}

// 查看全部交易
const viewAllTransactions = () => {
  router.push({
    path: '/transactions',
    query: { account: accountId.value }
  })
}
</script>

<style scoped>
.account-detail-page {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.page-container {
  padding-bottom: 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #969799;
  gap: 12px;
}

.loading-container.small {
  padding: 20px;
  font-size: 14px;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #969799;
  gap: 16px;
}

.error-icon {
  font-size: 48px;
}

.account-info-card {
  background: white;
  margin: 16px;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.account-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.account-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  background: #f0f9ff;
}

.account-details h3 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 600;
  color: #323233;
}

.account-details p {
  margin: 0;
  font-size: 14px;
  color: #969799;
}

.balance-info {
  margin-bottom: 24px;
}

.balance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.balance-item:last-child {
  margin-bottom: 0;
}

.balance-item .label {
  font-size: 14px;
  color: #646566;
}

.balance-item .amount {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
}

.balance-item .amount.current {
  font-size: 24px;
  color: #1989fa;
}

.stats-row {
  display: flex;
  gap: 24px;
  padding-top: 16px;
  border-top: 1px solid #ebedf0;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-item .value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-item .value.income {
  color: #07c160;
}

.stat-item .value.expense {
  color: #ee0a24;
}

.stat-item .label {
  font-size: 12px;
  color: #969799;
}

.empty-transactions {
  text-align: center;
  padding: 40px 20px;
  color: #969799;
}

.transaction-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 16px;
  background: #f0f9ff;
}

:deep(.van-cell-group__title) {
  padding: 16px 16px 8px;
  font-size: 14px;
  font-weight: 600;
  color: #323233;
}
</style>
