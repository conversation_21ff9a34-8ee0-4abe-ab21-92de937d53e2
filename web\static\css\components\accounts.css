/* 账户管理样式 */

.accounts-container {
    background-color: var(--color-card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.accounts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.accounts-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--color-text);
}

/* 表单样式 */
.account-form {
    background-color: var(--color-form-bg);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

/* 账户表格 */
.account-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: var(--spacing-md);
    box-shadow: var(--shadow-sm);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.account-table th {
    text-align: left;
    padding: 15px;
    background-color: var(--color-table-header);
    color: var(--color-text);
    font-weight: 600;
    font-size: var(--font-size-sm);
    border-bottom: 1px solid var(--color-border);
}

.account-table td {
    padding: 15px;
    border-bottom: 1px solid var(--color-border);
    font-size: var(--font-size-base);
}

.account-table tr:hover {
    background-color: var(--color-hover);
}

.account-table tr:last-child td {
    border-bottom: none;
}

/* 账户行样式 */
.account-name {
    font-weight: 500;
}

.account-type {
    color: var(--color-text-light);
}

.account-currency {
    color: var(--color-text-light);
    font-size: var(--font-size-sm);
}

.account-initial-balance {
    color: var(--color-text);
    font-family: monospace;
    font-size: var(--font-size-base);
}

.account-balance {
    color: var(--color-primary);
    font-weight: 600;
    font-family: monospace;
    font-size: var(--font-size-base);
}

.account-actions {
    text-align: right;
    white-space: nowrap;
}

/* 操作按钮 */
.action-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.3s;
    margin-left: 5px;
}

.action-btn:hover {
    background-color: var(--color-hover);
}

.edit-btn .action-icon {
    color: var(--color-primary);
}

.delete-btn .action-icon {
    color: var(--color-danger);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .account-table th:nth-child(3),
    .account-table td:nth-child(3) {
        display: none;
    }
}

@media (max-width: 480px) {
    .account-table th:nth-child(4),
    .account-table td:nth-child(4) {
        display: none;
    }
}
