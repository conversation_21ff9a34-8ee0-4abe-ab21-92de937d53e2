/*******************************************
 *          同步管理器样式
 *******************************************/

/* 同步状态 */
.sync-status {
  display: flex;
  align-items: center;
  font-size: var(--font-size-xs);
  color: var(--color-text-light);
  margin-top: var(--spacing-xs);
}

.sync-icon {
  margin-right: var(--spacing-xs);
}

/* 同步按钮容器 */
.sync-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

/* 同步按钮 */
.sync-button {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s;
  font-size: var(--font-size-sm);
  color: var(--color-text);
}

.sync-button:hover {
  background-color: var(--color-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.sync-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.sync-button .sync-icon {
  font-size: var(--font-size-md);
  margin-right: var(--spacing-sm);
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: var(--spacing-sm);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 消息提示 */
.message {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius);
  color: white;
  font-size: var(--font-size-sm);
  z-index: 1000;
  opacity: 1;
  transition: opacity 0.5s;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.message.success {
  background-color: var(--color-success);
}

.message.error {
  background-color: var(--color-danger);
}

/* 备份选择对话框 */
.backup-dialog,
.mode-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: var(--color-card);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  width: 90%;
  max-width: 500px;
}

.backup-dialog h3,
.mode-dialog h3 {
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-md);
  color: var(--color-text);
}

.backup-list {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 300px;
  overflow-y: auto;
}

.backup-list li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
}

.backup-list li:last-child {
  border-bottom: none;
}

.backup-date {
  font-size: var(--font-size-sm);
}

.restore-button,
.delete-button {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-size: var(--font-size-xs);
  cursor: pointer;
  border: none;
}

.restore-button {
  background-color: var(--color-primary);
  color: white;
  margin-right: var(--spacing-xs);
}

.delete-button {
  background-color: var(--color-danger);
  color: white;
}

.close-button {
  display: block;
  margin: var(--spacing-md) auto 0;
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: var(--color-text-light);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
}

/* 存储模式选择 */
.mode-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s;
}

.mode-button.active {
  border-color: var(--color-primary);
  background-color: rgba(var(--color-primary-rgb), 0.1);
}

.mode-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.mode-icon {
  font-size: 24px;
  margin-bottom: var(--spacing-sm);
}

.mode-name {
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
}

.mode-desc {
  font-size: var(--font-size-xs);
  color: var(--color-text-light);
  text-align: center;
}

/* 设置页面 */
#settingsPage {
  padding: var(--spacing-lg);
}

#settingsPage h2 {
  margin-top: 0;
  margin-bottom: var(--spacing-lg);
  font-size: var(--font-size-lg);
  color: var(--color-text);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sync-buttons {
    flex-direction: column;
  }
  
  .backup-dialog,
  .mode-dialog {
    width: 95%;
    max-width: none;
  }
}
