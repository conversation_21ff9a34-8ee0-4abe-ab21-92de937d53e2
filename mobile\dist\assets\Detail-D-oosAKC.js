import{a as Y,m as x,h as A,s as c,g as G,c as m,e as n,w as s,f as r,d as o,p as H,v as U,t as u,n as p,q as V,o as d,i as _}from"./index--MNqwREY.js";import{u as E}from"./transactions-F5TqZ8Qm.js";import{useAccountsStore as I}from"./accounts-bk1VVrk3.js";import{t as J}from"./transactions-CrdnwW_k.js";import{f as L,a as D}from"./format-wz8GKlWC.js";import{_ as q}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{s as j}from"./function-call-w7FI8rRn.js";const F={class:"transaction-detail-page"},K={key:0,class:"loading-container"},O={key:1,class:"page-container"},Q={class:"amount-info"},W={class:"amount-label"},X={class:"amount-value"},Z={class:"amount-type"},tt={class:"amount-icon"},at={class:"attachment-content"},et={class:"action-buttons"},nt={key:2,class:"error-container"},ot={__name:"Detail",setup(st){const v=G(),h=A(),b=E(),C=I(),f=Y(!0),y=Y(!1),a=Y(null);x(async()=>{await w()});const w=async()=>{f.value=!0;try{const e=h.params.id;await Promise.all([C.fetchAccounts(),S(e)])}catch(e){console.error("加载数据失败:",e),c("加载数据失败")}finally{f.value=!1}},S=async e=>{try{let t=b.transactions.find(l=>l.id==e);if(!t){const l=await J.getTransaction(e);t=l.data||l}t?a.value=t:(c("交易不存在"),v.back())}catch(t){console.error("加载交易失败:",t),c("加载交易失败"),v.back()}},N=e=>{const t=C.accounts.find(l=>l.id===e);return t?`${t.name} (${t.type})`:"未知账户"},T=e=>({MYR:"MYR - 马来西亚林吉特",USD:"USD - 美元",CNY:"CNY - 人民币",EUR:"EUR - 欧元",GBP:"GBP - 英镑",JPY:"JPY - 日元",SGD:"SGD - 新加坡元"})[e]||e,M=()=>{v.push(`/transaction/edit/${a.value.id}`)},$=async()=>{try{await j({title:"确认删除",message:"确定要删除这条交易记录吗？此操作不可撤销。"}),y.value=!0,await b.deleteTransaction(a.value.id),c({message:"交易删除成功",type:"success"}),v.replace("/transactions")}catch(e){e!=="cancel"&&(console.error("删除交易失败:",e),c({message:e.message||"删除交易失败",type:"fail"}))}finally{y.value=!1}};return(e,t)=>{const l=r("van-icon"),z=r("van-nav-bar"),B=r("van-loading"),P=r("van-tag"),i=r("van-cell"),g=r("van-cell-group"),k=r("van-button");return d(),m("div",F,[n(z,{title:"交易详情","left-arrow":"",onClickLeft:t[0]||(t[0]=R=>e.$router.back()),fixed:"",placeholder:""},{right:s(()=>[n(l,{name:"edit",size:"18",onClick:M})]),_:1}),f.value?(d(),m("div",K,[n(B,{size:"24px"}),t[2]||(t[2]=o("span",null,"加载中...",-1))])):a.value?(d(),m("div",O,[o("div",{class:V(["amount-card",a.value.type])},[o("div",Q,[o("div",W,u(a.value.type==="income"?"收入金额":"支出金额"),1),o("div",X,u(p(L)(a.value.amount,a.value.currency)),1),o("div",Z,[n(P,{type:a.value.type==="income"?"success":"danger",size:"medium"},{default:s(()=>[_(u(a.value.type==="income"?"收入":"支出"),1)]),_:1},8,["type"])])]),o("div",tt,u(a.value.type==="income"?"💰":"💸"),1)],2),n(g,{inset:"",title:"基本信息"},{default:s(()=>[n(i,{title:"交易描述",value:a.value.description},null,8,["value"]),n(i,{title:"交易日期",value:p(D)(a.value.date,"YYYY-MM-DD")},null,8,["value"]),n(i,{title:"交易分类",value:a.value.category},null,8,["value"]),n(i,{title:"交易账户",value:N(a.value.account)},null,8,["value"]),n(i,{title:"货币类型",value:T(a.value.currency)},null,8,["value"])]),_:1}),a.value.attachment?(d(),H(g,{key:0,inset:"",title:"备注信息"},{default:s(()=>[n(i,null,{default:s(()=>[o("div",at,u(a.value.attachment),1)]),_:1})]),_:1})):U("",!0),n(g,{inset:"",title:"时间信息"},{default:s(()=>[n(i,{title:"创建时间",value:p(D)(a.value.created_at,"YYYY-MM-DD HH:mm")},null,8,["value"]),n(i,{title:"更新时间",value:p(D)(a.value.updated_at,"YYYY-MM-DD HH:mm")},null,8,["value"])]),_:1}),o("div",et,[n(k,{type:"primary",size:"large",onClick:M,block:""},{default:s(()=>t[3]||(t[3]=[_(" 编辑交易 ")])),_:1,__:[3]}),n(k,{type:"danger",size:"large",plain:"",onClick:$,loading:y.value,block:""},{default:s(()=>t[4]||(t[4]=[_(" 删除交易 ")])),_:1,__:[4]},8,["loading"])])])):(d(),m("div",nt,[t[6]||(t[6]=o("div",{class:"error-icon"},"❌",-1)),t[7]||(t[7]=o("p",null,"交易不存在或已被删除",-1)),n(k,{type:"primary",onClick:t[1]||(t[1]=R=>e.$router.back())},{default:s(()=>t[5]||(t[5]=[_(" 返回 ")])),_:1,__:[5]})]))])}}},mt=q(ot,[["__scopeId","data-v-65bba5a3"]]);export{mt as default};
