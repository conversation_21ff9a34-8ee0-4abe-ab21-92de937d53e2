<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 钱管家</title>
    <link rel="stylesheet" href="../../assets/css/components/auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="auth-container">
        <!-- 登录卡片 -->
        <div class="login-container" id="loginCard">
            <div class="left-panel">
                <div class="welcome-content">
                    <div class="welcome-icon">钱</div>
                    <h1 class="welcome-title">欢迎回来</h1>
                    <p class="welcome-subtitle">钱管家助您轻松管理个人财务</p>
                    <ul class="feature-list">
                        <li>智能记账分析</li>
                        <li>预算管理提醒</li>
                        <li>财务目标规划</li>
                        <li>数据安全保护</li>
                    </ul>
                </div>
            </div>

            <div class="right-panel">
                <div class="login-header">
                    <h2 class="login-title">登录账户</h2>
                    <p class="login-subtitle">请输入您的登录信息</p>
                </div>

                <!-- 提示信息 -->
                <div id="authAlert" class="auth-alert"></div>

                <!-- 登录表单 -->
                <form id="loginForm" class="auth-form">
                    <div class="form-group">
                        <label for="username" class="form-label">用户名或邮箱</label>
                        <input type="text" id="username" name="username" class="form-input" placeholder="请输入用户名或邮箱" required>
                        <div class="auth-form-error" id="usernameError"></div>
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">密码</label>
                        <input type="password" id="password" name="password" class="form-input" placeholder="请输入密码" required>
                        <div class="auth-form-error" id="passwordError"></div>
                    </div>

                    <div class="form-options">
                        <label class="remember-me">
                            <input type="checkbox" id="rememberMe" name="rememberMe"> 记住我
                        </label>
                        <a href="#" class="forgot-link">忘记密码？</a>
                    </div>

                    <button type="submit" class="login-btn" id="loginBtn">
                        <span class="loading-spinner"></span>
                        <span class="btn-text">登录</span>
                    </button>
                </form>

                <div class="divider">
                    <span>或</span>
                </div>

                <div class="register-link">
                    还没有账户？<a href="#" id="showRegister">立即注册</a>
                </div>
            </div>
        </div>

        <!-- 注册弹窗 -->
        <div class="modal-overlay" id="registerModal" style="display: none;">
            <div class="modal-container">
                <div class="modal-header">
                    <h2 class="modal-title">
                        <i class="fas fa-user-plus" style="margin-right: 10px; font-size: 22px;"></i>
                        注册账户
                    </h2>
                    <button class="modal-close" id="closeRegisterModal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <!-- 提示信息 -->
                <div id="registerAlert" class="auth-alert"></div>

                <!-- 注册表单 -->
                <form id="registerForm" class="modal-form">
                    <div class="form-group">
                        <label for="regUsername" class="form-label">
                            <i class="fas fa-user" style="margin-right: 6px; color: #667eea;"></i>
                            用户名
                        </label>
                        <input type="text" id="regUsername" name="username" class="form-input" placeholder="请输入用户名" required>
                        <div class="auth-form-error" id="regUsernameError"></div>
                    </div>

                    <div class="form-group">
                        <label for="regEmail" class="form-label">
                            <i class="fas fa-envelope" style="margin-right: 6px; color: #667eea;"></i>
                            邮箱
                        </label>
                        <input type="email" id="regEmail" name="email" class="form-input" placeholder="请输入邮箱" required>
                        <div class="auth-form-error" id="regEmailError"></div>
                    </div>

                    <div class="form-group">
                        <label for="regDisplayName" class="form-label">
                            <i class="fas fa-id-card" style="margin-right: 6px; color: #667eea;"></i>
                            显示名称
                        </label>
                        <input type="text" id="regDisplayName" name="display_name" class="form-input" placeholder="请输入显示名称（可选）">
                        <div class="auth-form-error" id="regDisplayNameError"></div>
                    </div>

                    <div class="form-group">
                        <label for="regPassword" class="form-label">
                            <i class="fas fa-lock" style="margin-right: 6px; color: #667eea;"></i>
                            密码
                        </label>
                        <input type="password" id="regPassword" name="password" class="form-input" placeholder="请输入密码" required>
                        <div class="password-strength" id="passwordStrength">
                            <div class="password-strength-bar">
                                <div class="password-strength-fill" id="passwordStrengthFill"></div>
                            </div>
                            <div class="password-strength-text" id="passwordStrengthText"></div>
                        </div>
                        <div class="auth-form-error" id="regPasswordError"></div>
                    </div>

                    <div class="form-group">
                        <label for="regConfirmPassword" class="form-label">
                            <i class="fas fa-lock" style="margin-right: 6px; color: #667eea;"></i>
                            确认密码
                        </label>
                        <input type="password" id="regConfirmPassword" name="confirmPassword" class="form-input" placeholder="请再次输入密码" required>
                        <div class="auth-form-error" id="regConfirmPasswordError"></div>
                    </div>

                    <button type="submit" class="login-btn" id="registerBtn">
                        <span class="loading-spinner"></span>
                        <span class="btn-text">
                            <i class="fas fa-user-plus" style="margin-right: 8px;"></i>
                            注册账户
                        </span>
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script>
        // DOM元素
        const loginCard = document.getElementById('loginCard');
        const registerModal = document.getElementById('registerModal');
        const showRegisterLink = document.getElementById('showRegister');
        const closeRegisterModal = document.getElementById('closeRegisterModal');
        const loginForm = document.getElementById('loginForm');
        const registerForm = document.getElementById('registerForm');

        // 显示注册弹窗
        function showRegisterModal() {
            registerModal.style.display = 'flex';
            setTimeout(() => {
                registerModal.classList.add('show');
            }, 10);
            clearAlerts();
        }

        // 隐藏注册弹窗
        function hideRegisterModal() {
            registerModal.classList.remove('show');
            setTimeout(() => {
                registerModal.style.display = 'none';
            }, 300);
            clearAlerts();
            clearAllErrors();
            registerForm.reset();
        }

        // 显示提示信息
        function showAlert(message, type = 'info', isRegister = false) {
            const alertElement = isRegister ? document.getElementById('registerAlert') : document.getElementById('authAlert');
            alertElement.textContent = message;
            alertElement.className = `auth-alert ${type} show`;
        }

        // 清除提示信息
        function clearAlerts() {
            document.getElementById('authAlert').className = 'auth-alert';
            document.getElementById('registerAlert').className = 'auth-alert';
        }

        // 显示字段错误
        function showFieldError(fieldId, message) {
            const field = document.getElementById(fieldId);
            const errorElement = document.getElementById(fieldId + 'Error');
            field.classList.add('error');
            errorElement.textContent = message;
            errorElement.classList.add('show');
        }

        // 清除字段错误
        function clearFieldError(fieldId) {
            const field = document.getElementById(fieldId);
            const errorElement = document.getElementById(fieldId + 'Error');
            field.classList.remove('error');
            errorElement.classList.remove('show');
        }

        // 清除所有错误
        function clearAllErrors() {
            const errorFields = ['username', 'password', 'regUsername', 'regEmail', 'regDisplayName', 'regPassword', 'regConfirmPassword'];
            errorFields.forEach(clearFieldError);
        }

        // 设置按钮加载状态
        function setButtonLoading(buttonId, loading = true) {
            const button = document.getElementById(buttonId);
            if (loading) {
                button.classList.add('loading');
                button.disabled = true;
            } else {
                button.classList.remove('loading');
                button.disabled = false;
            }
        }

        // 验证密码强度
        function checkPasswordStrength(password) {
            const strengthElement = document.getElementById('passwordStrength');
            const fillElement = document.getElementById('passwordStrengthFill');
            const textElement = document.getElementById('passwordStrengthText');

            if (!password) {
                strengthElement.classList.remove('show');
                return;
            }

            strengthElement.classList.add('show');

            let score = 0;
            let feedback = [];

            // 长度检查
            if (password.length >= 6) score++;
            else feedback.push('至少6位');

            // 字母检查
            if (/[a-zA-Z]/.test(password)) score++;
            else feedback.push('包含字母');

            // 数字检查
            if (/\d/.test(password)) score++;
            else feedback.push('包含数字');

            // 特殊字符检查
            if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score++;

            // 更新显示
            fillElement.className = 'password-strength-fill';
            if (score <= 1) {
                fillElement.classList.add('weak');
                textElement.textContent = '密码强度：弱 (' + feedback.join('、') + ')';
            } else if (score <= 2) {
                fillElement.classList.add('medium');
                textElement.textContent = '密码强度：中等';
            } else {
                fillElement.classList.add('strong');
                textElement.textContent = '密码强度：强';
            }

            return score >= 2;
        }

        // 事件监听器
        showRegisterLink.addEventListener('click', (e) => {
            e.preventDefault();
            showRegisterModal();
        });

        closeRegisterModal.addEventListener('click', (e) => {
            e.preventDefault();
            hideRegisterModal();
        });

        // 点击弹窗背景关闭
        registerModal.addEventListener('click', (e) => {
            if (e.target === registerModal) {
                hideRegisterModal();
            }
        });

        // ESC键关闭弹窗
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && registerModal.classList.contains('show')) {
                hideRegisterModal();
            }
        });

        // 密码强度检查
        document.getElementById('regPassword').addEventListener('input', (e) => {
            checkPasswordStrength(e.target.value);
        });

        // 登录表单提交
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            clearAllErrors();
            clearAlerts();

            const formData = new FormData(loginForm);
            const data = Object.fromEntries(formData);

            // 基本验证
            if (!data.username.trim()) {
                showFieldError('username', '请输入用户名或邮箱');
                return;
            }

            if (!data.password) {
                showFieldError('password', '请输入密码');
                return;
            }

            setButtonLoading('loginBtn', true);

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('登录成功，正在跳转...', 'success');
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                } else {
                    showAlert(result.error || '登录失败', 'error');
                }
            } catch (error) {
                console.error('登录错误:', error);
                showAlert('网络错误，请稍后重试', 'error');
            } finally {
                setButtonLoading('loginBtn', false);
            }
        });

        // 注册表单提交
        registerForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            clearAllErrors();
            clearAlerts();

            const formData = new FormData(registerForm);
            const data = Object.fromEntries(formData);

            // 基本验证
            if (!data.username.trim()) {
                showFieldError('regUsername', '请输入用户名');
                return;
            }

            if (data.username.length < 3 || data.username.length > 20) {
                showFieldError('regUsername', '用户名长度必须在3-20位之间');
                return;
            }

            if (!data.email.trim()) {
                showFieldError('regEmail', '请输入邮箱');
                return;
            }

            if (!data.password) {
                showFieldError('regPassword', '请输入密码');
                return;
            }

            if (!checkPasswordStrength(data.password)) {
                showFieldError('regPassword', '密码强度不够');
                return;
            }

            if (data.password !== data.confirmPassword) {
                showFieldError('regConfirmPassword', '两次输入的密码不一致');
                return;
            }

            // 设置默认显示名称
            if (!data.display_name.trim()) {
                data.display_name = data.username;
            }

            setButtonLoading('registerBtn', true);

            try {
                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('注册成功！请登录', 'success', true);
                    setTimeout(() => {
                        hideRegisterModal();
                        document.getElementById('username').value = data.username;
                        showAlert('注册成功！请使用新账户登录', 'success');
                    }, 1500);
                } else {
                    showAlert(result.error || '注册失败', 'error', true);
                }
            } catch (error) {
                console.error('注册错误:', error);
                showAlert('网络错误，请稍后重试', 'error', true);
            } finally {
                setButtonLoading('registerBtn', false);
            }
        });

        // 页面加载时检查登录状态
        window.addEventListener('DOMContentLoaded', async () => {
            try {
                const response = await fetch('/api/auth/check');
                const result = await response.json();

                if (result.success && result.logged_in) {
                    // 已登录，跳转到主页
                    window.location.href = '/';
                }
            } catch (error) {
                console.error('检查登录状态失败:', error);
            }
        });
    </script>
</body>
</html>
