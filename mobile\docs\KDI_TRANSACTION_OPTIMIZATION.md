# KDI账户交易记录优化说明

## 更新内容

### 1. 分类配置优化 (`mobile/src/config/categories.js`)

#### 新增KDI专用分类
- **KDI收入分类**: `['入金', '收益']`
- **KDI支出分类**: `['出金']`

#### 优化分类选择逻辑
- 新增 `getCategoryOptions(type, accountName)` 函数
- 当账户名为 'KDI' 时，自动使用KDI专用分类
- 普通账户继续使用标准分类

#### 新增KDI分类图标和颜色
- 入金: 💰 (#4CAF50)
- 收益: 📈 (#00BCD4)  
- 出金: 💸 (#FF5722)

### 2. 添加交易页面优化 (`mobile/src/views/Transactions/Add.vue`)

#### KDI账户特殊逻辑
- **动态分类显示**: 根据选择的账户自动切换分类选项
- **出金账户选择**: 当KDI账户选择"出金"时，显示出金账户选择器
- **账户过滤**: 出金账户只显示马币的银行账户和电子钱包

#### 新增响应式数据
```javascript
const showWithdrawAccountPicker = ref(false) // KDI出金账户选择器
form.withdrawAccount = '' // KDI出金目标账户
```

#### 新增计算属性
- `isKDIAccount`: 判断当前选择的账户是否为KDI
- `showWithdrawAccountField`: 判断是否显示出金账户选择器
- `withdrawAccountColumns`: 出金账户选择器的选项
- `selectedWithdrawAccountName`: 显示选择的出金账户名称

#### 表单验证增强
- 当选择KDI出金时，验证出金账户是否已选择

#### 双向交易创建
- 当KDI出金时，调用 `transactionsStore.createKDIWithdrawTransaction()`
- 自动创建两笔交易记录：
  1. KDI账户的支出记录（出金）
  2. 目标账户的收入记录（来自KDI出金）

### 3. 编辑交易页面优化 (`mobile/src/views/Transactions/Edit.vue`)

#### 分类限制
- 同样支持KDI账户的分类限制
- 编辑时根据账户类型动态显示分类选项

#### 注意事项
- 编辑页面暂不支持KDI出金的双向交易编辑
- 建议删除原交易后重新创建，以确保数据一致性

### 4. 交易存储优化 (`mobile/src/stores/transactions.js`)

#### 新增KDI出金双向交易方法
```javascript
async createKDIWithdrawTransaction(kdiTransactionData, withdrawAccountId)
```

#### 双向交易逻辑
1. **验证出金账户**: 确保目标账户存在且有效
2. **创建KDI支出记录**: 使用原始交易数据
3. **创建目标收入记录**: 
   - 类型: 'income'
   - 分类: '投资收益'
   - 描述: '来自KDI出金 - [原描述]'
   - 账户: 出金目标账户
   - 金额: 与KDI支出相同
4. **数据同步**: 创建成功后重新获取交易列表

## 使用流程

### KDI收入交易
1. 选择交易类型: 收入
2. 选择账户: KDI
3. 选择分类: 入金 或 收益
4. 填写其他信息并保存

### KDI出金交易
1. 选择交易类型: 支出
2. 选择账户: KDI
3. 选择分类: 出金
4. **自动显示出金账户选择器**
5. 选择出金账户 (只显示马币的银行账户和电子钱包)
6. 填写其他信息并保存
7. **系统自动创建两笔交易记录**

## 技术实现

### 分类动态切换
```javascript
const categoryColumns = computed(() => {
  const selectedAccount = accountsStore.accounts.find(acc => acc.id === form.account)
  const accountName = selectedAccount?.name || ''
  const categoryOptions = getCategoryOptions(form.type, accountName)
  return categoryOptions
})
```

### 出金账户过滤
```javascript
const withdrawAccountColumns = computed(() => {
  return accountsStore.accounts
    .filter(account => 
      account.currency === 'MYR' && 
      (account.type === 'bank' || account.type === 'ewallet') &&
      account.id !== form.account
    )
    .map(account => ({
      text: `${account.name} (${account.type})`,
      value: account.id
    }))
})
```

### 双向交易创建
```javascript
// KDI出金记录
const kdiResponse = await transactionsAPI.createTransaction(kdiTransactionData)

// 目标账户收入记录
const targetTransactionData = {
  type: 'income',
  amount: kdiTransactionData.amount,
  description: `来自KDI出金 - ${kdiTransactionData.description}`,
  date: kdiTransactionData.date,
  category: '投资收益',
  account: withdrawAccountId,
  currency: withdrawAccount.currency,
  attachment: kdiTransactionData.attachment
}
const targetResponse = await transactionsAPI.createTransaction(targetTransactionData)
```

## 文件修改清单

1. `mobile/src/config/categories.js` - 新增KDI专用分类和优化分类选择逻辑
2. `mobile/src/views/Transactions/Add.vue` - 添加KDI出金特殊逻辑和双向交易创建
3. `mobile/src/views/Transactions/Edit.vue` - 添加KDI分类限制
4. `mobile/src/stores/transactions.js` - 新增KDI出金双向交易创建方法

## 测试建议

1. **KDI收入测试**: 验证分类只显示"入金"和"收益"
2. **KDI出金测试**: 验证分类只显示"出金"，且选择出金后显示出金账户选择器
3. **双向交易测试**: 验证KDI出金后自动创建两笔交易记录
4. **普通账户测试**: 验证非KDI账户仍使用标准分类
5. **编辑功能测试**: 验证编辑时分类限制正常工作

## 功能状态

✅ **已完成并测试通过**
- KDI账户分类限制功能正常
- KDI出金双向交易创建功能正常
- 出金账户选择器显示正常
- 表单验证和用户体验良好
