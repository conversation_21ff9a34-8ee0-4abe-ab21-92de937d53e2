#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据迁移相关API蓝图
"""

from flask import Blueprint, request, jsonify, g
import datetime
import logging
import sqlite3
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.database_config import MAIN_DB_PATH

# 创建蓝图
bp = Blueprint('migrate', __name__)
logger = logging.getLogger(__name__)

def get_db_connection():
    """获取数据库连接"""
    if 'db' not in g:
        g.db = sqlite3.connect(MAIN_DB_PATH)
        g.db.row_factory = sqlite3.Row
    return g.db

@bp.route('/', methods=['POST'])
def migrate_data():
    """从前端迁移数据到后端数据库"""
    data = request.json
    logger.info("开始数据迁移")

    conn = get_db_connection()

    # 开始事务
    conn.execute('BEGIN TRANSACTION')

    try:
        # 迁移交易数据
        if 'transactions' in data:
            logger.info(f"迁移交易数据: {len(data['transactions'])}条")
            for tx in data['transactions']:
                # 检查是否已存在
                existing = conn.execute('SELECT id FROM transactions WHERE id = ?', (tx['id'],)).fetchone()
                now = datetime.datetime.now().isoformat()

                if existing:
                    # 更新现有交易
                    conn.execute('''
                    UPDATE transactions
                    SET date = ?, type = ?, category = ?, description = ?, account = ?, amount = ?,
                        currency = ?, attachment = ?, updated_at = ?
                    WHERE id = ?
                    ''', (
                        tx['date'], tx['type'], tx['category'], tx['description'], tx['account'],
                        tx['amount'], tx['currency'], tx.get('attachment', ''), now, tx['id']
                    ))
                else:
                    # 创建新交易
                    conn.execute('''
                    INSERT INTO transactions (id, date, type, category, description, account, amount,
                                            currency, attachment, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        tx['id'], tx['date'], tx['type'], tx['category'], tx['description'],
                        tx['account'], tx['amount'], tx['currency'], tx.get('attachment', ''),
                        tx.get('createdAt', now), now
                    ))

        # 迁移账户数据
        if 'accounts' in data:
            logger.info(f"迁移账户数据: {len(data['accounts'])}条")
            for acc in data['accounts']:
                # 检查是否已存在
                existing = conn.execute('SELECT id FROM accounts WHERE id = ?', (acc['id'],)).fetchone()
                now = datetime.datetime.now().isoformat()

                if existing:
                    # 更新现有账户
                    conn.execute('''
                    UPDATE accounts
                    SET name = ?, type = ?, currency = ?, initial_balance = ?, description = ?,
                        icon = ?, updated_at = ?
                    WHERE id = ?
                    ''', (
                        acc['name'], acc['type'], acc['currency'], acc['initialBalance'],
                        acc.get('description', ''), acc.get('icon', ''), now, acc['id']
                    ))
                else:
                    # 创建新账户
                    conn.execute('''
                    INSERT INTO accounts (id, name, type, currency, initial_balance, description,
                                        icon, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        acc['id'], acc['name'], acc['type'], acc['currency'], acc['initialBalance'],
                        acc.get('description', ''), acc.get('icon', ''),
                        acc.get('createdAt', now), now
                    ))

        # 迁移预算数据
        if 'budgets' in data:
            logger.info(f"迁移预算数据: {len(data['budgets'])}条")
            for budget in data['budgets']:
                # 检查是否已存在
                existing = conn.execute('SELECT id FROM budgets WHERE id = ?', (budget['id'],)).fetchone()
                now = datetime.datetime.now().isoformat()

                if existing:
                    # 更新现有预算
                    conn.execute('''
                    UPDATE budgets
                    SET category = ?, amount = ?, period = ?, start_date = ?, end_date = ?,
                        currency = ?, updated_at = ?
                    WHERE id = ?
                    ''', (
                        budget['category'], budget['amount'], budget['period'],
                        budget['startDate'], budget['endDate'], budget['currency'], now, budget['id']
                    ))
                else:
                    # 创建新预算
                    conn.execute('''
                    INSERT INTO budgets (id, category, amount, period, start_date, end_date,
                                        currency, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        budget['id'], budget['category'], budget['amount'], budget['period'],
                        budget['startDate'], budget['endDate'], budget['currency'],
                        budget.get('createdAt', now), now
                    ))

        # 迁移目标数据
        if 'goals' in data:
            logger.info(f"迁移目标数据: {len(data['goals'])}条")
            for goal in data['goals']:
                # 检查是否已存在
                existing = conn.execute('SELECT id FROM goals WHERE id = ?', (goal['id'],)).fetchone()
                now = datetime.datetime.now().isoformat()

                if existing:
                    # 更新现有目标
                    conn.execute('''
                    UPDATE goals
                    SET name = ?, target_amount = ?, current_amount = ?, target_date = ?,
                        currency = ?, description = ?, icon = ?, updated_at = ?
                    WHERE id = ?
                    ''', (
                        goal['name'], goal['targetAmount'], goal['currentAmount'],
                        goal['targetDate'], goal['currency'], goal.get('description', ''),
                        goal.get('icon', ''), now, goal['id']
                    ))
                else:
                    # 创建新目标
                    conn.execute('''
                    INSERT INTO goals (id, name, target_amount, current_amount, target_date,
                                    currency, description, icon, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        goal['id'], goal['name'], goal['targetAmount'], goal['currentAmount'],
                        goal['targetDate'], goal['currency'], goal.get('description', ''),
                        goal.get('icon', ''), goal.get('createdAt', now), now
                    ))

        # 提交事务
        conn.commit()
        logger.info("数据迁移成功")

        return jsonify({'success': True, 'message': '数据迁移成功'})

    except Exception as e:
        # 回滚事务
        conn.rollback()
        logger.error(f"数据迁移失败: {str(e)}")
        return jsonify({'error': f'数据迁移失败: {str(e)}'}), 500
