/* 目标管理样式 */

.goals-container {
    background-color: var(--color-card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.goals-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.goals-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--color-text);
}

/* 表单样式 */
.goal-form {
    background-color: var(--color-form-bg);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

/* 目标部分标题 */
.goals-section h3 {
    font-size: var(--font-size-md);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--color-text);
    border-bottom: 1px solid var(--color-border);
    padding-bottom: var(--spacing-sm);
}

/* 目标列表 */
.goals-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

/* 目标卡片 */
.goal-card {
    background-color: white;
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
}

.goal-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.goal-header {
    display: flex;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--color-border);
}

.goal-icon {
    font-size: 24px;
    margin-right: var(--spacing-sm);
}

.goal-title {
    flex: 1;
    font-weight: 600;
    font-size: var(--font-size-base);
}

.goal-actions {
    display: flex;
    gap: 5px;
}

.goal-body {
    padding: var(--spacing-md);
}

.goal-progress {
    margin-bottom: var(--spacing-md);
}

.goal-details {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
}

.goal-amount {
    font-family: monospace;
}

.goal-current-amount {
    font-weight: 600;
    color: var(--color-primary);
}

.goal-separator {
    margin: 0 5px;
    color: var(--color-text-light);
}

.goal-target-amount {
    color: var(--color-text-light);
}

.goal-date {
    font-size: var(--font-size-sm);
    color: var(--color-text-light);
    text-align: right;
}

.goal-days-left {
    display: block;
    font-weight: 500;
    color: var(--color-text);
}

.goal-description {
    margin-top: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--color-text-light);
    border-top: 1px solid var(--color-border);
    padding-top: var(--spacing-sm);
}

/* 进度条样式 */
.progress-container {
    width: 100%;
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.progress-bar {
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}

.progress-bar.progress-info {
    background-color: var(--color-primary);
}

.progress-bar.progress-warning {
    background-color: #f39c12;
}

.progress-bar.progress-success {
    background-color: var(--color-income);
}

.progress-text {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    font-size: var(--font-size-xs);
    font-weight: 600;
}

/* 空数据提示 */
.empty-message {
    text-align: center;
    padding: var(--spacing-lg);
    color: var(--color-text-light);
    font-style: italic;
    background-color: #f9f9f9;
    border-radius: var(--border-radius-sm);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .goals-list {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media (max-width: 480px) {
    .goals-list {
        grid-template-columns: 1fr;
    }
    
    .goal-details {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .goal-date {
        text-align: left;
    }
}
