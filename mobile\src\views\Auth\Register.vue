<template>
  <div class="register-page">
    <div class="header">
      <van-nav-bar 
        title="注册账户" 
        left-arrow 
        @click-left="$router.back()"
        fixed
        placeholder
      />
    </div>
    
    <div class="form-container">
      <van-form @submit="handleRegister">
        <van-cell-group inset>
          <van-field
            v-model="form.username"
            name="username"
            label="用户名"
            placeholder="请输入用户名"
            left-icon="user-o"
            :rules="[{ required: true, message: '请输入用户名' }]"
            clearable
          />
          <van-field
            v-model="form.email"
            name="email"
            label="邮箱"
            placeholder="请输入邮箱"
            left-icon="envelop-o"
            :rules="[{ required: true, message: '请输入邮箱' }]"
            clearable
          />
          <van-field
            v-model="form.password"
            type="password"
            name="password"
            label="密码"
            placeholder="请输入密码"
            left-icon="lock"
            :rules="[{ required: true, message: '请输入密码' }]"
            clearable
          />
          <van-field
            v-model="form.confirmPassword"
            type="password"
            name="confirmPassword"
            label="确认密码"
            placeholder="请再次输入密码"
            left-icon="lock"
            :rules="[{ required: true, message: '请确认密码' }]"
            clearable
          />
        </van-cell-group>

        <div class="button-group">
          <van-button 
            round 
            block 
            type="primary" 
            native-type="submit"
            :loading="loading"
            loading-text="注册中..."
            size="large"
          >
            注册
          </van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { showToast } from 'vant'

const router = useRouter()
const authStore = useAuthStore()

const form = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

const loading = ref(false)

const handleRegister = async () => {
  if (form.password !== form.confirmPassword) {
    showToast('两次输入的密码不一致')
    return
  }

  loading.value = true
  
  try {
    await authStore.register({
      username: form.username,
      email: form.email,
      password: form.password,
      display_name: form.username
    })

    showToast({
      message: '注册成功，请登录',
      type: 'success'
    })

    router.push('/login')
    
  } catch (error) {
    console.error('注册失败:', error)
    showToast({
      message: error.message || '注册失败',
      type: 'fail'
    })
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.register-page {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.form-container {
  padding: 20px;
}

.button-group {
  padding: 24px 0;
}
</style>
