<template>
  <van-popup v-model:show="show" position="bottom" :style="{ height: '60%' }">
    <div class="device-info">
      <van-nav-bar title="设备信息" @click-left="show = false">
        <template #left>
          <van-icon name="cross" />
        </template>
      </van-nav-bar>
      
      <div class="info-content">
        <van-cell-group inset title="设备检测">
          <van-cell title="设备类型" :value="deviceInfo.deviceType" />
          <van-cell title="屏幕尺寸" :value="deviceInfo.screenSize" />
          <van-cell title="是否移动设备" :value="deviceInfo.isMobile ? '是' : '否'" />
          <van-cell title="是否平板设备" :value="deviceInfo.isTablet ? '是' : '否'" />
          <van-cell title="是否桌面设备" :value="deviceInfo.isDesktop ? '是' : '否'" />
          <van-cell title="是否触摸设备" :value="deviceInfo.isTouchDevice ? '是' : '否'" />
          <van-cell
            title="应该使用手机版"
            :value="shouldUseMobile ? '是' : '否'"
            :value-class="shouldUseMobile ? 'text-success' : 'text-danger'"
          />
        </van-cell-group>
        
        <van-cell-group inset title="屏幕信息">
          <van-cell title="屏幕宽度" :value="deviceInfo.screenWidth + 'px'" />
          <van-cell title="屏幕高度" :value="deviceInfo.screenHeight + 'px'" />
          <van-cell title="当前端口" :value="deviceInfo.currentPort || '默认'" />
          <van-cell title="当前主机" :value="deviceInfo.currentHost" />
        </van-cell-group>
        
        <van-cell-group inset title="用户代理">
          <van-cell
            title="User Agent"
            :value="deviceInfo.userAgent"
            :label="deviceInfo.userAgent"
            is-link
            @click="copyUserAgent"
          />
        </van-cell-group>

        <van-cell-group inset title="调试设置">
          <van-cell title="禁用自动重定向">
            <template #right-icon>
              <van-switch
                v-model="debugMode"
                @change="toggleDebugMode"
                size="20px"
              />
            </template>
          </van-cell>
        </van-cell-group>
        
        <div class="actions">
          <van-button 
            type="primary" 
            block 
            @click="refreshInfo"
            :loading="refreshing"
          >
            刷新信息
          </van-button>
          
          <van-button
            type="default"
            block
            plain
            @click="testRedirect"
            style="margin-top: 12px;"
          >
            测试重定向逻辑
          </van-button>

          <van-button
            type="warning"
            block
            plain
            @click="forceRedirectToWeb"
            style="margin-top: 12px;"
          >
            强制跳转到网页版
          </van-button>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { showToast } from 'vant'
import { getDeviceInfo, smartRedirect, shouldUseMobileVersion } from '@/utils/device'

const show = defineModel('show', { type: Boolean, default: false })

const refreshing = ref(false)
const deviceInfo = reactive({})
const debugMode = ref(localStorage.getItem('disableAutoRedirect') === 'true')

// 计算是否应该使用手机版
const shouldUseMobile = computed(() => shouldUseMobileVersion())

const updateDeviceInfo = () => {
  Object.assign(deviceInfo, getDeviceInfo())
}

const refreshInfo = async () => {
  refreshing.value = true
  await new Promise(resolve => setTimeout(resolve, 500)) // 模拟刷新延迟
  updateDeviceInfo()
  refreshing.value = false
  showToast('信息已刷新')
}

const copyUserAgent = async () => {
  try {
    await navigator.clipboard.writeText(deviceInfo.userAgent)
    showToast('User Agent 已复制到剪贴板')
  } catch (error) {
    showToast('复制失败')
  }
}

const testRedirect = () => {
  const redirectUrl = smartRedirect()
  const shouldUseMobileNow = shouldUseMobileVersion()

  showToast({
    message: `
      检测结果: ${shouldUseMobileNow ? '应该使用手机版' : '应该使用网页版'}
      ${redirectUrl ? `重定向到: ${redirectUrl}` : '无需重定向'}
      屏幕宽度: ${window.innerWidth}px
    `,
    duration: 4000
  })
}

const forceRedirectToWeb = () => {
  const deviceInfo = getDeviceInfo()
  const webUrl = `http://${deviceInfo.currentHost}:8000${window.location.pathname}`

  showToast({
    message: '正在跳转到网页版...',
    type: 'loading',
    duration: 1500
  })

  setTimeout(() => {
    window.location.href = webUrl
  }, 1500)
}

const toggleDebugMode = (value) => {
  localStorage.setItem('disableAutoRedirect', value.toString())
  showToast({
    message: value ? '已禁用自动重定向' : '已启用自动重定向',
    type: 'success'
  })
}

onMounted(() => {
  updateDeviceInfo()
})

// 监听窗口大小变化
window.addEventListener('resize', updateDeviceInfo)
</script>

<style scoped>
.device-info {
  height: 100%;
  background-color: #f7f8fa;
}

.info-content {
  padding: 16px;
  height: calc(100% - 46px);
  overflow-y: auto;
}

.actions {
  padding: 16px 0;
}

:deep(.van-cell__value) {
  word-break: break-all;
}

:deep(.van-cell__label) {
  font-size: 12px;
  color: #969799;
  word-break: break-all;
  white-space: normal;
  line-height: 1.4;
}
</style>
