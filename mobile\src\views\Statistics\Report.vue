<template>
  <div class="report-page">
    <van-nav-bar 
      title="财务报告" 
      left-arrow 
      @click-left="$router.back()"
      fixed
      placeholder
    >
      <template #right>
        <van-icon name="share" size="18" @click="shareReport" />
      </template>
    </van-nav-bar>

    <div class="page-container">
      <!-- 报告类型选择 -->
      <div class="report-type-selector">
        <van-tabs v-model:active="activeReportType" @change="onReportTypeChange">
          <van-tab title="月度报告" name="monthly" />
          <van-tab title="季度报告" name="quarterly" />
          <van-tab title="年度报告" name="yearly" />
        </van-tabs>
      </div>

      <!-- 时间选择 -->
      <div class="time-selector">
        <van-cell 
          title="报告时间" 
          :value="selectedTimeText" 
          is-link 
          @click="showTimePicker = true" 
        />
      </div>

      <!-- 报告概览 -->
      <div class="report-overview">
        <div class="overview-header">
          <h3>{{ reportTitle }}</h3>
          <div class="report-date">{{ reportDateRange }}</div>
        </div>
        
        <div class="overview-stats">
          <div class="stat-item">
            <div class="stat-label">总收入</div>
            <div class="stat-value income">{{ formatCurrency(reportData.totalIncome) }}</div>
            <div class="stat-change" :class="getChangeClass(reportData.incomeChange)">
              {{ formatChange(reportData.incomeChange) }}
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-label">总支出</div>
            <div class="stat-value expense">{{ formatCurrency(reportData.totalExpense) }}</div>
            <div class="stat-change" :class="getChangeClass(reportData.expenseChange, true)">
              {{ formatChange(reportData.expenseChange) }}
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-label">净收入</div>
            <div class="stat-value" :class="reportData.netIncome >= 0 ? 'income' : 'expense'">
              {{ formatCurrency(reportData.netIncome) }}
            </div>
            <div class="stat-change" :class="getChangeClass(reportData.netIncomeChange)">
              {{ formatChange(reportData.netIncomeChange) }}
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-label">储蓄率</div>
            <div class="stat-value">{{ savingsRate }}%</div>
            <div class="stat-change" :class="getChangeClass(reportData.savingsRateChange)">
              {{ formatChange(reportData.savingsRateChange) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 详细分析 -->
      <div class="detailed-analysis">
        <!-- 收支对比图 -->
        <div class="analysis-section">
          <div class="section-header">
            <h4>收支对比</h4>
          </div>
          <div class="chart-container">
            <BaseChart 
              v-if="chartData.comparison"
              :options="chartData.comparison" 
              height="200px" 
            />
          </div>
        </div>

        <!-- 支出分类排行 -->
        <div class="analysis-section">
          <div class="section-header">
            <h4>支出分类排行</h4>
          </div>
          <van-cell-group inset>
            <van-cell
              v-for="(category, index) in topExpenseCategories"
              :key="category.name"
              :title="category.name"
              :label="`占总支出 ${category.percentage}%`"
              :value="formatCurrency(category.amount)"
            >
              <template #icon>
                <div class="rank-badge" :class="getRankClass(index)">
                  {{ index + 1 }}
                </div>
              </template>
            </van-cell>
          </van-cell-group>
        </div>

        <!-- 收入来源分析 -->
        <div class="analysis-section">
          <div class="section-header">
            <h4>收入来源分析</h4>
          </div>
          <van-cell-group inset>
            <van-cell
              v-for="(source, index) in incomeSourcesAnalysis"
              :key="source.name"
              :title="source.name"
              :label="`占总收入 ${source.percentage}%`"
              :value="formatCurrency(source.amount)"
            >
              <template #icon>
                <div class="income-icon">💰</div>
              </template>
            </van-cell>
          </van-cell-group>
        </div>

        <!-- 财务健康度评估 -->
        <div class="analysis-section">
          <div class="section-header">
            <h4>财务健康度评估</h4>
          </div>
          <div class="health-assessment">
            <div class="health-score">
              <div class="score-circle" :class="healthScoreClass">
                <span class="score-value">{{ healthScore }}</span>
                <span class="score-label">分</span>
              </div>
              <div class="score-description">{{ healthDescription }}</div>
            </div>
            
            <div class="health-indicators">
              <div class="indicator-item">
                <div class="indicator-label">储蓄率</div>
                <div class="indicator-bar">
                  <div class="indicator-progress" :style="{ width: `${Math.min(savingsRate, 100)}%` }"></div>
                </div>
                <div class="indicator-value">{{ savingsRate }}%</div>
              </div>
              
              <div class="indicator-item">
                <div class="indicator-label">支出控制</div>
                <div class="indicator-bar">
                  <div class="indicator-progress" :style="{ width: `${expenseControlScore}%` }"></div>
                </div>
                <div class="indicator-value">{{ expenseControlScore }}%</div>
              </div>
              
              <div class="indicator-item">
                <div class="indicator-label">收入稳定性</div>
                <div class="indicator-bar">
                  <div class="indicator-progress" :style="{ width: `${incomeStabilityScore}%` }"></div>
                </div>
                <div class="indicator-value">{{ incomeStabilityScore }}%</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 建议与提醒 -->
        <div class="analysis-section">
          <div class="section-header">
            <h4>理财建议</h4>
          </div>
          <div class="suggestions">
            <div 
              v-for="suggestion in suggestions" 
              :key="suggestion.id"
              class="suggestion-item"
              :class="suggestion.type"
            >
              <div class="suggestion-icon">{{ suggestion.icon }}</div>
              <div class="suggestion-content">
                <div class="suggestion-title">{{ suggestion.title }}</div>
                <div class="suggestion-description">{{ suggestion.description }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <van-button type="primary" size="large" @click="exportReport" block>
          导出报告
        </van-button>
      </div>
    </div>

    <!-- 时间选择器 -->
    <van-popup v-model:show="showTimePicker" position="bottom" round>
      <van-date-picker
        v-model="selectedTime"
        :type="timePickerType"
        title="选择时间"
        @confirm="onTimeConfirm"
        @cancel="showTimePicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useTransactionsStore } from '@/stores/transactions'
import BaseChart from '@/components/charts/BaseChart.vue'
import { formatCurrency } from '@/utils/format'
import { showToast } from 'vant'

const router = useRouter()
const transactionsStore = useTransactionsStore()

// 响应式数据
const loading = ref(false)
const activeReportType = ref('monthly')
const showTimePicker = ref(false)
// Vant 4.x 使用数组格式 [year, month, day] 或 [year, month] 或 [year]
const selectedTime = ref([])

// 辅助函数：将数组格式转换为 Date 对象
const getDateFromArray = () => {
  if (!selectedTime.value || selectedTime.value.length === 0) {
    return new Date()
  }

  const [year, month = 1, day = 1] = selectedTime.value
  return new Date(year, month - 1, day) // month 需要减1，因为 Date 的月份从0开始
}

// 报告数据
const reportData = reactive({
  totalIncome: 0,
  totalExpense: 0,
  netIncome: 0,
  incomeChange: 0,
  expenseChange: 0,
  netIncomeChange: 0,
  savingsRateChange: 0
})

// 图表数据
const chartData = reactive({
  comparison: null
})

// 计算属性
const timePickerType = computed(() => {
  switch (activeReportType.value) {
    case 'monthly': return 'year-month'
    case 'quarterly': return 'year-month'
    case 'yearly': return 'year'
    default: return 'year-month'
  }
})

const selectedTimeText = computed(() => {
  const date = getDateFromArray()
  switch (activeReportType.value) {
    case 'monthly':
      return `${date.getFullYear()}年${date.getMonth() + 1}月`
    case 'quarterly':
      const quarter = Math.floor(date.getMonth() / 3) + 1
      return `${date.getFullYear()}年第${quarter}季度`
    case 'yearly':
      return `${date.getFullYear()}年`
    default:
      return ''
  }
})

const reportTitle = computed(() => {
  switch (activeReportType.value) {
    case 'monthly': return '月度财务报告'
    case 'quarterly': return '季度财务报告'
    case 'yearly': return '年度财务报告'
    default: return '财务报告'
  }
})

const reportDateRange = computed(() => {
  const date = getDateFromArray()
  switch (activeReportType.value) {
    case 'monthly':
      const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate()
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-01 至 ${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${lastDay}`
    case 'quarterly':
      const quarter = Math.floor(date.getMonth() / 3)
      const startMonth = quarter * 3 + 1
      const endMonth = startMonth + 2
      return `${date.getFullYear()}-${String(startMonth).padStart(2, '0')}-01 至 ${date.getFullYear()}-${String(endMonth).padStart(2, '0')}-31`
    case 'yearly':
      return `${date.getFullYear()}-01-01 至 ${date.getFullYear()}-12-31`
    default:
      return ''
  }
})

const savingsRate = computed(() => {
  if (reportData.totalIncome === 0) return 0
  return Math.round((reportData.netIncome / reportData.totalIncome) * 100)
})

const topExpenseCategories = computed(() => {
  const transactions = getFilteredTransactions().filter(t => t.type === 'expense')
  const categoryStats = {}
  
  transactions.forEach(t => {
    categoryStats[t.category] = (categoryStats[t.category] || 0) + t.amount
  })
  
  return Object.entries(categoryStats)
    .map(([name, amount]) => ({
      name,
      amount,
      percentage: reportData.totalExpense > 0 ? Math.round((amount / reportData.totalExpense) * 100) : 0
    }))
    .sort((a, b) => b.amount - a.amount)
    .slice(0, 5)
})

const incomeSourcesAnalysis = computed(() => {
  const transactions = getFilteredTransactions().filter(t => t.type === 'income')
  const sourceStats = {}
  
  transactions.forEach(t => {
    sourceStats[t.category] = (sourceStats[t.category] || 0) + t.amount
  })
  
  return Object.entries(sourceStats)
    .map(([name, amount]) => ({
      name,
      amount,
      percentage: reportData.totalIncome > 0 ? Math.round((amount / reportData.totalIncome) * 100) : 0
    }))
    .sort((a, b) => b.amount - a.amount)
})

const healthScore = computed(() => {
  // 简单的健康度评分算法
  let score = 60 // 基础分
  
  // 储蓄率加分
  if (savingsRate.value >= 30) score += 20
  else if (savingsRate.value >= 20) score += 15
  else if (savingsRate.value >= 10) score += 10
  else if (savingsRate.value >= 0) score += 5
  
  // 收入稳定性加分
  score += Math.min(incomeStabilityScore.value / 5, 10)
  
  // 支出控制加分
  score += Math.min(expenseControlScore.value / 5, 10)
  
  return Math.min(Math.round(score), 100)
})

const healthScoreClass = computed(() => {
  if (healthScore.value >= 80) return 'excellent'
  if (healthScore.value >= 60) return 'good'
  if (healthScore.value >= 40) return 'fair'
  return 'poor'
})

const healthDescription = computed(() => {
  if (healthScore.value >= 80) return '财务状况优秀'
  if (healthScore.value >= 60) return '财务状况良好'
  if (healthScore.value >= 40) return '财务状况一般'
  return '需要改善财务状况'
})

const expenseControlScore = computed(() => {
  // 支出控制评分：基于支出变化趋势
  const change = reportData.expenseChange
  if (change <= -10) return 90
  if (change <= 0) return 80
  if (change <= 10) return 70
  if (change <= 20) return 60
  return 50
})

const incomeStabilityScore = computed(() => {
  // 收入稳定性评分：基于收入变化趋势
  const change = Math.abs(reportData.incomeChange)
  if (change <= 5) return 90
  if (change <= 10) return 80
  if (change <= 20) return 70
  if (change <= 30) return 60
  return 50
})

const suggestions = computed(() => {
  const suggestions = []
  
  if (savingsRate.value < 10) {
    suggestions.push({
      id: 1,
      type: 'warning',
      icon: '⚠️',
      title: '储蓄率偏低',
      description: '建议将储蓄率提高到收入的10%以上，为未来做好准备。'
    })
  }
  
  if (reportData.expenseChange > 20) {
    suggestions.push({
      id: 2,
      type: 'danger',
      icon: '🚨',
      title: '支出增长过快',
      description: '支出增长超过20%，建议审查和控制非必要支出。'
    })
  }
  
  if (savingsRate.value >= 20) {
    suggestions.push({
      id: 3,
      type: 'success',
      icon: '🎉',
      title: '储蓄表现优秀',
      description: '储蓄率达到20%以上，可以考虑进行投资理财。'
    })
  }
  
  if (topExpenseCategories.value.length > 0 && topExpenseCategories.value[0].percentage > 40) {
    suggestions.push({
      id: 4,
      type: 'info',
      icon: '💡',
      title: '支出过于集中',
      description: `${topExpenseCategories.value[0].name}占支出比例过高，建议分散支出结构。`
    })
  }
  
  return suggestions
})

// 初始化
onMounted(async () => {
  // 初始化默认日期为当前月份
  const today = new Date()
  selectedTime.value = [today.getFullYear(), today.getMonth() + 1]

  await loadData()
})

// 监听报告类型和时间变化
watch([activeReportType, selectedTime], () => {
  generateReport()
}, { deep: true })

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    await transactionsStore.fetchTransactions()
    generateReport()
  } catch (error) {
    console.error('加载数据失败:', error)
    showToast('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 生成报告
const generateReport = () => {
  const transactions = getFilteredTransactions()
  const previousTransactions = getPreviousTransactions()

  // 计算当前期间数据
  const currentIncome = transactions.filter(t => t.type === 'income').reduce((sum, t) => sum + t.amount, 0)
  const currentExpense = transactions.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0)
  const currentNet = currentIncome - currentExpense

  // 计算上期数据
  const previousIncome = previousTransactions.filter(t => t.type === 'income').reduce((sum, t) => sum + t.amount, 0)
  const previousExpense = previousTransactions.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0)
  const previousNet = previousIncome - previousExpense

  // 计算储蓄率变化
  const currentSavingsRate = currentIncome > 0 ? (currentNet / currentIncome) * 100 : 0
  const previousSavingsRate = previousIncome > 0 ? (previousNet / previousIncome) * 100 : 0

  // 更新报告数据
  reportData.totalIncome = currentIncome
  reportData.totalExpense = currentExpense
  reportData.netIncome = currentNet
  reportData.incomeChange = calculatePercentageChange(previousIncome, currentIncome)
  reportData.expenseChange = calculatePercentageChange(previousExpense, currentExpense)
  reportData.netIncomeChange = calculatePercentageChange(previousNet, currentNet)
  reportData.savingsRateChange = Math.round(currentSavingsRate - previousSavingsRate)

  // 生成图表
  generateComparisonChart(transactions)
}

// 获取筛选后的交易
const getFilteredTransactions = () => {
  const date = getDateFromArray()
  let startDate, endDate

  switch (activeReportType.value) {
    case 'monthly':
      startDate = new Date(date.getFullYear(), date.getMonth(), 1)
      endDate = new Date(date.getFullYear(), date.getMonth() + 1, 0)
      break
    case 'quarterly':
      const quarter = Math.floor(date.getMonth() / 3)
      startDate = new Date(date.getFullYear(), quarter * 3, 1)
      endDate = new Date(date.getFullYear(), quarter * 3 + 3, 0)
      break
    case 'yearly':
      startDate = new Date(date.getFullYear(), 0, 1)
      endDate = new Date(date.getFullYear(), 11, 31)
      break
    default:
      startDate = new Date(date.getFullYear(), date.getMonth(), 1)
      endDate = new Date(date.getFullYear(), date.getMonth() + 1, 0)
  }

  return transactionsStore.transactions.filter(t => {
    const transactionDate = new Date(t.date)
    return transactionDate >= startDate && transactionDate <= endDate
  })
}

// 获取上期交易数据
const getPreviousTransactions = () => {
  const date = getDateFromArray()
  let startDate, endDate

  switch (activeReportType.value) {
    case 'monthly':
      startDate = new Date(date.getFullYear(), date.getMonth() - 1, 1)
      endDate = new Date(date.getFullYear(), date.getMonth(), 0)
      break
    case 'quarterly':
      const quarter = Math.floor(date.getMonth() / 3)
      startDate = new Date(date.getFullYear(), (quarter - 1) * 3, 1)
      endDate = new Date(date.getFullYear(), quarter * 3, 0)
      break
    case 'yearly':
      startDate = new Date(date.getFullYear() - 1, 0, 1)
      endDate = new Date(date.getFullYear() - 1, 11, 31)
      break
    default:
      startDate = new Date(date.getFullYear(), date.getMonth() - 1, 1)
      endDate = new Date(date.getFullYear(), date.getMonth(), 0)
  }

  return transactionsStore.transactions.filter(t => {
    const transactionDate = new Date(t.date)
    return transactionDate >= startDate && transactionDate <= endDate
  })
}

// 生成收支对比图
const generateComparisonChart = (transactions) => {
  const incomeTransactions = transactions.filter(t => t.type === 'income')
  const expenseTransactions = transactions.filter(t => t.type === 'expense')

  chartData.comparison = {
    title: {
      text: '收支对比',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      data: ['收入', '支出'],
      bottom: 10
    },
    series: [
      {
        name: '收支对比',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        data: [
          {
            name: '收入',
            value: reportData.totalIncome,
            itemStyle: { color: '#07c160' }
          },
          {
            name: '支出',
            value: reportData.totalExpense,
            itemStyle: { color: '#ee0a24' }
          }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
}

// 工具方法
const calculatePercentageChange = (oldValue, newValue) => {
  if (oldValue === 0) {
    return newValue > 0 ? 100 : 0
  }
  return Math.round(((newValue - oldValue) / oldValue) * 100)
}

const formatChange = (change) => {
  if (change > 0) {
    return `+${change}%`
  } else if (change < 0) {
    return `${change}%`
  } else {
    return '0%'
  }
}

const getChangeClass = (change, isExpense = false) => {
  if (change > 0) {
    return isExpense ? 'negative' : 'positive'
  } else if (change < 0) {
    return isExpense ? 'positive' : 'negative'
  } else {
    return 'neutral'
  }
}

const getRankClass = (index) => {
  if (index === 0) return 'rank-1'
  if (index === 1) return 'rank-2'
  if (index === 2) return 'rank-3'
  return ''
}

// 事件处理
const onReportTypeChange = () => {
  generateReport()
}

const onTimeConfirm = () => {
  showTimePicker.value = false
  generateReport()
}

const shareReport = () => {
  showToast('分享功能开发中...')
}

const exportReport = () => {
  showToast('导出功能开发中...')
}
</script>

<style scoped>
.report-page {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.page-container {
  padding-bottom: 20px;
}

.report-type-selector {
  background: white;
  margin-bottom: 12px;
}

.time-selector {
  margin-bottom: 16px;
}

.report-overview {
  background: white;
  margin: 0 16px 16px;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.overview-header {
  text-align: center;
  margin-bottom: 20px;
}

.overview-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #323233;
}

.report-date {
  font-size: 12px;
  color: #969799;
}

.overview-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: #969799;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-value.income {
  color: #07c160;
}

.stat-value.expense {
  color: #ee0a24;
}

.stat-change {
  font-size: 12px;
}

.stat-change.positive {
  color: #07c160;
}

.stat-change.negative {
  color: #ee0a24;
}

.stat-change.neutral {
  color: #969799;
}

.detailed-analysis {
  margin: 0 16px;
}

.analysis-section {
  background: white;
  border-radius: 12px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.section-header {
  padding: 16px 16px 0;
}

.section-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

.chart-container {
  padding: 16px;
}

.rank-badge {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: white;
}

.rank-badge.rank-1 {
  background: #ffd700;
}

.rank-badge.rank-2 {
  background: #c0c0c0;
}

.rank-badge.rank-3 {
  background: #cd7f32;
}

.rank-badge:not(.rank-1):not(.rank-2):not(.rank-3) {
  background: #969799;
}

.income-icon {
  font-size: 20px;
}

.health-assessment {
  padding: 16px;
}

.health-score {
  text-align: center;
  margin-bottom: 24px;
}

.score-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto 12px;
  border: 4px solid;
}

.score-circle.excellent {
  border-color: #07c160;
  background: rgba(7, 193, 96, 0.1);
}

.score-circle.good {
  border-color: #1989fa;
  background: rgba(25, 137, 250, 0.1);
}

.score-circle.fair {
  border-color: #ff976a;
  background: rgba(255, 151, 106, 0.1);
}

.score-circle.poor {
  border-color: #ee0a24;
  background: rgba(238, 10, 36, 0.1);
}

.score-value {
  font-size: 24px;
  font-weight: 600;
  line-height: 1;
}

.score-label {
  font-size: 12px;
  opacity: 0.8;
}

.score-description {
  font-size: 14px;
  color: #646566;
}

.health-indicators {
  space-y: 16px;
}

.indicator-item {
  margin-bottom: 16px;
}

.indicator-label {
  font-size: 14px;
  color: #323233;
  margin-bottom: 8px;
}

.indicator-bar {
  height: 8px;
  background: #f2f3f5;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 4px;
}

.indicator-progress {
  height: 100%;
  background: linear-gradient(90deg, #07c160, #1989fa);
  transition: width 0.3s ease;
}

.indicator-value {
  font-size: 12px;
  color: #969799;
  text-align: right;
}

.suggestions {
  padding: 16px;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 12px;
}

.suggestion-item.success {
  background: rgba(7, 193, 96, 0.1);
  border-left: 4px solid #07c160;
}

.suggestion-item.warning {
  background: rgba(255, 151, 106, 0.1);
  border-left: 4px solid #ff976a;
}

.suggestion-item.danger {
  background: rgba(238, 10, 36, 0.1);
  border-left: 4px solid #ee0a24;
}

.suggestion-item.info {
  background: rgba(25, 137, 250, 0.1);
  border-left: 4px solid #1989fa;
}

.suggestion-icon {
  font-size: 20px;
  margin-right: 12px;
  margin-top: 2px;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-size: 14px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
}

.suggestion-description {
  font-size: 12px;
  color: #646566;
  line-height: 1.4;
}

.action-buttons {
  padding: 20px 16px;
}
</style>
