<template>
  <div class="category-detail-page">
    <van-nav-bar 
      title="分类统计" 
      left-arrow 
      @click-left="$router.back()"
      fixed
      placeholder
    />

    <div class="page-container">
      <!-- 类型切换 -->
      <div class="type-selector">
        <van-tabs v-model:active="activeType" @change="onTypeChange">
          <van-tab title="支出分类" name="expense" />
          <van-tab title="收入分类" name="income" />
        </van-tabs>
      </div>

      <!-- 统计概览 -->
      <div class="overview-card">
        <div class="overview-item">
          <div class="overview-label">总{{ activeType === 'expense' ? '支出' : '收入' }}</div>
          <div class="overview-value">{{ formatCurrency(totalAmount) }}</div>
        </div>
        <div class="overview-item">
          <div class="overview-label">分类数量</div>
          <div class="overview-value">{{ categoryStats.length }}</div>
        </div>
        <div class="overview-item">
          <div class="overview-label">平均金额</div>
          <div class="overview-value">{{ formatCurrency(averageAmount) }}</div>
        </div>
      </div>

      <!-- 饼图 -->
      <div class="chart-container">
        <div class="chart-header">
          <h3>{{ activeType === 'expense' ? '支出' : '收入' }}分布</h3>
        </div>
        <div class="chart-content">
          <BaseChart 
            v-if="chartData"
            :options="chartData" 
            height="300px" 
          />
          <div v-else class="chart-loading">
            <van-loading size="24px" />
            <span>加载中...</span>
          </div>
        </div>
      </div>

      <!-- 分类列表 -->
      <div class="category-list">
        <div class="list-header">
          <h3>分类详情</h3>
          <van-button size="mini" type="primary" plain @click="toggleSortOrder">
            {{ sortOrder === 'desc' ? '金额↓' : '金额↑' }}
          </van-button>
        </div>
        
        <van-cell-group inset>
          <van-cell
            v-for="(category, index) in sortedCategoryStats"
            :key="category.name"
            :title="category.name"
            :label="`${category.count} 笔交易`"
            :value="formatCurrency(category.amount)"
            is-link
            @click="viewCategoryTransactions(category)"
          >
            <template #icon>
              <div class="category-rank">{{ index + 1 }}</div>
            </template>
            <template #right-icon>
              <div class="category-percentage">
                {{ ((category.amount / totalAmount) * 100).toFixed(1) }}%
              </div>
            </template>
          </van-cell>
        </van-cell-group>
      </div>

      <!-- 趋势分析 -->
      <div class="trend-container">
        <div class="trend-header">
          <h3>月度趋势</h3>
        </div>
        <div class="trend-content">
          <BaseChart 
            v-if="trendChartData"
            :options="trendChartData" 
            height="240px" 
          />
          <div v-else class="chart-loading">
            <van-loading size="24px" />
            <span>加载中...</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useTransactionsStore } from '@/stores/transactions'
import BaseChart from '@/components/charts/BaseChart.vue'
import { formatCurrency } from '@/utils/format'

const router = useRouter()
const transactionsStore = useTransactionsStore()

// 响应式数据
const loading = ref(false)
const activeType = ref('expense')
const sortOrder = ref('desc')
const chartData = ref(null)
const trendChartData = ref(null)

// 分类统计数据
const categoryStats = computed(() => {
  const transactions = transactionsStore.transactions.filter(t => t.type === activeType.value)
  const stats = {}
  
  transactions.forEach(t => {
    if (!stats[t.category]) {
      stats[t.category] = {
        name: t.category,
        amount: 0,
        count: 0
      }
    }
    stats[t.category].amount += t.amount
    stats[t.category].count += 1
  })
  
  return Object.values(stats)
})

// 排序后的分类统计
const sortedCategoryStats = computed(() => {
  return [...categoryStats.value].sort((a, b) => {
    return sortOrder.value === 'desc' ? b.amount - a.amount : a.amount - b.amount
  })
})

// 总金额
const totalAmount = computed(() => {
  return categoryStats.value.reduce((sum, cat) => sum + cat.amount, 0)
})

// 平均金额
const averageAmount = computed(() => {
  return categoryStats.value.length > 0 ? totalAmount.value / categoryStats.value.length : 0
})

// 初始化
onMounted(async () => {
  await loadData()
})

// 监听类型变化
watch(activeType, () => {
  generateCharts()
})

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    await transactionsStore.fetchTransactions()
    generateCharts()
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 生成图表
const generateCharts = () => {
  generatePieChart()
  generateTrendChart()
}

// 生成饼图
const generatePieChart = () => {
  const pieData = categoryStats.value.map(cat => ({
    name: cat.name,
    value: cat.amount
  })).sort((a, b) => b.value - a.value)
  
  chartData.value = {
    title: {
      text: `${activeType.value === 'expense' ? '支出' : '收入'}分布`,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: pieData.map(item => item.name)
    },
    series: [
      {
        name: `${activeType.value === 'expense' ? '支出' : '收入'}分类`,
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        data: pieData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          formatter: '{b}: {d}%'
        }
      }
    ]
  }
}

// 生成趋势图
const generateTrendChart = () => {
  const transactions = transactionsStore.transactions.filter(t => t.type === activeType.value)
  const monthlyData = {}
  
  // 获取最近6个月
  const now = new Date()
  for (let i = 5; i >= 0; i--) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1)
    const key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
    const label = `${date.getMonth() + 1}月`
    
    monthlyData[key] = {
      label,
      categories: {}
    }
  }
  
  // 统计每月各分类数据
  transactions.forEach(t => {
    const date = new Date(t.date)
    const key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
    
    if (monthlyData[key]) {
      if (!monthlyData[key].categories[t.category]) {
        monthlyData[key].categories[t.category] = 0
      }
      monthlyData[key].categories[t.category] += t.amount
    }
  })
  
  // 获取所有分类
  const allCategories = [...new Set(transactions.map(t => t.category))]
  const months = Object.values(monthlyData).map(data => data.label)
  
  // 生成系列数据
  const series = allCategories.map((category, index) => ({
    name: category,
    type: 'line',
    data: Object.values(monthlyData).map(data => data.categories[category] || 0),
    smooth: true,
    itemStyle: {
      color: getColorByIndex(index)
    }
  }))
  
  trendChartData.value = {
    title: {
      text: '月度趋势',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach(param => {
          result += `${param.seriesName}: ${formatCurrency(param.value)}<br/>`
        })
        return result
      }
    },
    legend: {
      data: allCategories,
      bottom: 10
    },
    xAxis: {
      type: 'category',
      data: months
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => formatCurrency(value, '', 0)
      }
    },
    series
  }
}

// 根据索引获取颜色
const getColorByIndex = (index) => {
  const colors = [
    '#1989fa', '#07c160', '#ee0a24', '#ff976a', '#91d5ff',
    '#b37feb', '#ffc53d', '#36cfc9', '#73d13d', '#ff85c0'
  ]
  return colors[index % colors.length]
}

// 事件处理
const onTypeChange = () => {
  generateCharts()
}

const toggleSortOrder = () => {
  sortOrder.value = sortOrder.value === 'desc' ? 'asc' : 'desc'
}

const viewCategoryTransactions = (category) => {
  // 跳转到该分类的交易列表
  router.push({
    path: '/transactions',
    query: {
      category: category.name,
      type: activeType.value
    }
  })
}
</script>

<style scoped>
.category-detail-page {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.page-container {
  padding-bottom: 20px;
}

.type-selector {
  background: white;
  margin-bottom: 12px;
}

.overview-card {
  background: white;
  margin: 0 16px 16px;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.overview-item {
  text-align: center;
}

.overview-label {
  font-size: 12px;
  color: #969799;
  margin-bottom: 8px;
}

.overview-value {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
}

.chart-container,
.trend-container {
  background: white;
  margin: 0 16px 16px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.chart-header,
.trend-header {
  padding: 16px 16px 0;
}

.chart-header h3,
.trend-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

.chart-content,
.trend-content {
  padding: 16px;
}

.chart-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 240px;
  color: #969799;
}

.chart-loading span {
  margin-top: 8px;
  font-size: 14px;
}

.category-list {
  margin: 0 16px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.list-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

.category-rank {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #1989fa;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.category-percentage {
  font-size: 12px;
  color: #969799;
  margin-right: 8px;
}
</style>
