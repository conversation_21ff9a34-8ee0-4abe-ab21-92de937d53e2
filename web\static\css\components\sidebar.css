/*******************************************
 *          侧边栏样式
 *******************************************/
.sidebar {
  width: var(--sidebar-width);
  background-color: var(--color-card);
  box-shadow: var(--shadow-sm);
  display: flex;
  flex-direction: column;
  justify-content: flex-start; /* 改为从顶部开始布局 */
  padding: var(--spacing-lg);
  height: 100vh; /* 确保侧边栏高度固定 */
  overflow-y: auto; /* 如果内容过多，允许滚动 */
}

/* 侧边栏顶部（Logo） */
.sidebar-header {
  margin-bottom: var(--spacing-xl);
  display: flex;
  align-items: center;
}

.sidebar-header .logo {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--color-text);
  display: flex;
  align-items: center;
}

.logo-icon {
  width: 28px;
  height: 28px;
  background-color: var(--color-primary);
  border-radius: var(--border-radius-sm);
  margin-right: var(--spacing-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
}

/* 菜单区域 */
.menu-section {
  margin-bottom: var(--spacing-lg);
}

.menu-title {
  font-size: var(--font-size-xs);
  color: var(--color-text-lighter);
  margin-bottom: var(--spacing-sm);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.menu {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.menu-item {
  text-decoration: none;
  color: var(--color-text-light);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-sm);
  transition: background-color 0.3s, color 0.3s;
  font-size: var(--font-size-base);
  display: flex;
  align-items: center;
}

.menu-item:hover {
  background-color: var(--color-hover);
}

.menu-item.active {
  background-color: var(--color-primary);
  color: white;
  font-weight: 500;
}

.menu-icon {
  margin-right: var(--spacing-sm);
  width: 20px;
  text-align: center;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: var(--sidebar-width-mobile);
    padding: var(--spacing-md) var(--spacing-sm);
  }

  .sidebar-header .logo span,
  .menu-item span,
  .menu-title {
    display: none;
  }

  .menu-icon {
    margin-right: 0;
  }

  .logo-icon {
    margin-right: 0;
  }
}
