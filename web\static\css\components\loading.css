/*******************************************
 *          加载状态和消息样式
 *******************************************/
.loading-message {
  text-align: center;
  padding: var(--spacing-lg);
  color: var(--color-text-light);
  font-style: italic;
}

.empty-message {
  text-align: center;
  padding: var(--spacing-lg);
  color: var(--color-text-light);
}

.error-message {
  text-align: center;
  padding: var(--spacing-lg);
  color: var(--color-expense);
  font-weight: bold;
}

.app-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-md);
}

.loading-text {
  font-size: var(--font-size-md);
  color: var(--color-text);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
