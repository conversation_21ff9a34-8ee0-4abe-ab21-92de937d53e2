import{a as s,l as Y,m as L,h as H,s as D,c as y,e as n,f as u,d as p,w as l,g as A,o as f,i as h,t as J,n as K,H as O}from"./index--MNqwREY.js";import{useBudgetsStore as Q}from"./budgets-DJh-v5Y8.js";import{g as W}from"./categories-Zg0JKh5g.js";import{_ as X}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./transactions-CrdnwW_k.js";const Z={class:"edit-budget-page"},ee={key:0,class:"loading-container"},te={key:1,class:"error-container"},ae={key:2,class:"page-container"},oe={class:"currency-symbol"},ne={class:"submit-container"},le={__name:"Edit",setup(re){const B=A(),S=H(),V=Q(),g=s(!0),b=s(!1),$=s(null),i=s(!1),m=s(!1),v=s(!1),a=s({project_name:"",category:"",amount:"",currency:"MYR",year:new Date().getFullYear(),month:new Date().getMonth()+1,description:""}),U=Y(()=>!a.value.year||!a.value.month?"选择年月":`${a.value.year}年${a.value.month}月`),j=W("budget"),M=new Date().getFullYear(),w=[];for(let t=M-2;t<=M+5;t++)w.push({text:`${t}年`,value:t});const R=[{text:"01月",value:1},{text:"02月",value:2},{text:"03月",value:3},{text:"04月",value:4},{text:"05月",value:5},{text:"06月",value:6},{text:"07月",value:7},{text:"08月",value:8},{text:"09月",value:9},{text:"10月",value:10},{text:"11月",value:11},{text:"12月",value:12}],E=[{text:"马来西亚令吉 (MYR)",value:"MYR"},{text:"人民币 (CNY)",value:"CNY"},{text:"美元 (USD)",value:"USD"},{text:"欧元 (EUR)",value:"EUR"},{text:"新加坡元 (SGD)",value:"SGD"}];Y(()=>N(a.value.period));const N=t=>({monthly:"每月",weekly:"每周",custom:"自定义"})[t]||t,q=t=>({MYR:"RM",CNY:"¥",USD:"$",EUR:"€",SGD:"S$"})[t]||"RM";L(async()=>{await P()});const P=async()=>{g.value=!0;try{const t=S.params.id,e=await V.fetchBudget(t);if(e){$.value=e;const d=new Date(e.start_date);a.value={project_name:e.project_name||"",category:e.category,amount:e.amount.toString(),currency:e.currency,year:d.getFullYear(),month:d.getMonth()+1,description:e.description||""}}}catch(t){console.error("加载预算失败:",t),D({message:"加载预算失败",type:"fail"})}finally{g.value=!1}},F=({selectedOptions:t})=>{a.value.category=t[0].value,i.value=!1},I=({selectedOptions:t})=>{a.value.currency=t[0].value,m.value=!1},G=({selectedOptions:t})=>{a.value.year=t[0].value,a.value.month=t[1].value,v.value=!1},T=async()=>{b.value=!0;try{if(!a.value.year||!a.value.month){D({message:"请选择预算年月",type:"fail"});return}const t=a.value.year,e=a.value.month,d=`${t}-${String(e).padStart(2,"0")}-01`,x=new Date(t,e,0).getDate(),c=`${t}-${String(e).padStart(2,"0")}-${String(x).padStart(2,"0")}`,r={...a.value,amount:parseFloat(a.value.amount),period:"monthly",start_date:d,end_date:c};await V.updateBudget(S.params.id,r),O("预算更新成功"),B.back()}catch(t){console.error("更新预算失败:",t),D({message:t.message||"更新预算失败",type:"fail"})}finally{b.value=!1}};return(t,e)=>{const d=u("van-nav-bar"),x=u("van-loading"),c=u("van-button"),r=u("van-field"),C=u("van-cell-group"),z=u("van-form"),_=u("van-picker"),k=u("van-popup");return f(),y("div",Z,[n(d,{title:"编辑预算","left-arrow":"",onClickLeft:e[0]||(e[0]=o=>t.$router.back()),fixed:"",placeholder:""}),g.value?(f(),y("div",ee,[n(x,{size:"24px"}),e[17]||(e[17]=p("span",null,"加载中...",-1))])):$.value?(f(),y("div",ae,[n(z,{onSubmit:T},{default:l(()=>[n(C,{inset:"",title:"基本信息"},{default:l(()=>[n(r,{modelValue:a.value.project_name,"onUpdate:modelValue":e[2]||(e[2]=o=>a.value.project_name=o),name:"project_name",label:"项目名称",placeholder:"请输入预算项目名称",rules:[{required:!0,message:"请输入项目名称"}]},null,8,["modelValue"]),n(r,{modelValue:a.value.category,"onUpdate:modelValue":e[3]||(e[3]=o=>a.value.category=o),name:"category",label:"预算类别",placeholder:"选择支出类别",readonly:"","is-link":"",onClick:e[4]||(e[4]=o=>i.value=!0),rules:[{required:!0,message:"请选择预算类别"}]},null,8,["modelValue"]),n(r,{modelValue:a.value.amount,"onUpdate:modelValue":e[5]||(e[5]=o=>a.value.amount=o),name:"amount",label:"预算金额",placeholder:"请输入预算金额",type:"number",rules:[{required:!0,message:"请输入预算金额"},{pattern:/^\d+(\.\d{1,2})?$/,message:"请输入有效的金额"}]},{"left-icon":l(()=>[p("span",oe,J(q(a.value.currency)),1)]),_:1},8,["modelValue"]),n(r,{modelValue:a.value.currency,"onUpdate:modelValue":e[6]||(e[6]=o=>a.value.currency=o),name:"currency",label:"货币类型",placeholder:"选择货币",readonly:"","is-link":"",onClick:e[7]||(e[7]=o=>m.value=!0),rules:[{required:!0,message:"请选择货币类型"}]},null,8,["modelValue"])]),_:1}),n(C,{inset:"",title:"预算周期"},{default:l(()=>[n(r,{modelValue:U.value,"onUpdate:modelValue":e[8]||(e[8]=o=>U.value=o),name:"period",label:"预算月份",placeholder:"选择年月",readonly:"","is-link":"",onClick:e[9]||(e[9]=o=>v.value=!0),rules:[{required:!0,message:"请选择预算月份"}]},null,8,["modelValue"])]),_:1}),n(C,{inset:"",title:"其他信息"},{default:l(()=>[n(r,{modelValue:a.value.description,"onUpdate:modelValue":e[10]||(e[10]=o=>a.value.description=o),name:"description",label:"预算描述",placeholder:"添加预算说明（可选）",type:"textarea",rows:"3",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1}),p("div",ne,[n(c,{type:"primary","native-type":"submit",block:"",round:"",loading:b.value},{default:l(()=>e[21]||(e[21]=[h(" 更新预算 ")])),_:1,__:[21]},8,["loading"])])]),_:1})])):(f(),y("div",te,[e[19]||(e[19]=p("div",{class:"error-icon"},"❌",-1)),e[20]||(e[20]=p("p",null,"预算不存在或已被删除",-1)),n(c,{type:"primary",size:"small",onClick:e[1]||(e[1]=o=>t.$router.back())},{default:l(()=>e[18]||(e[18]=[h(" 返回 ")])),_:1,__:[18]})])),n(k,{show:i.value,"onUpdate:show":e[12]||(e[12]=o=>i.value=o),position:"bottom"},{default:l(()=>[n(_,{columns:K(j),onConfirm:F,onCancel:e[11]||(e[11]=o=>i.value=!1)},null,8,["columns"])]),_:1},8,["show"]),n(k,{show:m.value,"onUpdate:show":e[14]||(e[14]=o=>m.value=o),position:"bottom"},{default:l(()=>[n(_,{columns:E,onConfirm:I,onCancel:e[13]||(e[13]=o=>m.value=!1)})]),_:1},8,["show"]),n(k,{show:v.value,"onUpdate:show":e[16]||(e[16]=o=>v.value=o),position:"bottom"},{default:l(()=>[n(_,{columns:[w,R],"default-index":[w.findIndex(o=>o.value===a.value.year),R.findIndex(o=>o.value===a.value.month)],title:"选择年月",onConfirm:G,onCancel:e[15]||(e[15]=o=>v.value=!1)},null,8,["columns","default-index"])]),_:1},8,["show"])])}}},ve=X(le,[["__scopeId","data-v-92838dd0"]]);export{ve as default};
