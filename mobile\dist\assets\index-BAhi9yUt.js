import{a as y,l as f,m as Y,s as L,c as m,e as l,d as t,w as i,f as c,p as g,t as n,n as r,g as T,o,i as h,F as M,k as S,q as x,v as E}from"./index--MNqwREY.js";import{useAccountsStore as G}from"./accounts-bk1VVrk3.js";import{g as q,f as u}from"./format-wz8GKlWC.js";import{_ as J}from"./_plugin-vue_export-helper-DlAUqK2U.js";const W={class:"investments-page"},j={class:"page-container"},H={class:"investment-summary-card"},K={class:"summary-header"},O={class:"summary-icon"},Q={class:"total-investment"},X={class:"currency"},Z={class:"amount"},tt={class:"investment-stats"},st={class:"stat-item"},et={class:"stat-value positive"},at={class:"stat-item"},nt={class:"stat-value positive"},ot={key:0,class:"loading-container"},lt={key:1,class:"empty-container"},it={class:"account-header"},rt={class:"account-info"},dt={class:"account-details"},ct={class:"account-type"},ut={class:"account-balance"},vt={class:"current-balance"},mt={class:"account-stats"},pt={class:"stat-row"},_t={class:"stat-item"},yt={class:"stat-value"},ft={class:"stat-item"},bt={class:"stat-value"},gt={class:"stat-row"},ht={class:"stat-item"},kt={class:"stat-value"},wt={class:"stat-item"},Ct={class:"stat-value"},Mt={class:"stat-row"},St={class:"stat-item"},xt={class:"stat-item"},Rt={class:"currency-picker"},$t={class:"picker-header"},At={__name:"index",setup(Ft){T();const k=G(),b=y(!1),p=y(!1),v=y(localStorage.getItem("investments-currency")||"MYR"),R=y([{code:"MYR",name:"马来西亚林吉特",symbol:"RM"},{code:"USD",name:"美元",symbol:"$"},{code:"CNY",name:"人民币",symbol:"¥"},{code:"EUR",name:"欧元",symbol:"€"},{code:"GBP",name:"英镑",symbol:"£"},{code:"JPY",name:"日元",symbol:"¥"},{code:"SGD",name:"新加坡元",symbol:"S$"}]),d=f(()=>k.accounts.filter(a=>a.type==="investment"&&a.currency===v.value).map(a=>({...a,deposits:Math.floor(Math.random()*1e4)+5e3,withdrawals:Math.floor(Math.random()*3e3),profit:Math.floor(Math.random()*2e3)-500,return_rate:(Math.random()*20-5).toFixed(2)}))),$=f(()=>d.value.reduce((a,s)=>a+(s.current_balance||s.initial_balance||0),0)),A=f(()=>d.value.reduce((a,s)=>a+(s.profit||0),0)),F=f(()=>d.value.length===0?"0.00":(d.value.reduce((s,_)=>s+parseFloat(_.return_rate||0),0)/d.value.length).toFixed(2));Y(async()=>{await z()});const z=async()=>{b.value=!0;try{await k.fetchAccountsWithBalances()}catch(a){console.error("加载投资账户失败:",a),L({message:"加载投资账户失败",type:"fail"})}finally{b.value=!1}},B=a=>({investment:"投资账户"})[a]||a,N=a=>{v.value=a,localStorage.setItem("investments-currency",a),p.value=!1},D=a=>a?Number(a).toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}):"0.00";return(a,s)=>{const _=c("van-button"),I=c("van-nav-bar"),w=c("van-icon"),P=c("van-loading"),C=c("van-cell-group"),U=c("van-cell"),V=c("van-popup");return o(),m("div",W,[l(I,{title:"投资账号","left-arrow":"",onClickLeft:s[1]||(s[1]=e=>a.$router.back()),fixed:"",placeholder:"","safe-area-inset-top":""},{right:i(()=>[l(_,{size:"mini",type:"primary",plain:"",onClick:s[0]||(s[0]=e=>p.value=!0),class:"currency-btn"},{default:i(()=>[h(n(v.value),1)]),_:1})]),_:1}),t("div",j,[t("div",H,[t("div",K,[s[5]||(s[5]=t("div",{class:"summary-title"},[t("h3",null,"投资总资产"),t("div",{class:"summary-year"},"2025年")],-1)),t("div",O,[l(w,{name:"chart-trending-o",size:"24"})])]),t("div",Q,[t("span",X,n(r(q)(v.value)),1),t("span",Z,n(D($.value)),1)]),t("div",tt,[t("div",st,[s[6]||(s[6]=t("span",{class:"stat-label"},"总收益",-1)),t("span",et,"+"+n(r(u)(A.value,v.value)),1)]),t("div",at,[s[7]||(s[7]=t("span",{class:"stat-label"},"总回报率",-1)),t("span",nt,"+"+n(F.value)+"%",1)])])]),b.value?(o(),m("div",ot,[l(P,{size:"24px"}),s[8]||(s[8]=t("span",null,"加载中...",-1))])):d.value.length===0?(o(),m("div",lt,[s[10]||(s[10]=t("div",{class:"empty-icon"},"📈",-1)),s[11]||(s[11]=t("p",null,"暂无投资账户",-1)),l(_,{type:"primary",size:"small",onClick:s[2]||(s[2]=e=>a.$router.push("/account/add"))},{default:i(()=>s[9]||(s[9]=[h(" 添加投资账户 ")])),_:1,__:[9]})])):(o(),g(C,{key:2,inset:"",title:"我的投资账户"},{default:i(()=>[(o(!0),m(M,null,S(d.value,e=>(o(),m("div",{key:e.id,class:"investment-account-card"},[t("div",it,[t("div",rt,[s[12]||(s[12]=t("div",{class:"account-icon"},"📈",-1)),t("div",dt,[t("h4",null,n(e.name),1),t("span",ct,n(B(e.type)),1)])]),t("div",ut,[t("span",vt,n(r(u)(e.current_balance||e.initial_balance,e.currency)),1)])]),t("div",mt,[t("div",pt,[t("div",_t,[s[13]||(s[13]=t("span",{class:"stat-label"},"初始余额",-1)),t("span",yt,n(r(u)(e.initial_balance,e.currency)),1)]),t("div",ft,[s[14]||(s[14]=t("span",{class:"stat-label"},"当前余额",-1)),t("span",bt,n(r(u)(e.current_balance||e.initial_balance,e.currency)),1)])]),t("div",gt,[t("div",ht,[s[15]||(s[15]=t("span",{class:"stat-label"},"入金数",-1)),t("span",kt,n(r(u)(e.deposits||0,e.currency)),1)]),t("div",wt,[s[16]||(s[16]=t("span",{class:"stat-label"},"出金数",-1)),t("span",Ct,n(r(u)(e.withdrawals||0,e.currency)),1)])]),t("div",Mt,[t("div",St,[s[17]||(s[17]=t("span",{class:"stat-label"},"收益",-1)),t("span",{class:x(["stat-value",{positive:e.profit>0,negative:e.profit<0}])},n(e.profit>0?"+":"")+n(r(u)(e.profit||0,e.currency)),3)]),t("div",xt,[s[18]||(s[18]=t("span",{class:"stat-label"},"回报率",-1)),t("span",{class:x(["stat-value",{positive:e.return_rate>0,negative:e.return_rate<0}])},n(e.return_rate>0?"+":"")+n(e.return_rate||0)+"% ",3)])])])]))),128))]),_:1}))]),l(V,{show:p.value,"onUpdate:show":s[4]||(s[4]=e=>p.value=e),position:"bottom",round:""},{default:i(()=>[t("div",Rt,[t("div",$t,[s[20]||(s[20]=t("h3",null,"选择货币",-1)),l(_,{size:"mini",type:"primary",onClick:s[3]||(s[3]=e=>p.value=!1)},{default:i(()=>s[19]||(s[19]=[h(" 确定 ")])),_:1,__:[19]})]),l(C,null,{default:i(()=>[(o(!0),m(M,null,S(R.value,e=>(o(),g(U,{key:e.code,title:e.name,label:e.code,value:e.symbol,clickable:"",onClick:zt=>N(e.code)},{"right-icon":i(()=>[v.value===e.code?(o(),g(w,{key:0,name:"success",color:"#1989fa"})):E("",!0)]),_:2},1032,["title","label","value","onClick"]))),128))]),_:1})])]),_:1},8,["show"])])}}},Pt=J(At,[["__scopeId","data-v-671ac8f7"]]);export{Pt as default};
