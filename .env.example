# 机器人配置文件示例
# 复制此文件为 .env 并根据实际情况修改配置

# 基础配置
BOT_DEBUG=True
WEB_API_BASE_URL=http://localhost:8000/api
WEB_API_TOKEN=finance_app_token
BOT_SERVER_HOST=0.0.0.0
BOT_SERVER_PORT=8001

# 预算检查配置
# 预算检查间隔（秒）
BUDGET_CHECK_INTERVAL=300
# 预警阈值（80%）
BUDGET_WARNING_THRESHOLD=0.8
# 危险阈值（100%）
BUDGET_DANGER_THRESHOLD=1.0

# Telegram机器人配置
TELEGRAM_ENABLED=False
TELEGRAM_BOT_TOKEN=YOUR_BOT_TOKEN
# 可选，留空会自动获取
TELEGRAM_CHAT_ID=

# 日志配置
BOT_LOG_LEVEL=INFO
BOT_LOG_FILE=logs/bot.log

# === 性能优化配置 ===

# 启用预算检查功能
DISABLE_BUDGET_CHECK=0

# 启用缓存
ENABLE_CACHE=1
CACHE_TIMEOUT=300

# 数据库优化
DB_POOL_SIZE=5
DB_TIMEOUT=30

# API优化
API_TIMEOUT=10
MAX_WORKERS=2

# 日志级别（减少日志输出）
LOG_LEVEL=WARNING
