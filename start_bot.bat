@echo off
echo ===================================================
echo Telegram Bot Launcher
echo ===================================================
echo Startup time: %date% %time%

REM Create logs directory
if not exist logs mkdir logs
echo Startup time: %date% %time% > logs\bot_startup.log

REM Check if .env exists
if not exist .env (
    echo Error: .env file not found
    echo Please create .env file and configure Telegram Bot Token
    echo.
    echo Example configuration:
    echo TELEGRAM_ENABLED=True
    echo TELEGRAM_BOT_TOKEN=your_bot_token
    echo.
    echo Please refer to .env.example file
    echo Error: .env file not found >> logs\bot_startup.log
    pause
    exit /b 1
)

REM Check if Python is installed
echo Checking Python environment... >> logs\bot_startup.log
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python not installed or not in PATH
    echo Python not installed >> logs\bot_startup.log
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist venv (
    echo Creating virtual environment...
    echo Creating virtual environment... >> logs\bot_startup.log
    python -m venv venv
    if %errorlevel% neq 0 (
        echo Failed to create virtual environment
        echo Failed to create virtual environment >> logs\bot_startup.log
        pause
        exit /b 1
    )
    echo Virtual environment created successfully
    echo Virtual environment created successfully >> logs\bot_startup.log
)

REM Activate virtual environment
echo Activating virtual environment...
echo Activating virtual environment... >> logs\bot_startup.log
call venv\Scripts\activate.bat

REM Install dependencies if needed
if exist requirements.txt (
    echo Checking dependencies...
    echo Installing dependencies... >> logs\bot_startup.log
    pip install -r requirements.txt > logs\bot_pip_install.log 2>&1
    if %errorlevel% neq 0 (
        echo Warning: Failed to install dependencies, check logs\bot_pip_install.log
        echo Failed to install dependencies >> logs\bot_startup.log
    ) else (
        echo Dependencies installed successfully
        echo Dependencies installed successfully >> logs\bot_startup.log
    )
)

echo.
echo ===================================================
echo Starting Interactive Telegram Bot
echo ===================================================
echo Features:
echo - Interactive keyboard accounting
echo - Account selection
echo - File attachment support
echo - Natural language accounting (backup)
echo - Balance and budget queries
echo ===================================================
echo Press Ctrl+C to stop the bot
echo.

echo Starting bot... >> logs\bot_startup.log
python -m bot.bot

echo.
echo Bot stopped
echo Bot stopped >> logs\bot_startup.log
pause
