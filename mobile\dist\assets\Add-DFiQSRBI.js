import{a as p,r as $,l as r,c as M,e as n,d as E,w as s,f as u,g as F,o as q,i as I,s as i}from"./index--MNqwREY.js";import{useAccountsStore as J}from"./accounts-bk1VVrk3.js";import{_ as L}from"./_plugin-vue_export-helper-DlAUqK2U.js";const z={class:"add-account-page"},H={class:"page-container"},K={__name:"Add",setup(W){const g=F(),N=J(),C=p(!1),v=p(!1),c=p(!1),m=p(!1),t=$({name:"",type:"",currency:"MYR",initial_balance:"0",description:""}),h=[{text:"🏦 银行账户",value:"bank"},{text:"💵 现金",value:"cash"},{text:"💰 投资账户",value:"investment"},{text:"🏪 电子钱包",value:"ewallet"}],y=[{text:"MYR - 马来西亚林吉特",value:"MYR"},{text:"USD - 美元",value:"USD"},{text:"CNY - 人民币",value:"CNY"},{text:"EUR - 欧元",value:"EUR"},{text:"GBP - 英镑",value:"GBP"},{text:"JPY - 日元",value:"JPY"},{text:"SGD - 新加坡元",value:"SGD"}],B={MYR:{bank:[{text:"Maybank",value:"Maybank"},{text:"HLB",value:"HLB"},{text:"PBB",value:"PBB"},{text:"GXBank",value:"GXBank"}],ewallet:[{text:"TNG eWallet",value:"TNG eWallet"}],investment:[{text:"KDI",value:"KDI"},{text:"moomoo",value:"moomoo"},{text:"EPF",value:"EPF"}],cash:[{text:"Cash",value:"Cash"}]},USD:{bank:[{text:"ABA",value:"ABA"}],ewallet:[{text:"Cash",value:"Cash"}],investment:[{text:"Cash",value:"Cash"}],cash:[{text:"Cash",value:"Cash"}]},CNY:{bank:[{text:"Cash",value:"Cash"}],ewallet:[{text:"Cash",value:"Cash"}],investment:[{text:"Cash",value:"Cash"}],cash:[{text:"Cash",value:"Cash"}]},EUR:{bank:[{text:"Cash",value:"Cash"}],ewallet:[{text:"Cash",value:"Cash"}],investment:[{text:"Cash",value:"Cash"}],cash:[{text:"Cash",value:"Cash"}]},GBP:{bank:[{text:"Cash",value:"Cash"}],ewallet:[{text:"Cash",value:"Cash"}],investment:[{text:"Cash",value:"Cash"}],cash:[{text:"Cash",value:"Cash"}]},JPY:{bank:[{text:"Cash",value:"Cash"}],ewallet:[{text:"Cash",value:"Cash"}],investment:[{text:"Cash",value:"Cash"}],cash:[{text:"Cash",value:"Cash"}]},SGD:{bank:[{text:"Cash",value:"Cash"}],ewallet:[{text:"Cash",value:"Cash"}],investment:[{text:"Cash",value:"Cash"}],cash:[{text:"Cash",value:"Cash"}]}},U=r(()=>h),P=r(()=>y),A=r(()=>{var l;return!t.currency||!t.type?[]:((l=B[t.currency])==null?void 0:l[t.type])||[]}),b=r(()=>{const l=h.find(e=>e.value===t.type);return l?l.text:""}),w=r(()=>{const l=y.find(e=>e.value===t.currency);return l?l.text:""}),k=r(()=>t.name),Y=({selectedOptions:l})=>{var o;const e=((o=l[0])==null?void 0:o.value)||"";e!==t.type&&(t.type=e,t.name=""),v.value=!1},G=({selectedOptions:l})=>{var o;const e=((o=l[0])==null?void 0:o.value)||"MYR";e!==t.currency&&(t.currency=e,t.type="",t.name=""),c.value=!1},S=({selectedOptions:l})=>{var e;t.name=((e=l[0])==null?void 0:e.value)||"",m.value=!1},D=()=>t.name.trim()?t.type?t.currency?t.initial_balance===""||isNaN(parseFloat(t.initial_balance))?(i("请输入有效的初始余额"),!1):!0:(i("请选择货币"),!1):(i("请选择账户类型"),!1):(i("请输入账户名称"),!1),_=async()=>{if(D()){C.value=!0;try{const l={name:t.name.trim(),type:t.type,currency:t.currency,initial_balance:parseFloat(t.initial_balance),description:t.description.trim()};await N.createAccount(l),i({message:"账户创建成功",type:"success"}),g.back()}catch(l){console.error("创建账户失败:",l),i({message:l.message||"创建账户失败",type:"fail"})}finally{C.value=!1}}};return(l,e)=>{const o=u("van-button"),R=u("van-nav-bar"),d=u("van-field"),V=u("van-cell-group"),T=u("van-form"),f=u("van-picker"),x=u("van-popup");return q(),M("div",z,[n(R,{title:"添加账户","left-arrow":"",onClickLeft:e[0]||(e[0]=a=>l.$router.back()),fixed:"",placeholder:""},{right:s(()=>[n(o,{type:"primary",size:"small",loading:C.value,onClick:_},{default:s(()=>e[15]||(e[15]=[I(" 保存 ")])),_:1,__:[15]},8,["loading"])]),_:1}),E("div",H,[n(T,{onSubmit:_,class:"account-form"},{default:s(()=>[n(V,{inset:"",title:"基本信息"},{default:s(()=>[n(d,{modelValue:w.value,"onUpdate:modelValue":e[1]||(e[1]=a=>w.value=a),label:"货币",placeholder:"选择货币",readonly:"","is-link":"",onClick:e[2]||(e[2]=a=>c.value=!0),rules:[{required:!0,message:"请选择货币"}]},null,8,["modelValue"]),n(d,{modelValue:b.value,"onUpdate:modelValue":e[3]||(e[3]=a=>b.value=a),label:"账户类型",placeholder:"选择账户类型",readonly:"","is-link":"",onClick:e[4]||(e[4]=a=>v.value=!0),rules:[{required:!0,message:"请选择账户类型"}]},null,8,["modelValue"]),n(d,{modelValue:k.value,"onUpdate:modelValue":e[5]||(e[5]=a=>k.value=a),label:"账户名称",placeholder:"选择账户名称",readonly:"","is-link":"",onClick:e[6]||(e[6]=a=>m.value=!0),rules:[{required:!0,message:"请选择账户名称"}]},null,8,["modelValue"]),n(d,{modelValue:t.initial_balance,"onUpdate:modelValue":e[7]||(e[7]=a=>t.initial_balance=a),label:"初始余额",type:"number",placeholder:"0.00",rules:[{required:!0,message:"请输入初始余额"}]},null,8,["modelValue"])]),_:1}),n(V,{inset:"",title:"附加信息"},{default:s(()=>[n(d,{modelValue:t.description,"onUpdate:modelValue":e[8]||(e[8]=a=>t.description=a),label:"描述",type:"textarea",placeholder:"添加账户描述（可选）",rows:"3",autosize:"",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1})]),n(x,{show:v.value,"onUpdate:show":e[10]||(e[10]=a=>v.value=a),position:"bottom"},{default:s(()=>[n(f,{columns:U.value,title:"选择账户类型",onConfirm:Y,onCancel:e[9]||(e[9]=a=>v.value=!1)},null,8,["columns"])]),_:1},8,["show"]),n(x,{show:c.value,"onUpdate:show":e[12]||(e[12]=a=>c.value=a),position:"bottom"},{default:s(()=>[n(f,{columns:P.value,title:"选择货币",onConfirm:G,onCancel:e[11]||(e[11]=a=>c.value=!1)},null,8,["columns"])]),_:1},8,["show"]),n(x,{show:m.value,"onUpdate:show":e[14]||(e[14]=a=>m.value=a),position:"bottom"},{default:s(()=>[n(f,{columns:A.value,title:"选择账户名称",onConfirm:S,onCancel:e[13]||(e[13]=a=>m.value=!1)},null,8,["columns"])]),_:1},8,["show"])])}}},Q=L(K,[["__scopeId","data-v-55e8700a"]]);export{Q as default};
