<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>个人财务系统 - 钱管家</title>
  <!-- 引入 Chart.js（CDN） -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <!-- 引入 Font Awesome 图标库 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- 引入SQL.js（本地优先，CDN备用） -->
  <script>
    // 检测本地SQL.js是否可用
    function loadSqlJs() {
      try {
        // 创建一个全局的initSqlJs函数
        window.initSqlJs = function(config) {
          console.log('SQL.js模拟初始化');

          // 返回一个Promise，模拟SQL.js的行为
          return new Promise((resolve, reject) => {
            // 创建一个模拟的SQL对象
            const SQL = {
              // 模拟Database类
              Database: class MockDatabase {
                constructor(data) {
                  console.log('创建模拟数据库');
                  this.data = data || null;
                  this.tables = {};
                }

                run(sql, params) {
                  console.log('执行SQL:', sql, params);
                  // 提取表名（简单实现）
                  const match = sql.match(/CREATE TABLE IF NOT EXISTS (\w+)/i);
                  if (match) {
                    const tableName = match[1];
                    this.tables[tableName] = { rows: [] };
                  }
                  return true;
                }

                prepare(sql) {
                  console.log('准备SQL:', sql);
                  return {
                    bind: (params) => console.log('绑定参数:', params),
                    step: () => false,
                    getAsObject: () => ({}),
                    free: () => console.log('释放语句')
                  };
                }

                exec(sql) {
                  console.log('执行SQL:', sql);
                  return [{ columns: [], values: [] }];
                }

                export() {
                  console.log('导出数据库');
                  return new Uint8Array(0);
                }

                close() {
                  console.log('关闭数据库');
                }
              }
            };

            // 解析Promise
            resolve(SQL);
          });
        };

        // 通知页面SQL.js已加载
        console.log('SQL.js模拟文件已加载');

        // 创建一个自定义事件，通知应用程序SQL.js已加载
        document.dispatchEvent(new Event('sqljs-loaded'));
      } catch (e) {
        console.error('SQL.js加载失败:', e);
      }
    }

    // 页面加载时执行
    window.addEventListener('DOMContentLoaded', loadSqlJs);
  </script>
  <!-- 引入样式文件 -->
  <link rel="stylesheet" href="assets/css/main.css">
  <link rel="stylesheet" href="assets/css/variables.css">

  <!-- 设备检测和自适应重定向 -->
  <script src="assets/js/device-redirect.js"></script>

  <link rel="stylesheet" href="assets/css/components/sidebar.css">
  <link rel="stylesheet" href="assets/css/components/dashboard.css">
  <link rel="stylesheet" href="assets/css/components/transactions.css">
  <link rel="stylesheet" href="assets/css/components/storage-mode.css">
  <link rel="stylesheet" href="assets/css/components/loading.css">
  <link rel="stylesheet" href="assets/css/components/sync-manager.css">
  <link rel="stylesheet" href="assets/css/components/settings.css">
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      display: flex;
      min-height: 100vh;
    }

    .sidebar {
      width: 250px;
      background-color: #fff;
      color: #333;
      padding: 20px;
      flex-shrink: 0;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }

    .main-content {
      flex: 1;
      padding: 20px;
      background-color: #f5f5f5;
    }

    .menu-title {
      font-size: 12px;
      margin-bottom: 10px;
      color: #95a5a6;
      font-weight: bold;
    }

    .menu-item {
      display: flex;
      align-items: center;
      padding: 10px;
      margin-bottom: 5px;
      color: #333;
      text-decoration: none;
      border-radius: 5px;
      transition: all 0.3s ease;
    }

    .menu-item:hover {
      background-color: #f0f0f0;
    }

    .menu-item.active {
      background-color: #4e73df;
      color: white;
    }

    .menu-icon {
      margin-right: 10px;
      font-size: 20px;
    }

    .menu-section {
      margin-top: 20px;
      margin-bottom: 10px;
    }

    .logo {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }

    .logo-icon {
      width: 40px;
      height: 40px;
      background-color: #3a5a8c;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
      margin-right: 10px;
      font-weight: bold;
      font-size: 20px;
    }
  </style>
</head>
<body>
  <!-- 左侧卡片式菜单 -->
  <aside class="sidebar">
    <!-- 侧边栏头部：Logo -->
    <div class="sidebar-header">
      <div class="logo">
        <div class="logo-icon">钱</div>
        <span>钱管家</span>
      </div>
    </div>

    <!-- 导航菜单 -->
    <div class="menu-section">
      <div class="menu-title" data-i18n="menu">菜单</div>
      <nav class="menu">
        <a href="javascript:void(0)" class="menu-item active" data-page="dashboardPage">
          <div class="menu-icon">📊</div>
          <span data-i18n="dashboard">仪表盘</span>
        </a>
        <a href="javascript:void(0)" class="menu-item" data-page="transactionsPage">
          <div class="menu-icon">💰</div>
          <span data-i18n="transactions">交易记录</span>
        </a>
        <a href="javascript:void(0)" class="menu-item" data-page="accountsPage">
          <div class="menu-icon">🏦</div>
          <span data-i18n="accounts">账户管理</span>
        </a>
      </nav>
    </div>

    <div class="menu-section">
      <div class="menu-title" data-i18n="financial">财务</div>
      <nav class="menu">
        <a href="javascript:void(0)" class="menu-item" data-page="budgetsPage">
          <div class="menu-icon">💼</div>
          <span data-i18n="budgets">预算管理</span>
        </a>
        <a href="javascript:void(0)" class="menu-item" data-page="goalsPage">
          <div class="menu-icon">🎯</div>
          <span data-i18n="goals">财务目标</span>
        </a>
        <a href="javascript:void(0)" class="menu-item" data-page="settingsPage">
          <div class="menu-icon">⚙️</div>
          <span data-i18n="settings">设置</span>
        </a>
      </nav>
    </div>


  </aside>

  <!-- 右侧主内容区域 -->
  <main class="main-content">
    <!-- 顶部条 -->
    <div class="topbar">
      <div class="topbar-left">
        <h1 data-i18n="dashboard">仪表盘</h1>
        <div class="date" id="currentDate">2023年10月15日</div>
      </div>
      <div class="topbar-right">
        <button class="search-btn" title="搜索">
          <i class="fas fa-search"></i>
        </button>
        <button class="notification-btn" title="通知">
          <i class="fas fa-bell"></i>
        </button>
        <div class="user-profile">
          <div class="user-avatar">U</div>
          <div class="user-info">
            <div class="user-name">用户名</div>
            <div class="user-role">个人用户</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Dashboard页面 -->
    <div id="dashboardPage" class="page active">
      <!-- 货币筛选器 -->
      <div class="currency-filter">
        <label for="dashboardCurrency">货币筛选：</label>
        <select id="dashboardCurrency">
          <!-- 货币选项将通过JavaScript动态添加 -->
        </select>
      </div>

      <!-- 统计卡片 -->
      <div class="dashboard-grid">
        <div class="stat-card">
          <div class="stat-card-header">
            <div class="stat-card-icon">💰</div>
            <div class="stat-card-title" data-i18n="total_income">总收入</div>
          </div>
          <div class="stat-card-value" id="totalIncome">RM 0.00</div>
          <div class="stat-card-comparison">
            <span data-i18n="compared_to_last_month">较上月</span> <span class="stat-card-badge positive" id="incomeChange">+0%</span>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-card-header">
            <div class="stat-card-icon">💸</div>
            <div class="stat-card-title" data-i18n="total_expense">总支出</div>
          </div>
          <div class="stat-card-value" id="totalExpense">RM 0.00</div>
          <div class="stat-card-comparison">
            <span data-i18n="compared_to_last_month">较上月</span> <span class="stat-card-badge negative" id="expenseChange">+0%</span>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-card-header">
            <div class="stat-card-icon">📊</div>
            <div class="stat-card-title" data-i18n="net_worth">净资产</div>
          </div>
          <div class="stat-card-value" id="netWorth">RM 0.00</div>
          <div class="stat-card-comparison">
            <span data-i18n="compared_to_last_month">较上月</span> <span class="stat-card-badge positive" id="netWorthChange">+0%</span>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="chart-row">
        <div class="chart-card">
          <div class="chart-header">
            <div class="chart-title" data-i18n="income_expense_trend">收支趋势</div>
            <div class="chart-period">
              <span data-i18n="last_6_months">最近6个月</span> <span class="chart-period-icon">▼</span>
            </div>
          </div>
          <div class="chart-container">
            <canvas id="incomeExpenseChart"></canvas>
          </div>
        </div>
        <div class="chart-card">
          <div class="chart-header">
            <div class="chart-title" data-i18n="expense_by_category">支出分类</div>
            <div class="chart-period">
              <span data-i18n="this_month">本月</span> <span class="chart-period-icon">▼</span>
            </div>
          </div>
          <div class="chart-container">
            <canvas id="expenseCategoryChart"></canvas>
          </div>
        </div>
      </div>

      <!-- 最近交易 -->
      <div class="chart-card customer-growth">
        <div class="customer-growth-header">
          <div class="customer-growth-title" data-i18n="recent_transactions">最近交易</div>
        </div>
        <table class="transaction-table" id="recentTransactionsTable">
          <thead>
            <tr>
              <th data-i18n="date">日期</th>
              <th data-i18n="type">类型</th>
              <th data-i18n="category">类别</th>
              <th data-i18n="description">描述</th>
              <th data-i18n="amount">金额</th>
            </tr>
          </thead>
          <tbody id="recentTransactionsList">
            <!-- 最近交易数据将通过JavaScript动态添加 -->
          </tbody>
        </table>
      </div>
    </div>

    <!-- Transactions页面 -->
    <div id="transactionsPage" class="page">
      <div class="transaction-container">
        <div class="transaction-header">
          <div class="transaction-title" data-i18n="transaction_management">交易管理</div>
          <div class="transaction-actions">
            <button id="exportTransactionsBtn" class="action-button" title="导出交易记录">
              <i class="fas fa-file-export"></i> 导出CSV
            </button>
            <button id="importTransactionsBtn" class="action-button" title="导入交易记录">
              <i class="fas fa-file-import"></i> 导入CSV
            </button>
          </div>
        </div>

        <!-- 交易表单 -->
        <form id="transactionForm" class="transaction-form">
          <div class="form-row">
            <div class="form-group">
              <label for="transactionDate" data-i18n="date">日期</label>
              <input type="date" id="transactionDate" required>
            </div>
            <div class="form-group">
              <label for="transactionType" data-i18n="type">类型</label>
              <select id="transactionType" required>
                <option value="" data-i18n="please_select">请选择</option>
                <option value="income" data-i18n="income">收入</option>
                <option value="expense" data-i18n="expense">支出</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="transactionCategory" data-i18n="category">类别</label>
              <select id="transactionCategory" required>
                <option value="" data-i18n="please_select">请选择</option>
                <!-- 类别选项将通过JavaScript动态添加 -->
              </select>
            </div>
            <div class="form-group">
              <label for="transactionDescription" data-i18n="description">描述</label>
              <input type="text" id="transactionDescription" required>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="transactionAccount" data-i18n="account">账户</label>
              <select id="transactionAccount" required>
                <!-- 账户选项将通过JavaScript动态添加 -->
              </select>
            </div>
            <div class="form-group">
              <label for="transactionCurrency" data-i18n="currency">货币</label>
              <select id="transactionCurrency" required>
                <!-- 货币选项将通过JavaScript动态添加 -->
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="transactionAmount" data-i18n="amount">金额</label>
              <input type="number" id="transactionAmount" step="0.01" min="0" required>
            </div>
            <div class="form-group">
              <label for="transactionAttachment" data-i18n="attachment">附件</label>
              <input type="file" id="transactionAttachment" accept="image/*,.pdf">
              <div id="attachmentPreview" class="attachment-preview"></div>
            </div>
          </div>

          <div class="form-group">
            <label>&nbsp;</label>
            <button type="submit" class="form-submit" id="saveTransaction" data-i18n="save">保存</button>
          </div>
        </form>

        <!-- 交易列表 -->
        <table class="transaction-table">
          <thead>
            <tr>
              <th data-i18n="date">日期</th>
              <th data-i18n="type">类型</th>
              <th data-i18n="category">类别</th>
              <th data-i18n="description">描述</th>
              <th data-i18n="account">账户</th>
              <th data-i18n="amount">金额</th>
              <th data-i18n="attachment">附件</th>
              <th data-i18n="actions">操作</th>
            </tr>
          </thead>
          <tbody id="transactionsList">
            <!-- 交易数据将通过JavaScript动态添加 -->
          </tbody>
        </table>
      </div>
    </div>

    <!-- 账户页面 -->
    <div id="accountsPage" class="page">
      <div class="accounts-container">
        <div class="accounts-header">
          <div class="accounts-title" data-i18n="account_management">账户管理</div>
        </div>

        <!-- 账户表单 -->
        <form id="accountForm" class="account-form">
          <div class="form-row">
            <div class="form-group">
              <label for="accountName" data-i18n="account_name">账户名称</label>
              <input type="text" id="accountName" required>
            </div>
            <div class="form-group">
              <label for="accountType" data-i18n="account_type">账户类型</label>
              <select id="accountType" required>
                <option value="bank" data-i18n="bank_account">银行账户</option>
                <option value="cash" data-i18n="cash">现金</option>
                <option value="credit" data-i18n="credit_card">信用卡</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="accountCurrency" data-i18n="currency">货币</label>
              <select id="accountCurrency" required>
                <!-- 货币选项将通过JavaScript动态添加 -->
              </select>
            </div>
            <div class="form-group">
              <label for="accountInitialBalance" data-i18n="initial_balance">初始余额</label>
              <input type="number" id="accountInitialBalance" step="0.01" min="0" value="0">
            </div>
          </div>

          <div class="form-group">
            <label for="accountDescription" data-i18n="description">描述</label>
            <textarea id="accountDescription" rows="2"></textarea>
          </div>

          <div class="form-group">
            <label>&nbsp;</label>
            <button type="submit" class="form-submit" id="saveAccount" data-i18n="save">保存</button>
          </div>
        </form>

        <!-- 账户列表 -->
        <table class="account-table">
          <thead>
            <tr>
              <th data-i18n="account_name">账户名称</th>
              <th data-i18n="account_type">账户类型</th>
              <th data-i18n="currency">货币</th>
              <th data-i18n="initial_balance">初始余额</th>
              <th data-i18n="current_balance">当前余额</th>
              <th data-i18n="actions">操作</th>
            </tr>
          </thead>
          <tbody id="accountsList">
            <!-- 账户数据将通过JavaScript动态添加 -->
          </tbody>
        </table>
      </div>
    </div>

    <!-- 预算页面 -->
    <div id="budgetsPage" class="page">
      <div class="budgets-container">
        <div class="budgets-header">
          <div class="budgets-title" data-i18n="budget_management">预算管理</div>
        </div>

        <!-- 预算表单 -->
        <form id="budgetForm" class="budget-form">
          <div class="form-row">
            <div class="form-group">
              <label for="budgetPeriod" data-i18n="period">周期</label>
              <select id="budgetPeriod" required>
                <option value="monthly">每月</option>
                <option value="yearly">每年</option>
                <option value="specific">指定月份</option>
              </select>
            </div>
            <div class="form-group" id="budgetDescriptionGroup">
              <label for="budgetDescription" data-i18n="description">描述</label>
              <input type="text" id="budgetDescription" placeholder="例如：买电话">
            </div>
          </div>

          <div class="form-row" id="specificMonthSelector" style="display: none;">
            <div class="form-group">
              <label for="budgetYear">年份</label>
              <select id="budgetYear">
                <!-- 年份选项将通过JavaScript动态添加 -->
              </select>
            </div>
            <div class="form-group">
              <label for="budgetMonth">月份</label>
              <select id="budgetMonth">
                <option value="1">1月</option>
                <option value="2">2月</option>
                <option value="3">3月</option>
                <option value="4">4月</option>
                <option value="5">5月</option>
                <option value="6">6月</option>
                <option value="7">7月</option>
                <option value="8">8月</option>
                <option value="9">9月</option>
                <option value="10">10月</option>
                <option value="11">11月</option>
                <option value="12">12月</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="budgetCategory" data-i18n="category">类别</label>
              <select id="budgetCategory" required>
                <!-- 类别选项将通过JavaScript动态添加 -->
              </select>
            </div>
            <div class="form-group">
              <label for="budgetAmount" data-i18n="budget_amount">预算金额</label>
              <input type="number" id="budgetAmount" step="0.01" min="0" required>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="budgetCurrency" data-i18n="currency">货币</label>
              <select id="budgetCurrency" required>
                <!-- 货币选项将通过JavaScript动态添加 -->
              </select>
            </div>
          </div>

          <div class="form-row" id="dateRangeSelector">
            <div class="form-group">
              <label for="budgetStartDate" data-i18n="start_date">开始日期</label>
              <input type="date" id="budgetStartDate" required>
            </div>
            <div class="form-group">
              <label for="budgetEndDate" data-i18n="end_date">结束日期</label>
              <input type="date" id="budgetEndDate" required>
            </div>
          </div>

          <div class="form-group">
            <label>&nbsp;</label>
            <button type="submit" class="form-submit" id="saveBudget" data-i18n="save">保存</button>
          </div>
        </form>

        <!-- 预算列表 -->
        <table class="budget-table">
          <thead>
            <tr>
              <th data-i18n="category">类别</th>
              <th data-i18n="budget_amount">预算金额</th>
              <th data-i18n="period">周期</th>
              <th data-i18n="date_range">日期范围</th>
              <th data-i18n="description">描述</th>
              <th data-i18n="progress">进度</th>
              <th data-i18n="actions">操作</th>
            </tr>
          </thead>
          <tbody id="budgetsList">
            <!-- 预算数据将通过JavaScript动态添加 -->
          </tbody>
        </table>
      </div>
    </div>

    <!-- 财务目标页面 -->
    <div id="goalsPage" class="page">
      <div class="goals-container">
        <div class="goals-header">
          <div class="goals-title" data-i18n="goal_management">财务目标管理</div>
        </div>

        <!-- 净资产分配 -->
        <div class="net-asset-allocation">
          <div class="net-asset-header">
            <h3>净资产分配</h3>
            <div class="net-asset-summary">
              <div class="net-asset-total">
                <span>总净资产:</span>
                <span id="totalNetAsset">RM 0.00</span>
              </div>
              <div class="net-asset-allocated">
                <span>已分配:</span>
                <span id="allocatedAmount">RM 0.00</span>
              </div>
              <div class="net-asset-remaining">
                <span>剩余可分配:</span>
                <span id="remainingAmount">RM 0.00</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 目标表单 -->
        <form id="goalForm" class="goal-form">
          <div class="form-row">
            <div class="form-group">
              <label for="goalName" data-i18n="goal_name">目标名称</label>
              <input type="text" id="goalName" required>
            </div>
            <div class="form-group">
              <label for="goalTargetAmount" data-i18n="target_amount">目标金额</label>
              <input type="number" id="goalTargetAmount" step="0.01" min="0" required>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="goalCurrentAmount" data-i18n="current_amount">当前金额</label>
              <input type="number" id="goalCurrentAmount" step="0.01" min="0" value="0">
            </div>
            <div class="form-group">
              <label for="goalDescription" data-i18n="description">描述</label>
              <input type="text" id="goalDescription">
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="goalTargetDate" data-i18n="target_date">目标日期</label>
              <input type="date" id="goalTargetDate" required>
            </div>
          </div>

          <div class="form-group">
            <label>&nbsp;</label>
            <button type="submit" class="form-submit" id="saveGoal" data-i18n="save">保存</button>
          </div>
        </form>

        <!-- 进行中的目标 -->
        <div class="goals-section">
          <h3 data-i18n="active_goals">进行中的目标</h3>
          <div id="activeGoalsList" class="goals-list">
            <!-- 目标卡片将通过JavaScript动态添加 -->
          </div>
        </div>

        <!-- 已完成的目标 -->
        <div class="goals-section">
          <h3 data-i18n="completed_goals">已完成的目标</h3>
          <div id="completedGoalsList" class="goals-list">
            <!-- 目标卡片将通过JavaScript动态添加 -->
          </div>
        </div>
      </div>
    </div>

    <!-- 设置页面 -->
    <div id="settingsPage" class="page">
      <div class="settings-container">
        <div class="settings-header">
          <div class="settings-title" data-i18n="settings">设置</div>
        </div>

        <!-- 设置内容 -->
        <div class="settings-section">
          <h3>关于</h3>
          <div class="settings-about">
            <div class="about-logo">
              <div class="logo-icon">钱</div>
              <span class="about-app-name">钱管家</span>
            </div>
            <div class="about-description">
              <p>钱管家是一款简单易用的个人财务管理工具，帮助您追踪收支、管理预算、设定财务目标。</p>
              <p>版本: 1.0.0</p>
            </div>
          </div>
        </div>

        <div class="settings-section">
          <h3>数据管理</h3>
          <div class="settings-actions">
            <button id="exportDataBtn" class="settings-btn">导出数据</button>
            <button id="importDataBtn" class="settings-btn">导入数据</button>
            <button id="clearDataBtn" class="settings-btn danger">清除所有数据</button>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- SQL.js已在头部引入 -->

  <!-- 引入JavaScript模块 -->
  <script type="module" src="assets/js/app.js"></script>
</body>
</html>
