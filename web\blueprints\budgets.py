#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
预算相关API蓝图
"""

from flask import Blueprint, request, jsonify, g, session
import datetime
import uuid
import logging
import sqlite3
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.database_config import MAIN_DB_PATH

# 创建蓝图
bp = Blueprint('budgets', __name__)
logger = logging.getLogger(__name__)

def get_db_connection():
    """获取数据库连接"""
    if 'db' not in g:
        g.db = sqlite3.connect(MAIN_DB_PATH)
        g.db.row_factory = sqlite3.Row
        # 启用外键约束
        g.db.execute('PRAGMA foreign_keys = ON')
        # 优化查询性能
        g.db.execute('PRAGMA journal_mode = WAL')
        g.db.execute('PRAGMA synchronous = NORMAL')
    return g.db

def login_required(f):
    """登录验证装饰器"""
    from functools import wraps

    @wraps(f)
    def decorated(*args, **kwargs):
        if not session.get('logged_in') or not session.get('user_id'):
            logger.warning(f"未登录用户访问API: {request.path}")
            return jsonify({'success': False, 'error': '请先登录'}), 401
        return f(*args, **kwargs)
    return decorated

def get_current_user_id():
    """获取当前登录用户ID"""
    return session.get('user_id')

@bp.route('/', methods=['GET'])
@login_required
def get_budgets():
    """获取所有预算"""
    user_id = get_current_user_id()
    logger.info(f"用户 {user_id} 获取所有预算")
    conn = get_db_connection()
    budgets = conn.execute('SELECT * FROM budgets WHERE user_id = ? ORDER BY category', (user_id,)).fetchall()
    return jsonify([dict(budget) for budget in budgets])

@bp.route('/<budget_id>', methods=['GET'])
@login_required
def get_budget(budget_id):
    """获取单个预算"""
    user_id = get_current_user_id()
    logger.info(f"用户 {user_id} 获取预算: {budget_id}")
    conn = get_db_connection()
    budget = conn.execute('SELECT * FROM budgets WHERE id = ? AND user_id = ?', (budget_id, user_id)).fetchone()

    if budget is None:
        return jsonify({'error': '预算不存在'}), 404

    return jsonify(dict(budget))

@bp.route('/<budget_id>', methods=['PUT'])
@login_required
def update_budget(budget_id):
    """更新预算"""
    user_id = get_current_user_id()
    data = request.json
    logger.info(f"用户 {user_id} 更新预算: {budget_id}")

    # 验证必填字段
    required_fields = ['project_name', 'category', 'amount', 'period', 'start_date', 'end_date', 'currency']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'缺少必填字段: {field}'}), 400

    conn = get_db_connection()

    # 检查预算是否存在且属于当前用户
    budget = conn.execute('SELECT id FROM budgets WHERE id = ? AND user_id = ?', (budget_id, user_id)).fetchone()
    if not budget:
        return jsonify({'error': '预算不存在'}), 404

    # 更新预算
    now = datetime.datetime.now().isoformat()
    conn.execute('''
    UPDATE budgets
    SET project_name = ?, category = ?, amount = ?, period = ?, start_date = ?, end_date = ?, currency = ?, description = ?, updated_at = ?
    WHERE id = ? AND user_id = ?
    ''', (
        data['project_name'], data['category'], data['amount'], data['period'], data['start_date'],
        data['end_date'], data['currency'], data.get('description', ''), now, budget_id, user_id
    ))

    conn.commit()

    return jsonify({'success': True})

@bp.route('/', methods=['POST'])
@login_required
def create_budget():
    """创建预算"""
    try:
        user_id = get_current_user_id()
        data = request.json
        logger.info(f"用户 {user_id} 创建/更新预算: {data.get('id', '新预算')}")

        # 验证必填字段
        required_fields = ['project_name', 'category', 'amount', 'period', 'start_date', 'end_date', 'currency']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必填字段: {field}', 'success': False}), 400

        # 获取描述字段（可选）
        description = data.get('description', '')

        # 生成ID和时间戳
        budget_id = data.get('id') or f"budget_{uuid.uuid4()}"
        now = datetime.datetime.now().isoformat()

        conn = get_db_connection()

        try:
            # 检查是否存在相同ID的预算（只检查当前用户的预算）
            existing = conn.execute('SELECT id FROM budgets WHERE id = ? AND user_id = ?', (budget_id, user_id)).fetchone()

            if existing:
                # 更新现有预算
                conn.execute('''
                UPDATE budgets
                SET project_name = ?, category = ?, amount = ?, period = ?, start_date = ?, end_date = ?, currency = ?, description = ?, updated_at = ?
                WHERE id = ? AND user_id = ?
                ''', (
                    data['project_name'], data['category'], data['amount'], data['period'], data['start_date'],
                    data['end_date'], data['currency'], description, now, budget_id, user_id
                ))
                logger.info(f"用户 {user_id} 更新预算: {budget_id}")
            else:
                # 创建新预算
                conn.execute('''
                INSERT INTO budgets (id, user_id, project_name, category, amount, period, start_date, end_date, currency, description, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    budget_id, user_id, data['project_name'], data['category'], data['amount'], data['period'], data['start_date'],
                    data['end_date'], data['currency'], description, now, now
                ))
                logger.info(f"用户 {user_id} 创建新预算: {budget_id}")

            # 确保提交事务
            conn.commit()

            # 返回成功响应
            return jsonify({'id': budget_id, 'success': True})

        except sqlite3.Error as e:
            # 回滚事务
            conn.rollback()
            logger.error(f"数据库错误: {str(e)}")
            return jsonify({'error': f'数据库错误: {str(e)}', 'success': False}), 500

    except Exception as e:
        logger.error(f"处理预算请求时出错: {str(e)}")
        return jsonify({'error': f'服务器错误: {str(e)}', 'success': False}), 500

@bp.route('/<budget_id>', methods=['DELETE'])
@login_required
def delete_budget(budget_id):
    """删除预算"""
    user_id = get_current_user_id()
    logger.info(f"用户 {user_id} 删除预算: {budget_id}")
    conn = get_db_connection()

    # 检查预算是否存在且属于当前用户
    budget = conn.execute('SELECT id FROM budgets WHERE id = ? AND user_id = ?', (budget_id, user_id)).fetchone()
    if not budget:
        return jsonify({'error': '预算不存在'}), 404

    conn.execute('DELETE FROM budgets WHERE id = ? AND user_id = ?', (budget_id, user_id))
    conn.commit()

    return jsonify({'success': True})

@bp.route('/stats', methods=['GET'])
@login_required
def get_budget_stats():
    """获取预算统计信息"""
    user_id = get_current_user_id()
    logger.info(f"用户 {user_id} 获取预算统计")

    conn = get_db_connection()

    # 获取用户所有预算
    budgets = conn.execute('SELECT * FROM budgets WHERE user_id = ?', (user_id,)).fetchall()

    if not budgets:
        return jsonify({
            'total_budgets': 0,
            'total_amount': 0,
            'total_used': 0,
            'usage_percentage': 0,
            'categories': []
        })

    total_amount = 0
    total_used = 0
    categories = []

    for budget in budgets:
        budget_dict = dict(budget)

        # 计算该预算的使用情况 - 精确匹配交易描述和预算项目名称
        used_amount = conn.execute('''
            SELECT COALESCE(SUM(amount), 0) FROM transactions
            WHERE user_id = ? AND type = 'expense'
            AND description = ?
            AND date >= ? AND date <= ?
        ''', (user_id, budget_dict['project_name'], budget_dict['start_date'], budget_dict['end_date'])).fetchone()[0]

        total_amount += budget_dict['amount']
        total_used += used_amount

        usage_percentage = (used_amount / budget_dict['amount']) * 100 if budget_dict['amount'] > 0 else 0

        # 确定状态
        status = 'normal'
        if usage_percentage >= 100:
            status = 'exceeded'
        elif usage_percentage >= 90:
            status = 'danger'
        elif usage_percentage >= 80:
            status = 'warning'

        categories.append({
            'id': budget_dict['id'],
            'project_name': budget_dict['project_name'],
            'category': budget_dict['category'],
            'amount': budget_dict['amount'],
            'used_amount': used_amount,
            'remaining_amount': budget_dict['amount'] - used_amount,
            'usage_percentage': usage_percentage,
            'status': status,
            'period': budget_dict['period'],
            'start_date': budget_dict['start_date'],
            'end_date': budget_dict['end_date'],
            'currency': budget_dict['currency'],
            'description': budget_dict.get('description', '')
        })

    overall_usage = (total_used / total_amount) * 100 if total_amount > 0 else 0

    return jsonify({
        'total_budgets': len(budgets),
        'total_amount': total_amount,
        'total_used': total_used,
        'remaining_amount': total_amount - total_used,
        'usage_percentage': overall_usage,
        'categories': categories
    })

@bp.route('/check/<project_name>', methods=['GET'])
@login_required
def check_budget_status(project_name):
    """检查特定项目的预算状态"""
    user_id = get_current_user_id()
    logger.info(f"用户 {user_id} 检查预算状态: {project_name}")

    conn = get_db_connection()

    # 获取该项目的预算
    budget = conn.execute('''
        SELECT * FROM budgets WHERE user_id = ? AND project_name = ?
        ORDER BY created_at DESC LIMIT 1
    ''', (user_id, project_name)).fetchone()

    if not budget:
        return jsonify({'error': '该项目没有设置预算'}), 404

    budget_dict = dict(budget)

    # 计算使用情况 - 精确匹配交易描述和预算项目名称
    used_amount = conn.execute('''
        SELECT COALESCE(SUM(amount), 0) FROM transactions
        WHERE user_id = ? AND type = 'expense'
        AND description = ?
        AND date >= ? AND date <= ?
    ''', (user_id, budget_dict['project_name'], budget_dict['start_date'], budget_dict['end_date'])).fetchone()[0]

    usage_percentage = (used_amount / budget_dict['amount']) * 100 if budget_dict['amount'] > 0 else 0

    # 确定状态
    status = 'normal'
    message = ''

    if usage_percentage >= 100:
        status = 'exceeded'
        message = f'预算已超支 {usage_percentage:.1f}%'
    elif usage_percentage >= 90:
        status = 'danger'
        message = f'预算即将用完，已使用 {usage_percentage:.1f}%'
    elif usage_percentage >= 80:
        status = 'warning'
        message = f'预算使用较多，已使用 {usage_percentage:.1f}%'
    else:
        message = f'预算使用正常，已使用 {usage_percentage:.1f}%'

    return jsonify({
        'budget': budget_dict,
        'used_amount': used_amount,
        'remaining_amount': budget_dict['amount'] - used_amount,
        'usage_percentage': usage_percentage,
        'status': status,
        'message': message
    })
