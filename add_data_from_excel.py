#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
向现有数据库中增量导入财务数据
支持账户、交易记录和预算数据的增量导入，不删除现有数据
"""

import csv
import sqlite3
import uuid
import datetime
import os
import logging
from pathlib import Path

# 尝试导入pandas，如果没有则使用CSV
try:
    import pandas as pd
    HAS_PANDAS = True
except ImportError:
    HAS_PANDAS = False

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库路径
DB_PATH = './data/finance.db'

def get_admin_user_id():
    """获取admin用户ID"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    user = cursor.execute('SELECT id FROM users WHERE username = ?', ('admin',)).fetchone()
    conn.close()
    
    if not user:
        raise Exception("未找到admin用户，请先注册admin账户")
    
    return user[0]

def validate_excel_file(file_path):
    """验证Excel文件格式"""
    if not os.path.exists(file_path):
        raise Exception(f"文件不存在: {file_path}")
    
    if not file_path.endswith(('.xlsx', '.xls')):
        raise Exception("文件格式错误，请使用Excel文件(.xlsx或.xls)")
    
    try:
        # 尝试读取文件
        xl_file = pd.ExcelFile(file_path)
        return xl_file.sheet_names
    except Exception as e:
        raise Exception(f"无法读取Excel文件: {e}")

def clean_dataframe(df):
    """清理DataFrame，移除空行和说明行"""
    if df.empty:
        return df
    
    # 移除完全空白的行
    df = df.dropna(how='all')
    
    # 移除说明行（包含"==="的行）
    if not df.empty and len(df.columns) > 0:
        first_col = df.columns[0]
        df = df[~df[first_col].astype(str).str.contains('===', na=False)]
    
    # 重置索引
    df = df.reset_index(drop=True)
    
    return df

def get_existing_accounts(user_id):
    """获取现有账户名称集合"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    accounts = cursor.execute('SELECT name FROM accounts WHERE user_id = ?', (user_id,)).fetchall()
    conn.close()
    
    return {account[0] for account in accounts}

def get_existing_budgets(user_id):
    """获取现有预算的项目名称-期间-类别组合集合"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    budgets = cursor.execute('SELECT project_name, period, category FROM budgets WHERE user_id = ?', (user_id,)).fetchall()
    conn.close()

    return {(budget[0], budget[1], budget[2]) for budget in budgets}

def add_accounts(file_path, user_id):
    """增量导入账户数据"""
    try:
        df = pd.read_excel(file_path, sheet_name='账户')
        df = clean_dataframe(df)
        
        if df.empty:
            logger.info("账户工作表为空，跳过导入")
            return {'added': 0, 'skipped': 0, 'failed': 0}
        
        # 获取现有账户名称
        existing_accounts = get_existing_accounts(user_id)
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        added_count = 0
        skipped_count = 0
        failed_count = 0
        now = datetime.datetime.now().isoformat()
        
        for _, row in df.iterrows():
            try:
                account_name = str(row['账户名称'])
                
                # 检查账户是否已存在
                if account_name in existing_accounts:
                    logger.warning(f"账户已存在，跳过: {account_name}")
                    skipped_count += 1
                    continue
                
                account_id = str(uuid.uuid4())
                
                cursor.execute('''
                    INSERT INTO accounts (id, user_id, name, type, currency, initial_balance, description, icon, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    account_id, user_id, account_name, str(row['账户类型']), 
                    str(row['货币']), float(row['初始余额']), 
                    str(row.get('描述', '')), str(row.get('图标', '')), now, now
                ))
                
                added_count += 1
                existing_accounts.add(account_name)  # 更新本地缓存
                logger.info(f"新增账户: {account_name}")
                
            except Exception as e:
                logger.error(f"导入账户失败 {row.get('账户名称', 'Unknown')}: {e}")
                failed_count += 1
        
        conn.commit()
        conn.close()
        
        result = {'added': added_count, 'skipped': skipped_count, 'failed': failed_count}
        logger.info(f"账户导入完成 - 新增: {added_count}, 跳过: {skipped_count}, 失败: {failed_count}")
        return result
        
    except Exception as e:
        logger.error(f"导入账户数据失败: {e}")
        return {'added': 0, 'skipped': 0, 'failed': 0}

def add_transactions(file_path, user_id):
    """增量导入交易记录（允许重复）"""
    try:
        df = pd.read_excel(file_path, sheet_name='交易记录')
        df = clean_dataframe(df)
        
        if df.empty:
            logger.info("交易记录工作表为空，跳过导入")
            return {'added': 0, 'skipped': 0, 'failed': 0}
        
        # 获取账户名称到ID的映射
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        accounts = cursor.execute('SELECT id, name FROM accounts WHERE user_id = ?', (user_id,)).fetchall()
        account_name_to_id = {account[1]: account[0] for account in accounts}
        
        added_count = 0
        skipped_count = 0
        failed_count = 0
        now = datetime.datetime.now().isoformat()
        
        for _, row in df.iterrows():
            try:
                # 查找账户ID
                account_name = str(row['账户'])
                if account_name not in account_name_to_id:
                    logger.warning(f"未找到账户: {account_name}，跳过交易记录")
                    skipped_count += 1
                    continue
                
                account_id = account_name_to_id[account_name]
                transaction_id = str(uuid.uuid4())
                
                # 处理日期
                date_str = str(row['日期'])
                if 'T' in date_str:  # 处理datetime格式
                    date_str = date_str.split('T')[0]
                
                cursor.execute('''
                    INSERT INTO transactions (id, user_id, date, type, category, description, account, amount, currency, attachment, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    transaction_id, user_id, date_str, str(row['类型']), str(row['分类']),
                    str(row['描述']), account_id, float(row['金额']), str(row['货币']),
                    str(row.get('附件', '')), now, now
                ))
                
                added_count += 1
                logger.info(f"新增交易: {row['日期']} - {row['描述']} - {row['金额']}")
                
            except Exception as e:
                logger.error(f"导入交易记录失败 {row.get('描述', 'Unknown')}: {e}")
                failed_count += 1
        
        conn.commit()
        conn.close()
        
        result = {'added': added_count, 'skipped': skipped_count, 'failed': failed_count}
        logger.info(f"交易记录导入完成 - 新增: {added_count}, 跳过: {skipped_count}, 失败: {failed_count}")
        return result
        
    except Exception as e:
        logger.error(f"导入交易记录失败: {e}")
        return {'added': 0, 'skipped': 0, 'failed': 0}

def add_budgets(file_path, user_id):
    """增量导入预算数据"""
    try:
        df = pd.read_excel(file_path, sheet_name='预算')
        df = clean_dataframe(df)

        if df.empty:
            logger.info("预算工作表为空，跳过导入")
            return {'added': 0, 'skipped': 0, 'failed': 0}

        # 获取现有预算的项目名称-期间-类别组合
        existing_budgets = get_existing_budgets(user_id)

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        added_count = 0
        skipped_count = 0
        failed_count = 0
        now = datetime.datetime.now().isoformat()

        for _, row in df.iterrows():
            try:
                # 计算开始和结束日期
                # 支持三种格式：分离年月列、YYYY-MM 和 YYYY年MM月
                if '年' in row.keys() and '月' in row.keys():
                    # 新格式：分离的年月列
                    year = str(int(row['年']))  # 确保是字符串
                    month = str(int(row['月']))  # 确保是字符串，去掉小数点
                    period = f"{year}-{month.zfill(2)}"
                elif '期间' in row.keys():
                    period_str = str(row['期间'])
                    if '年' in period_str and '月' in period_str:
                        # 格式：2025年01月
                        period_str = period_str.replace('年', '-').replace('月', '')
                        year, month = period_str.split('-')
                        period = f"{year}-{month.zfill(2)}"
                    else:
                        # 格式：YYYY-MM
                        year, month = period_str.split('-')
                        period = f"{year}-{month.zfill(2)}"
                else:
                    raise Exception("未找到期间信息，请检查列名")

                category = str(row['类别'])
                project_name = str(row['项目名称'])

                # 检查预算是否已存在（同一项目名称、期间和类别）
                if (project_name, period, category) in existing_budgets:
                    logger.warning(f"预算已存在，跳过: {project_name} - {period} - {category}")
                    skipped_count += 1
                    continue

                budget_id = str(uuid.uuid4())
                start_date = f"{year}-{month.zfill(2)}-01"

                # 计算月末日期
                month_int = int(month)
                if month_int == 12:
                    end_date = f"{int(year)+1}-01-01"
                else:
                    end_date = f"{year}-{month_int+1:02d}-01"

                # 转换为日期对象然后减一天得到月末
                from datetime import timedelta
                end_date_obj = datetime.datetime.strptime(end_date, '%Y-%m-%d') - timedelta(days=1)
                end_date = end_date_obj.strftime('%Y-%m-%d')

                cursor.execute('''
                    INSERT INTO budgets (id, user_id, project_name, category, amount, period, start_date, end_date, currency, description, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    budget_id, user_id, project_name, category,
                    float(row['预算金额']), period, start_date, end_date,
                    str(row['货币']), str(row.get('描述', '')), now, now
                ))

                added_count += 1
                existing_budgets.add((project_name, period, category))  # 更新本地缓存
                logger.info(f"新增预算: {project_name} - {period} - {category} - {row['预算金额']}")

            except Exception as e:
                logger.error(f"导入预算失败 {row.get('项目名称', 'Unknown')}: {e}")
                failed_count += 1

        conn.commit()
        conn.close()

        result = {'added': added_count, 'skipped': skipped_count, 'failed': failed_count}
        logger.info(f"预算导入完成 - 新增: {added_count}, 跳过: {skipped_count}, 失败: {failed_count}")
        return result

    except Exception as e:
        logger.error(f"导入预算数据失败: {e}")
        return {'added': 0, 'skipped': 0, 'failed': 0}

def main():
    """主函数"""
    try:
        # 检查数据库文件是否存在
        if not os.path.exists(DB_PATH):
            logger.error(f"数据库文件不存在: {DB_PATH}")
            return

        # 获取admin用户ID
        user_id = get_admin_user_id()
        logger.info(f"找到admin用户ID: {user_id}")

        # 获取Excel文件路径
        print("请输入Excel文件路径（或拖拽文件到此处）:")
        file_path = input().strip().strip('"\'')

        if not file_path:
            logger.error("未提供文件路径")
            return

        # 验证文件
        sheet_names = validate_excel_file(file_path)
        logger.info(f"找到工作表: {sheet_names}")

        # 显示现有数据统计
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        account_count = cursor.execute('SELECT COUNT(*) FROM accounts WHERE user_id = ?', (user_id,)).fetchone()[0]
        transaction_count = cursor.execute('SELECT COUNT(*) FROM transactions WHERE user_id = ?', (user_id,)).fetchone()[0]
        budget_count = cursor.execute('SELECT COUNT(*) FROM budgets WHERE user_id = ?', (user_id,)).fetchone()[0]

        conn.close()

        logger.info(f"当前数据统计 - 账户: {account_count}, 交易: {transaction_count}, 预算: {budget_count}")

        # 确认是否继续增量导入
        response = input("确认要进行增量导入（不会删除现有数据）？(Y/n): ")
        if response.lower() == 'n':
            logger.info("取消导入")
            return

        # 开始增量导入
        logger.info("开始增量导入数据...")

        # 初始化统计结果
        total_results = {
            'accounts': {'added': 0, 'skipped': 0, 'failed': 0},
            'transactions': {'added': 0, 'skipped': 0, 'failed': 0},
            'budgets': {'added': 0, 'skipped': 0, 'failed': 0}
        }

        # 导入账户（必须先导入，因为交易记录依赖账户）
        if '账户' in sheet_names:
            total_results['accounts'] = add_accounts(file_path, user_id)

        # 导入交易记录
        if '交易记录' in sheet_names:
            total_results['transactions'] = add_transactions(file_path, user_id)

        # 导入预算
        if '预算' in sheet_names:
            total_results['budgets'] = add_budgets(file_path, user_id)

        # 显示导入结果汇总
        logger.info("=" * 60)
        logger.info("增量导入完成！")
        logger.info("=" * 60)

        # 账户结果
        acc_res = total_results['accounts']
        logger.info(f"账户导入结果:")
        logger.info(f"  - 新增: {acc_res['added']} 个")
        logger.info(f"  - 跳过: {acc_res['skipped']} 个（已存在）")
        logger.info(f"  - 失败: {acc_res['failed']} 个")

        # 交易记录结果
        trans_res = total_results['transactions']
        logger.info(f"交易记录导入结果:")
        logger.info(f"  - 新增: {trans_res['added']} 条")
        logger.info(f"  - 跳过: {trans_res['skipped']} 条（账户不存在）")
        logger.info(f"  - 失败: {trans_res['failed']} 条")

        # 预算结果
        budget_res = total_results['budgets']
        logger.info(f"预算导入结果:")
        logger.info(f"  - 新增: {budget_res['added']} 个")
        logger.info(f"  - 跳过: {budget_res['skipped']} 个（项目-期间-类别已存在）")
        logger.info(f"  - 失败: {budget_res['failed']} 个")

        # 总计
        total_added = acc_res['added'] + trans_res['added'] + budget_res['added']
        total_skipped = acc_res['skipped'] + trans_res['skipped'] + budget_res['skipped']
        total_failed = acc_res['failed'] + trans_res['failed'] + budget_res['failed']

        logger.info("=" * 60)
        logger.info(f"总计 - 新增: {total_added}, 跳过: {total_skipped}, 失败: {total_failed}")
        logger.info("=" * 60)

    except Exception as e:
        logger.error(f"导入失败: {e}")

if __name__ == '__main__':
    main()
