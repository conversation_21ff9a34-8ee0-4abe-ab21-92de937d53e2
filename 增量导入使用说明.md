# 增量数据导入脚本使用说明

## 概述

`add_data_from_excel.py` 是一个专门用于向现有数据库中增量导入财务数据的脚本，与原有的 `import_from_excel.py` 不同，它不会删除任何现有数据，而是在现有数据基础上追加新记录。

## 主要特性

### 1. 增量导入模式
- **不删除现有数据**：保留数据库中的所有现有记录
- **智能去重**：根据不同数据类型采用不同的重复检测策略
- **安全操作**：导入前会显示当前数据统计，用户确认后才执行

### 2. 重复数据处理策略

#### 账户数据
- **检测规则**：按账户名称检测重复
- **处理方式**：如果账户名称已存在，跳过该记录并记录警告
- **原因**：避免创建重复的账户，保持账户唯一性

#### 交易记录
- **检测规则**：不检测重复
- **处理方式**：允许所有交易记录导入（只要关联的账户存在）
- **原因**：实际业务中可能存在相同金额、相同日期的多笔交易

#### 预算数据
- **检测规则**：按项目名称+期间+类别组合检测重复
- **处理方式**：如果同一项目、同一期间和同一类别的预算已存在，跳过该记录
- **原因**：同一项目在同一期间的同一类别只应有一个预算，但不同项目可以有相同类别的预算

### 3. 数据完整性保障
- **依赖关系检查**：交易记录导入时会验证关联账户是否存在
- **错误隔离**：单条记录失败不影响其他记录的导入
- **详细日志**：提供完整的导入过程日志和结果统计

## 使用方法

### 1. 运行脚本
```bash
python add_data_from_excel.py
```

### 2. 输入Excel文件路径
脚本会提示输入Excel文件路径，支持：
- 直接输入文件路径
- 拖拽文件到命令行窗口

### 3. 确认导入
脚本会显示当前数据统计，确认后开始增量导入

## Excel文件格式要求

### 工作表结构
Excel文件应包含以下工作表（可选）：
- **账户**：账户信息
- **交易记录**：交易数据
- **预算**：预算数据

### 列名要求

#### 账户工作表
| 列名 | 必需 | 说明 |
|------|------|------|
| 账户名称 | 是 | 账户的唯一标识 |
| 账户类型 | 是 | 如：储蓄账户、信用卡等 |
| 货币 | 是 | 如：CNY、USD等 |
| 初始余额 | 是 | 数值类型 |
| 描述 | 否 | 账户描述 |
| 图标 | 否 | 图标标识 |

#### 交易记录工作表
| 列名 | 必需 | 说明 |
|------|------|------|
| 日期 | 是 | YYYY-MM-DD格式 |
| 账户 | 是 | 必须是已存在的账户名称 |
| 类型 | 是 | 如：收入、支出等 |
| 分类 | 是 | 交易分类 |
| 描述 | 是 | 交易描述 |
| 金额 | 是 | 数值类型 |
| 货币 | 是 | 货币代码 |
| 附件 | 否 | 附件信息 |

#### 预算工作表
支持两种期间格式：

**格式1：分离年月列**
| 列名 | 必需 | 说明 |
|------|------|------|
| 年 | 是 | 如：2025 |
| 月 | 是 | 如：1 |
| 项目名称 | 是 | 预算项目名称 |
| 类别 | 是 | 预算类别 |
| 预算金额 | 是 | 数值类型 |
| 货币 | 是 | 货币代码 |
| 描述 | 否 | 预算描述 |

**格式2：期间列**
| 列名 | 必需 | 说明 |
|------|------|------|
| 期间 | 是 | 如：2025-01 或 2025年01月 |
| 项目名称 | 是 | 预算项目名称 |
| 类别 | 是 | 预算类别 |
| 预算金额 | 是 | 数值类型 |
| 货币 | 是 | 货币代码 |
| 描述 | 否 | 预算描述 |

## 导入结果说明

脚本执行完成后会显示详细的导入结果：

```
增量导入完成！
============================================================
账户导入结果:
  - 新增: 5 个
  - 跳过: 2 个（已存在）
  - 失败: 0 个
交易记录导入结果:
  - 新增: 150 条
  - 跳过: 3 条（账户不存在）
  - 失败: 1 条
预算导入结果:
  - 新增: 12 个
  - 跳过: 1 个（项目-期间-类别已存在）
  - 失败: 0 个
============================================================
总计 - 新增: 167, 跳过: 6, 失败: 1
============================================================
```

## 注意事项

1. **账户依赖**：交易记录必须关联到已存在的账户，建议先导入账户数据
2. **数据备份**：虽然脚本不会删除数据，但建议在导入前备份数据库
3. **Excel格式**：确保Excel文件格式正确，列名与要求一致
4. **错误处理**：查看日志了解跳过或失败记录的具体原因
5. **重复运行**：可以多次运行脚本，已存在的数据会被自动跳过

## 与原脚本的区别

| 特性 | import_from_excel.py | add_data_from_excel.py |
|------|---------------------|------------------------|
| 数据处理方式 | 完全替换 | 增量追加 |
| 现有数据 | 删除后重新导入 | 保留并追加新数据 |
| 重复检测 | 无（因为先清空） | 智能去重 |
| 使用场景 | 初始化或完整替换 | 日常数据追加 |
| 安全性 | 需要用户确认删除 | 无删除操作，更安全 |
