import{l as T,c as u,d as e,F as x,k as w,t as l,q as D,e as m,o as r,a as L,m as Q,s as O,w as p,f as k,p as V,v as X,g as Y,i as I,L as Z}from"./index--MNqwREY.js";import{u as ee}from"./subscriptions-4RI1wB9c.js";import{S as q}from"./ServiceLogo-DKV-znac.js";import{_ as z}from"./_plugin-vue_export-helper-DlAUqK2U.js";const te={class:"timeline-template"},se={class:"timeline-container"},ne={class:"timeline-date"},oe={class:"date-indicator"},ie={class:"date-text"},ae={class:"date-count"},le={class:"timeline-items"},ce=["onClick"],de={class:"timeline-content"},re={class:"item-header"},ue={class:"service-info"},_e={class:"service-details"},ve={class:"service-name"},me={class:"service-type"},pe={class:"price-info"},he={class:"amount"},fe={class:"cycle"},ye={class:"item-footer"},ge={class:"billing-info"},$e={__name:"TimelineTemplate",props:{subscriptions:{type:Array,required:!0}},emits:["click-subscription"],setup(M){const C=M,_=T(()=>{const s={};C.subscriptions.forEach(o=>{let i,t;if(o.is_due_soon){const c=o.days_until_billing;c<=3?(i="urgent",t="紧急到期"):c<=7?(i="soon",t="即将到期"):(i="upcoming",t="近期到期")}else{const $=new Date(o.next_billing_date).getMonth()+1;i=`month-${$}`,t=`${$}月到期`}s[i]||(s[i]={date:i,dateLabel:t,subscriptions:[]}),s[i].subscriptions.push(o)});const a=["urgent","soon","upcoming"];return Object.values(s).sort((o,i)=>{const t=a.indexOf(o.date),c=a.indexOf(i.date);return t!==-1&&c!==-1?t-c:t!==-1?-1:c!==-1?1:o.date.localeCompare(i.date)})}),h=s=>{const a={streaming:"流媒体",music:"音乐",cloud:"云服务",productivity:"生产力",development:"开发工具",design:"设计工具",fitness:"健身",education:"教育",news:"新闻",gaming:"游戏",ai:"AI工具",other:"其他"};return a[s]||a.other},f=s=>s.is_due_soon?"status-warning":s.is_active?"status-active":"status-inactive",b=s=>s.is_due_soon?"即将到期":s.is_active?"正常":"已暂停",g=s=>({weekly:"周",monthly:"月",quarterly:"季",yearly:"年"})[s]||"月",y=s=>{try{const a=new Date(s);return`${a.getMonth()+1}月${a.getDate()}日`}catch{return"未知"}};return(s,a)=>(r(),u("div",te,[e("div",se,[(r(!0),u(x,null,w(_.value,(o,i)=>(r(),u("div",{key:o.date,class:"timeline-group"},[e("div",ne,[e("div",oe,[e("span",ie,l(o.dateLabel),1),e("span",ae,l(o.subscriptions.length)+"个",1)])]),e("div",le,[(r(!0),u(x,null,w(o.subscriptions,t=>(r(),u("div",{key:t.id,class:"timeline-item",onClick:c=>s.$emit("click-subscription",t.id)},[e("div",{class:D(["timeline-dot",f(t)])},null,2),e("div",de,[e("div",re,[e("div",ue,[m(q,{"service-name":t.name,size:"small"},null,8,["service-name"]),e("div",_e,[e("h4",ve,l(t.name),1),e("p",me,l(h(t.service_type)),1)])]),e("div",pe,[e("span",he,"$"+l(t.amount),1),e("span",fe,l(g(t.billing_cycle)),1)])]),e("div",ye,[e("span",ge,l(t.is_due_soon?`还有${t.days_until_billing}天到期`:`下次扣费: ${y(t.next_billing_date)}`),1),e("span",{class:D(["status-text",f(t)])},l(b(t)),3)])])],8,ce))),128))])]))),128))])]))}},E=z($e,[["__scopeId","data-v-367f9ece"]]),be={class:"timeline-template modern-theme"},ke={class:"timeline-container"},xe={class:"timeline-date"},we={class:"date-indicator"},Ce={class:"date-text"},Se={class:"date-count"},Le={class:"timeline-items"},Te=["onClick"],De={class:"timeline-content"},Me={class:"item-header"},Ie={class:"service-info"},Oe={class:"service-details"},ze={class:"service-name"},Ve={class:"service-type"},qe={class:"price-info"},Ae={class:"amount"},Be={class:"cycle"},Ee={class:"item-footer"},Ne={class:"billing-info"},Ue={__name:"TimelineModernTemplate",props:{subscriptions:{type:Array,required:!0}},emits:["click-subscription"],setup(M){const C=M,_=T(()=>{const s={};C.subscriptions.forEach(o=>{let i,t;if(o.is_due_soon){const c=o.days_until_billing;c<=3?(i="urgent",t="紧急到期"):c<=7?(i="soon",t="即将到期"):(i="upcoming",t="近期到期")}else{const $=new Date(o.next_billing_date).getMonth()+1;i=`month-${$}`,t=`${$}月到期`}s[i]||(s[i]={date:i,dateLabel:t,subscriptions:[]}),s[i].subscriptions.push(o)});const a=["urgent","soon","upcoming"];return Object.values(s).sort((o,i)=>{const t=a.indexOf(o.date),c=a.indexOf(i.date);return t!==-1&&c!==-1?t-c:t!==-1?-1:c!==-1?1:o.date.localeCompare(i.date)})}),h=s=>{const a={streaming:"流媒体",music:"音乐",cloud:"云服务",productivity:"生产力",development:"开发工具",design:"设计工具",fitness:"健身",education:"教育",news:"新闻",gaming:"游戏",ai:"AI工具",other:"其他"};return a[s]||a.other},f=s=>s.is_due_soon?"status-warning":s.is_active?"status-active":"status-inactive",b=s=>s.is_due_soon?"即将到期":s.is_active?"正常":"已暂停",g=s=>({weekly:"周",monthly:"月",quarterly:"季",yearly:"年"})[s]||"月",y=s=>{try{const a=new Date(s);return`${a.getMonth()+1}月${a.getDate()}日`}catch{return"未知"}};return(s,a)=>(r(),u("div",be,[e("div",ke,[(r(!0),u(x,null,w(_.value,(o,i)=>(r(),u("div",{key:o.date,class:"timeline-group"},[e("div",xe,[e("div",we,[e("span",Ce,l(o.dateLabel),1),e("span",Se,l(o.subscriptions.length)+"个",1)])]),e("div",Le,[(r(!0),u(x,null,w(o.subscriptions,t=>(r(),u("div",{key:t.id,class:"timeline-item",onClick:c=>s.$emit("click-subscription",t.id)},[e("div",{class:D(["timeline-dot",f(t)])},null,2),e("div",De,[e("div",Me,[e("div",Ie,[m(q,{"service-name":t.name,size:"small"},null,8,["service-name"]),e("div",Oe,[e("h4",ze,l(t.name),1),e("p",Ve,l(h(t.service_type)),1)])]),e("div",qe,[e("span",Ae,"$"+l(t.amount),1),e("span",Be,l(g(t.billing_cycle)),1)])]),e("div",Ee,[e("span",Ne,l(t.is_due_soon?`还有${t.days_until_billing}天到期`:`下次扣费: ${y(t.next_billing_date)}`),1),e("span",{class:D(["status-text",f(t)])},l(b(t)),3)])])],8,Te))),128))])]))),128))])]))}},je=z(Ue,[["__scopeId","data-v-c2094303"]]),Fe={class:"timeline-template warm-theme"},Ke={class:"timeline-container"},Re={class:"timeline-date"},We={class:"date-indicator"},Pe={class:"date-text"},Ge={class:"date-count"},He={class:"timeline-items"},Je=["onClick"],Qe={class:"timeline-content"},Xe={class:"item-header"},Ye={class:"service-info"},Ze={class:"service-details"},et={class:"service-name"},tt={class:"service-type"},st={class:"price-info"},nt={class:"amount"},ot={class:"cycle"},it={class:"item-footer"},at={class:"billing-info"},lt={__name:"TimelineWarmTemplate",props:{subscriptions:{type:Array,required:!0}},emits:["click-subscription"],setup(M){const C=M,_=T(()=>{const s={};C.subscriptions.forEach(o=>{let i,t;if(o.is_due_soon){const c=o.days_until_billing;c<=3?(i="urgent",t="紧急到期"):c<=7?(i="soon",t="即将到期"):(i="upcoming",t="近期到期")}else{const $=new Date(o.next_billing_date).getMonth()+1;i=`month-${$}`,t=`${$}月到期`}s[i]||(s[i]={date:i,dateLabel:t,subscriptions:[]}),s[i].subscriptions.push(o)});const a=["urgent","soon","upcoming"];return Object.values(s).sort((o,i)=>{const t=a.indexOf(o.date),c=a.indexOf(i.date);return t!==-1&&c!==-1?t-c:t!==-1?-1:c!==-1?1:o.date.localeCompare(i.date)})}),h=s=>{const a={streaming:"流媒体",music:"音乐",cloud:"云服务",productivity:"生产力",development:"开发工具",design:"设计工具",fitness:"健身",education:"教育",news:"新闻",gaming:"游戏",ai:"AI工具",other:"其他"};return a[s]||a.other},f=s=>s.is_due_soon?"status-warning":s.is_active?"status-active":"status-inactive",b=s=>s.is_due_soon?"即将到期":s.is_active?"正常":"已暂停",g=s=>({weekly:"周",monthly:"月",quarterly:"季",yearly:"年"})[s]||"月",y=s=>{try{const a=new Date(s);return`${a.getMonth()+1}月${a.getDate()}日`}catch{return"未知"}};return(s,a)=>(r(),u("div",Fe,[e("div",Ke,[(r(!0),u(x,null,w(_.value,(o,i)=>(r(),u("div",{key:o.date,class:"timeline-group"},[e("div",Re,[e("div",We,[e("span",Pe,l(o.dateLabel),1),e("span",Ge,l(o.subscriptions.length)+"个",1)])]),e("div",He,[(r(!0),u(x,null,w(o.subscriptions,t=>(r(),u("div",{key:t.id,class:"timeline-item",onClick:c=>s.$emit("click-subscription",t.id)},[e("div",{class:D(["timeline-dot",f(t)])},null,2),e("div",Qe,[e("div",Xe,[e("div",Ye,[m(q,{"service-name":t.name,size:"small"},null,8,["service-name"]),e("div",Ze,[e("h4",et,l(t.name),1),e("p",tt,l(h(t.service_type)),1)])]),e("div",st,[e("span",nt,"$"+l(t.amount),1),e("span",ot,l(g(t.billing_cycle)),1)])]),e("div",it,[e("span",at,l(t.is_due_soon?`还有${t.days_until_billing}天到期`:`下次扣费: ${y(t.next_billing_date)}`),1),e("span",{class:D(["status-text",f(t)])},l(b(t)),3)])])],8,Je))),128))])]))),128))])]))}},ct=z(lt,[["__scopeId","data-v-b873de5e"]]),dt={class:"subscriptions-page"},rt={class:"nav-actions"},ut={class:"page-container"},_t={class:"stats-card"},vt={class:"stats-grid"},mt={class:"stat-item"},pt={class:"stat-info"},ht={class:"stat-value"},ft={class:"stat-item"},yt={class:"stat-info"},gt={class:"stat-value"},$t={class:"stat-item"},bt={class:"stat-info"},kt={class:"stat-value"},xt={class:"subscriptions-list"},wt={key:0,class:"empty-state"},Ct={class:"due-soon-popup"},St={class:"popup-header"},Lt={class:"subscription-icon"},Tt={class:"template-selector"},Dt={class:"popup-header"},Mt={class:"template-grid"},It=["onClick"],Ot={class:"template-preview"},zt={class:"preview-icon"},Vt={class:"template-info"},qt={__name:"index",setup(M){const C=Y(),_=ee(),h=L(!1),f=L(!1),b=L(!1),g=L(!1),y=L(!1),s=L("modern"),a=T(()=>_.subscriptions||[]),o=T(()=>_.stats||{total_subscriptions:0,monthly_cost:0,yearly_cost:0,due_soon_count:0,service_types:{}}),i=T(()=>_.dueSoonSubscriptions||[]),t=L([{name:"timeline",title:"原版时间轴",description:"黑金VIP风格时间轴",icon:"⏰",component:E},{name:"modern",title:"现代简约",description:"浅色主题，简洁现代",icon:"🤍",component:je},{name:"warm",title:"暖色温馨",description:"米色主题，温馨舒适",icon:"�",component:ct}]),c=T(()=>{const v=t.value.find(n=>n.name===s.value);return v?v.component:E}),$=v=>{var n;s.value=v,y.value=!1,O(`已切换到${(n=t.value.find(S=>S.name===v))==null?void 0:n.title}`)},N=v=>({chatgpt_pro:"🤖",chatgpt_plus:"🤖",chatgpt_team:"🤖",icloud_plus:"☁️",youtube_premium:"📺",youtube_music:"🎵"})[v]||"📱",A=v=>{C.push(`/subscription/detail/${v}`)},U=async()=>{try{await _.fetchSubscriptions(),await _.fetchStats(),await _.fetchDueSoon(),O("刷新成功")}catch{O("刷新失败")}finally{b.value=!1}},j=()=>{f.value=!0};return Q(async()=>{h.value=!0;try{await _.fetchSubscriptions(),await _.fetchStats(),await _.fetchDueSoon()}catch{O("获取数据失败")}finally{h.value=!1}}),(v,n)=>{const S=k("van-button"),F=k("van-nav-bar"),K=k("van-notice-bar"),R=k("van-empty"),W=k("van-list"),P=k("van-pull-refresh"),G=k("van-cell"),H=k("van-cell-group"),B=k("van-popup");return r(),u("div",dt,[m(F,{title:"我的订阅","left-arrow":"",fixed:"",placeholder:"","safe-area-inset-top":"",onClickLeft:n[2]||(n[2]=d=>v.$router.back())},{right:p(()=>[e("div",rt,[m(S,{size:"small",type:"primary",plain:"",onClick:n[0]||(n[0]=d=>y.value=!0),class:"template-btn"},{default:p(()=>n[11]||(n[11]=[I(" 模板 ")])),_:1,__:[11]}),m(S,{size:"small",type:"primary",plain:"",onClick:n[1]||(n[1]=d=>v.$router.push("/subscription/add"))},{default:p(()=>n[12]||(n[12]=[I(" 添加订阅 ")])),_:1,__:[12]})])]),_:1}),e("div",ut,[e("div",_t,[n[19]||(n[19]=e("div",{class:"stats-header"},[e("h3",null,"订阅概览")],-1)),e("div",vt,[e("div",mt,[n[14]||(n[14]=e("div",{class:"stat-icon"},"📱",-1)),e("div",pt,[e("div",ht,l(o.value.total_subscriptions),1),n[13]||(n[13]=e("div",{class:"stat-label"},"总订阅数",-1))])]),e("div",ft,[n[16]||(n[16]=e("div",{class:"stat-icon"},"💰",-1)),e("div",yt,[e("div",gt,"¥"+l(o.value.monthly_cost.toFixed(2)),1),n[15]||(n[15]=e("div",{class:"stat-label"},"月费用",-1))])]),e("div",$t,[n[18]||(n[18]=e("div",{class:"stat-icon"},"⚠️",-1)),e("div",bt,[e("div",kt,l(o.value.due_soon_count),1),n[17]||(n[17]=e("div",{class:"stat-label"},"即将到期",-1))])])])]),i.value.length>0?(r(),V(K,{key:0,"left-icon":"warning-o",text:`您有 ${i.value.length} 个订阅即将到期，请及时续费`,color:"#dc2626",background:"#fef2f2",onClick:n[3]||(n[3]=d=>g.value=!0)},null,8,["text"])):X("",!0),e("div",xt,[m(P,{modelValue:b.value,"onUpdate:modelValue":n[6]||(n[6]=d=>b.value=d),onRefresh:U},{default:p(()=>[m(W,{loading:h.value,"onUpdate:loading":n[5]||(n[5]=d=>h.value=d),finished:f.value,"finished-text":"没有更多了",onLoad:j},{default:p(()=>[a.value.length===0&&!h.value?(r(),u("div",wt,[m(R,{image:"search",description:"暂无订阅记录"},{default:p(()=>[m(S,{type:"primary",size:"small",onClick:n[4]||(n[4]=d=>v.$router.push("/subscription/add"))},{default:p(()=>n[20]||(n[20]=[I(" 添加第一个订阅 ")])),_:1,__:[20]})]),_:1})])):(r(),V(Z(c.value),{key:1,subscriptions:a.value,onClickSubscription:A},null,40,["subscriptions"]))]),_:1},8,["loading","finished"])]),_:1},8,["modelValue"])])]),m(B,{show:g.value,"onUpdate:show":n[8]||(n[8]=d=>g.value=d),position:"bottom",style:{height:"60%"},round:""},{default:p(()=>[e("div",Ct,[e("div",St,[n[22]||(n[22]=e("h3",null,"即将到期的订阅",-1)),m(S,{type:"primary",size:"small",text:"",onClick:n[7]||(n[7]=d=>g.value=!1)},{default:p(()=>n[21]||(n[21]=[I(" 关闭 ")])),_:1,__:[21]})]),m(H,null,{default:p(()=>[(r(!0),u(x,null,w(i.value,d=>(r(),V(G,{key:d.id,title:d.name,label:`${d.days_until_billing}天后到期`,value:`¥${d.amount}`,"is-link":"",onClick:J=>A(d.id)},{icon:p(()=>[e("div",Lt,l(N(d.service_type)),1)]),_:2},1032,["title","label","value","onClick"]))),128))]),_:1})])]),_:1},8,["show"]),m(B,{show:y.value,"onUpdate:show":n[10]||(n[10]=d=>y.value=d),position:"bottom",style:{height:"50%"},round:""},{default:p(()=>[e("div",Tt,[e("div",Dt,[n[24]||(n[24]=e("h3",null,"选择显示模板",-1)),m(S,{type:"primary",size:"small",text:"",onClick:n[9]||(n[9]=d=>y.value=!1)},{default:p(()=>n[23]||(n[23]=[I(" 关闭 ")])),_:1,__:[23]})]),e("div",Mt,[(r(!0),u(x,null,w(t.value,d=>(r(),u("div",{key:d.name,class:D(["template-item",{active:s.value===d.name}]),onClick:J=>$(d.name)},[e("div",Ot,[e("div",zt,l(d.icon),1),e("div",Vt,[e("h4",null,l(d.title),1),e("p",null,l(d.description),1)])])],10,It))),128))])])]),_:1},8,["show"])])}}},Ut=z(qt,[["__scopeId","data-v-20dabb13"]]);export{Ut as default};
