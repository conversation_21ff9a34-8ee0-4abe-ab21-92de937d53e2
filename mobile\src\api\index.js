import axios from 'axios'
import { showToast, showLoadingToast, closeToast } from 'vant'
import router from '@/router'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  withCredentials: true, // 重要：携带cookies用于session认证
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 显示加载提示（可选）
    if (config.showLoading !== false) {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        duration: 0
      })
    }
    
    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }
    
    console.log('API请求:', config.method?.toUpperCase(), config.url, config.data || config.params)
    return config
  },
  error => {
    closeToast()
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    closeToast()
    
    const { data } = response
    console.log('API响应:', response.config.url, data)
    
    // 检查业务状态码
    if (data && data.success === false) {
      // 业务错误
      if (data.error) {
        showToast({
          message: data.error,
          type: 'fail'
        })
      }
      return Promise.reject(new Error(data.error || '请求失败'))
    }
    
    return data
  },
  error => {
    closeToast()
    
    console.error('响应错误:', error)
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // 未授权，跳转到登录页
          showToast({
            message: '请先登录',
            type: 'fail'
          })
          router.push('/login')
          break
        case 403:
          showToast({
            message: '权限不足',
            type: 'fail'
          })
          break
        case 404:
          showToast({
            message: '请求的资源不存在',
            type: 'fail'
          })
          break
        case 500:
          showToast({
            message: '服务器内部错误',
            type: 'fail'
          })
          break
        default:
          showToast({
            message: data?.error || data?.message || `请求失败 (${status})`,
            type: 'fail'
          })
      }
    } else if (error.code === 'ECONNABORTED') {
      showToast({
        message: '请求超时，请检查网络连接',
        type: 'fail'
      })
    } else {
      showToast({
        message: '网络错误，请检查网络连接',
        type: 'fail'
      })
    }
    
    return Promise.reject(error)
  }
)

export default api
