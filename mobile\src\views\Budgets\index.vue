<template>
  <div class="budgets-page">
    <van-nav-bar
      title="预算管理"
      fixed
      placeholder
      safe-area-inset-top
    >
      <template #right>
        <van-icon name="plus" size="18" @click="addBudget" />
      </template>
    </van-nav-bar>

    <div class="page-container">
      <div v-if="loading" class="loading-container">
        <van-loading size="24px" />
        <span>加载中...</span>
      </div>

      <div v-else>
        <!-- 预算概览卡片 -->
        <div class="budget-overview-card">
          <div class="overview-header">
            <div class="overview-title">
              <h3>预算概览</h3>
              <div class="overview-trend">
                <van-icon name="chart-trending-o" size="14" />
                <span>{{ Math.round(totalUsagePercentage) }}%</span>
              </div>
            </div>
            <div class="overview-icon">
              <van-icon name="gold-coin-o" size="20" />
            </div>
          </div>

          <!-- 紧凑统计行 -->
          <div class="compact-stats">
            <div class="stat-row">
              <div class="stat-item">
                <span class="stat-label">总预算</span>
                <span class="stat-value">{{ formatCurrency(totalBudgetAmount) }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">已使用</span>
                <span class="stat-value used">{{ formatCurrency(totalUsedAmount) }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">剩余</span>
                <span class="stat-value remaining">{{ formatCurrency(totalBudgetAmount - totalUsedAmount) }}</span>
              </div>
            </div>
          </div>

          <!-- 总体使用率 -->
          <div class="overall-progress">
            <div class="progress-header">
              <span>总体使用率</span>
              <span class="percentage">{{ totalUsagePercentage.toFixed(1) }}%</span>
            </div>
            <van-progress
              :percentage="Math.min(totalUsagePercentage, 100)"
              :color="getProgressColor(totalUsagePercentage)"
              stroke-width="6"
            />
          </div>
        </div>

        <!-- 月度预算概览 -->
        <div class="monthly-overview-card">
          <div class="monthly-header">
            <div class="monthly-title">
              <h3>2025年月度预算</h3>
              <div class="year-selector">
                <van-icon name="calendar-o" size="14" />
                <span>2025</span>
              </div>
            </div>
            <div class="monthly-icon">
              <van-icon name="chart-trending-o" size="20" />
            </div>
          </div>

          <!-- 年度预算详情入口 -->
          <div class="annual-budget-entry" @click="goToAnnualBudget">
            <div class="entry-content">
              <div class="entry-icon">📊</div>
              <div class="entry-info">
                <div class="entry-title">查看今年预算详情</div>
                <div class="entry-subtitle">{{ currentYear }}年月度预算概览</div>
              </div>
              <van-icon name="arrow" />
            </div>
          </div>
        </div>

        <!-- 快速操作卡片 -->
        <div class="quick-actions-card">
          <div class="actions-header">
            <div class="actions-title">
              <h3>快速操作</h3>
              <div class="actions-subtitle">管理您的预算</div>
            </div>
            <div class="actions-icon">
              <van-icon name="setting-o" size="20" />
            </div>
          </div>

          <div class="action-buttons">
            <div class="action-button" @click="goToBudgetsList">
              <div class="action-icon">📊</div>
              <div class="action-text">
                <div class="action-title">查看预算列表</div>
                <div class="action-desc">管理所有预算项目</div>
              </div>
              <van-icon name="arrow" />
            </div>

            <div class="action-button" @click="addBudget">
              <div class="action-icon">➕</div>
              <div class="action-text">
                <div class="action-title">添加新预算</div>
                <div class="action-desc">创建预算计划</div>
              </div>
              <van-icon name="arrow" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useBudgetsStore } from '@/stores/budgets'
import { useTransactionsStore } from '@/stores/transactions'
import { formatCurrency } from '@/utils/format'
import { showToast } from 'vant'

const router = useRouter()
const budgetsStore = useBudgetsStore()
const transactionsStore = useTransactionsStore()

// 响应式数据
const loading = ref(false)

// 计算属性
const totalBudgetAmount = computed(() => budgetsStore.totalBudgetAmount)
const totalUsedAmount = computed(() => budgetsStore.totalUsedAmount)
const totalUsagePercentage = computed(() => budgetsStore.totalUsagePercentage)

// 月度预算数据
const monthlyBudgetData = computed(() => {
  const year = 2025
  const months = []

  for (let month = 1; month <= 12; month++) {
    const monthData = calculateMonthlyBudget(year, month)
    months.push({
      month,
      name: `${month}月`,
      budget: monthData.budget,
      used: monthData.used,
      remaining: monthData.remaining,
      usagePercentage: monthData.usagePercentage,
      status: monthData.status
    })
  }

  return months
})

// 初始化
onMounted(async () => {
  await loadBudgets()
  await loadTransactions()
})

// 加载预算数据
const loadBudgets = async () => {
  loading.value = true
  try {
    await budgetsStore.fetchBudgets()
  } catch (error) {
    console.error('加载预算失败:', error)
    showToast({
      message: '加载预算失败',
      type: 'fail'
    })
  } finally {
    loading.value = false
  }
}

// 加载交易数据
const loadTransactions = async () => {
  try {
    await transactionsStore.fetchTransactions()
  } catch (error) {
    console.error('加载交易数据失败:', error)
  }
}

// 添加预算
const addBudget = () => {
  router.push('/budget/add')
}

// 查看月度详情
const viewMonthDetail = (monthData) => {
  showToast({
    message: `${monthData.name}详情：预算${formatCurrency(monthData.budget)}，已用${formatCurrency(monthData.used)}`,
    duration: 3000
  })
}

// 计算指定月份的预算情况
const calculateMonthlyBudget = (year, month) => {
  const monthStr = String(month).padStart(2, '0')
  const startDate = `${year}-${monthStr}-01`

  // 计算月末日期
  const nextMonth = month === 12 ? 1 : month + 1
  const nextYear = month === 12 ? year + 1 : year
  const endDate = new Date(nextYear, nextMonth - 1, 0).toISOString().split('T')[0]

  // 获取该月的预算总额和使用情况
  let monthlyBudget = 0
  let monthlyUsed = 0

  if (budgetsStore.budgets) {
    budgetsStore.budgets.forEach(budget => {
      // 检查预算期间是否与指定月份重叠
      if (budget.start_date <= endDate && budget.end_date >= startDate) {
        // 简单累加，与预算概览保持一致的逻辑
        monthlyBudget += budget.amount || 0
        monthlyUsed += budget.used_amount || 0
      }
    })
  }

  const remaining = monthlyBudget - monthlyUsed
  const usagePercentage = monthlyBudget > 0 ? Math.round((monthlyUsed / monthlyBudget) * 100) : 0

  // 确定状态
  let status = 'normal'
  if (usagePercentage >= 100) {
    status = 'exceeded'
  } else if (usagePercentage >= 90) {
    status = 'danger'
  } else if (usagePercentage >= 80) {
    status = 'warning'
  }

  return {
    budget: monthlyBudget,
    used: monthlyUsed,
    remaining,
    usagePercentage,
    status
  }
}

// 当前年份
const currentYear = computed(() => {
  return new Date().getFullYear()
})

// 跳转到预算列表页面
const goToBudgetsList = () => {
  router.push('/budgets/list')
}

// 跳转到年度预算详情页面
const goToAnnualBudget = () => {
  router.push('/budgets/annual')
}

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage >= 100) return '#ee0a24'
  if (percentage >= 90) return '#ff976a'
  if (percentage >= 80) return '#ffd21e'
  return '#07c160'
}
</script>

<style scoped>
.budgets-page {
  background-color: #f7f8fa;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #969799;
  gap: 12px;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #969799;
  gap: 16px;
}

.empty-icon {
  font-size: 48px;
}

/* 预算概览卡片 */
.budget-overview-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  color: #1e293b;
  padding: 18px;
  margin: 16px;
  border-radius: 20px;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 12px 24px rgba(30, 58, 138, 0.12),
    0 4px 8px rgba(30, 58, 138, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.budget-overview-card::before {
  content: '';
  position: absolute;
  top: -40%;
  right: -15%;
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, rgba(30, 58, 138, 0.06) 0%, transparent 70%);
  border-radius: 50%;
}

.budget-overview-card::after {
  content: '';
  position: absolute;
  bottom: -25%;
  left: -10%;
  width: 60px;
  height: 60px;
  background: radial-gradient(circle, rgba(55, 48, 163, 0.04) 0%, transparent 70%);
  border-radius: 50%;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  position: relative;
  z-index: 1;
}

.overview-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.overview-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  letter-spacing: -0.3px;
}

.overview-trend {
  display: flex;
  align-items: center;
  gap: 3px;
  color: #64748b;
  font-size: 12px;
  font-weight: 600;
  background: rgba(100, 116, 139, 0.1);
  padding: 4px 8px;
  border-radius: 12px;
}

.overview-icon {
  width: 36px;
  height: 36px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1e40af 0%, #3730a3 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.25);
}

/* 紧凑统计行 */
.compact-stats {
  margin-bottom: 16px;
  position: relative;
  z-index: 1;
}

.stat-row {
  display: flex;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 16px;
  border: 1px solid rgba(30, 58, 138, 0.1);
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border-right: 1px solid rgba(30, 58, 138, 0.08);
}

.stat-item:last-child {
  border-right: none;
}

.stat-label {
  font-size: 11px;
  color: #64748b;
  font-weight: 600;
  letter-spacing: 0.3px;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 14px;
  font-weight: 700;
  color: #1e293b;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  letter-spacing: -0.2px;
  text-align: center;
}

.stat-value.used {
  color: #dc2626;
}

.stat-value.remaining {
  color: #059669;
}

.overall-progress {
  position: relative;
  z-index: 1;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-header span {
  font-size: 12px;
  color: #64748b;
  font-weight: 600;
}

.percentage {
  font-weight: 700;
  color: #1e293b;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 12px;
}

/* 月度预算概览 */
.monthly-overview-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  color: #1e293b;
  padding: 18px;
  margin: 16px;
  border-radius: 20px;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 12px 24px rgba(30, 58, 138, 0.12),
    0 4px 8px rgba(30, 58, 138, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.monthly-overview-card::before {
  content: '';
  position: absolute;
  top: -40%;
  right: -15%;
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, rgba(30, 58, 138, 0.06) 0%, transparent 70%);
  border-radius: 50%;
}

.monthly-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  position: relative;
  z-index: 1;
}

.monthly-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.monthly-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  letter-spacing: -0.3px;
}

.year-selector {
  display: flex;
  align-items: center;
  gap: 3px;
  color: #64748b;
  font-size: 12px;
  font-weight: 600;
  background: rgba(100, 116, 139, 0.1);
  padding: 4px 8px;
  border-radius: 12px;
}

.monthly-icon {
  width: 36px;
  height: 36px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1e40af 0%, #3730a3 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.25);
}

/* 年度预算详情入口 */
.annual-budget-entry {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 16px;
  margin: 0 16px 16px;
  border: 1px solid rgba(30, 58, 138, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  z-index: 1;
}

.annual-budget-entry:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.8);
}

.entry-content {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 12px;
}

.entry-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1e40af 0%, #3730a3 100%);
  color: white;
  font-size: 18px;
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.25);
}

.entry-info {
  flex: 1;
}

.entry-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 2px;
}

.entry-subtitle {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.monthly-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  position: relative;
  z-index: 1;
}

.month-item {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  padding: 8px;
  border: 1px solid rgba(30, 58, 138, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  cursor: pointer;
}

.month-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.8);
}

.month-item.warning {
  border-color: rgba(245, 158, 11, 0.3);
  background: rgba(255, 251, 235, 0.8);
}

.month-item.danger {
  border-color: rgba(239, 68, 68, 0.3);
  background: rgba(254, 242, 242, 0.8);
}

.month-item.exceeded {
  border-color: rgba(220, 38, 38, 0.3);
  background: rgba(254, 242, 242, 0.8);
}

.month-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.month-name {
  font-size: 13px;
  font-weight: 700;
  color: #1e293b;
}

.month-usage {
  font-size: 10px;
  font-weight: 600;
  color: #64748b;
  background: rgba(100, 116, 139, 0.1);
  padding: 2px 6px;
  border-radius: 8px;
}

.month-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.budget-amount {
  font-size: 11px;
  color: #64748b;
  font-weight: 500;
}

.used-amount {
  font-size: 11px;
  font-weight: 600;
  color: #1e293b;
}

.used-amount.over-budget {
  color: #ef4444;
}

.month-amounts {
  margin-bottom: 8px;
}

.amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2px;
}

.amount-label {
  font-size: 10px;
  color: #64748b;
  font-weight: 600;
}

.amount-value {
  font-size: 11px;
  font-weight: 600;
  color: #1e293b;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.amount-value.used {
  color: #dc2626;
}

.amount-value.remaining {
  color: #059669;
}

.month-progress {
  margin-top: 6px;
}

/* 预算提醒 */
.budget-alerts {
  margin: 0 16px 16px;
}

.budget-alerts .van-notice-bar {
  margin-bottom: 8px;
}

/* 预算列表 */
.budget-list {
  margin-bottom: 20px;
}

/* 预算卡片样式 */
.budget-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 0 16px;
}

.budget-card {
  background: #ffffff;
  border-radius: 16px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.budget-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #3b82f6;
}

.budget-card:active {
  transform: scale(0.98);
}

.budget-card.warning {
  border-color: #f59e0b;
  background: linear-gradient(135deg, #ffffff 0%, #fffbeb 100%);
}

.budget-card.danger {
  border-color: #ef4444;
  background: linear-gradient(135deg, #ffffff 0%, #fef2f2 100%);
}

.budget-card.exceeded {
  border-color: #dc2626;
  background: linear-gradient(135deg, #ffffff 0%, #fef2f2 100%);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.budget-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.budget-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 18px;
  background: #f0f9ff;
  border: 2px solid #e2e8f0;
}

.budget-icon.warning {
  background: #fef3c7;
  border-color: #f59e0b;
}

.budget-icon.danger {
  background: #fecaca;
  border-color: #ef4444;
}

.budget-icon.exceeded {
  background: #fecaca;
  border-color: #dc2626;
}

.budget-details {
  flex: 1;
}

.project-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.budget-meta {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.category-tag {
  background: #f3f4f6;
  color: #374151;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.period-text {
  color: #6b7280;
}

.status-indicator {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-align: center;
  min-width: 50px;
}

.status-indicator.normal {
  background: #d1fae5;
  color: #065f46;
}

.status-indicator.warning {
  background: #fef3c7;
  color: #92400e;
}

.status-indicator.danger {
  background: #fecaca;
  color: #991b1b;
}

.status-indicator.exceeded {
  background: #fecaca;
  color: #991b1b;
}

/* 预算进度 */
.budget-progress {
  margin-top: 16px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.used-amount {
  font-size: 14px;
  font-weight: 600;
  color: #ef4444;
}

.total-amount {
  font-size: 14px;
  font-weight: 600;
  color: #3b82f6;
}

.progress-bar-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.progress-bar {
  flex: 1;
}

.progress-percentage {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  min-width: 35px;
  text-align: right;
}

.remaining-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.remaining-amount {
  font-size: 12px;
  color: #10b981;
  font-weight: 500;
}

.remaining-days {
  font-size: 11px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 8px;
}

:deep(.van-cell-group__title) {
  padding: 16px 16px 8px;
  font-size: 14px;
  font-weight: 600;
  color: #323233;
}

/* 快速操作卡片 */
.quick-actions-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  color: #1e293b;
  padding: 18px;
  margin: 16px;
  border-radius: 20px;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 12px 24px rgba(30, 58, 138, 0.12),
    0 4px 8px rgba(30, 58, 138, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.quick-actions-card::before {
  content: '';
  position: absolute;
  top: -40%;
  right: -15%;
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, rgba(30, 58, 138, 0.06) 0%, transparent 70%);
  border-radius: 50%;
}

.actions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  position: relative;
  z-index: 1;
}

.actions-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  letter-spacing: -0.3px;
}

.actions-subtitle {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  margin-top: 2px;
}

.actions-icon {
  width: 36px;
  height: 36px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1e40af 0%, #3730a3 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.25);
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  position: relative;
  z-index: 1;
}

.action-button {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 16px;
  padding: 16px;
  border: 1px solid rgba(30, 58, 138, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-button:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.8);
}

.action-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 16px;
  flex-shrink: 0;
}

.action-text {
  flex: 1;
}

.action-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 2px;
}

.action-desc {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}
</style>
