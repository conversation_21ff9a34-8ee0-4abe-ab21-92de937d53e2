<template>
  <div class="transfer-page">
    <van-nav-bar
      title="账户转移"
      left-arrow
      @click-left="handleBack"
      fixed
      placeholder
    >
      <template #right>
        <van-button
          type="primary"
          size="small"
          :loading="loading"
          @click="handleSubmit"
        >
          确认转账
        </van-button>
      </template>
    </van-nav-bar>

    <div class="page-container">
      <!-- 转账金额 -->
      <div class="amount-section">
        <div class="amount-label">转账金额</div>
        <div class="amount-input-container">
          <span class="currency-symbol">{{ currencySymbol }}</span>
          <van-field
            v-model="form.amount"
            type="number"
            placeholder="0.00"
            class="amount-input"
            :rules="[{ required: true, message: '请输入转账金额' }]"
            @input="onAmountInput"
          />
        </div>
      </div>

      <!-- 转账信息 -->
      <van-form @submit="handleSubmit" class="transfer-form">
        <van-cell-group inset title="转账信息">
          <!-- 货币选择 -->
          <van-field
            v-model="form.currency"
            label="货币"
            placeholder="选择货币"
            readonly
            is-link
            @click="showCurrencyPicker = true"
            :rules="[{ required: true, message: '请选择货币' }]"
          />

          <!-- 转出账户 -->
          <van-field
            v-model="selectedFromAccountName"
            label="转出账户"
            placeholder="选择转出账户"
            readonly
            is-link
            @click="showFromAccountPicker = true"
            :rules="[{ required: true, message: '请选择转出账户' }]"
          />

          <!-- 转入账户 -->
          <van-field
            v-model="selectedToAccountName"
            label="转入账户"
            placeholder="选择转入账户"
            readonly
            is-link
            @click="showToAccountPicker = true"
            :rules="[{ required: true, message: '请选择转入账户' }]"
          />

          <!-- 转账日期 -->
          <van-field
            v-model="form.date"
            label="日期"
            placeholder="选择转账日期"
            readonly
            is-link
            @click="handleDateClick"
            :rules="[{ required: true, message: '请选择转账日期' }]"
          />

          <!-- 转账描述 -->
          <van-field
            v-model="form.description"
            label="描述"
            placeholder="请输入转账描述（可选）"
            clearable
          />
        </van-cell-group>

        <!-- 账户余额信息 -->
        <van-cell-group inset title="账户信息" v-if="selectedFromAccount">
          <van-cell
            :title="`${selectedFromAccount.name} 当前余额`"
            :value="formatBalance(selectedFromAccount.current_balance || selectedFromAccount.initial_balance || 0, selectedFromAccount.currency)"
            :label="selectedFromAccount.type"
          />
          <van-cell
            v-if="selectedToAccount"
            :title="`${selectedToAccount.name} 当前余额`"
            :value="formatBalance(selectedToAccount.current_balance || selectedToAccount.initial_balance || 0, selectedToAccount.currency)"
            :label="selectedToAccount.type"
          />
        </van-cell-group>

        <!-- 附加信息 -->
        <van-cell-group inset title="附加信息">
          <van-field
            v-model="form.attachment"
            label="备注"
            type="textarea"
            placeholder="添加备注信息（可选）"
            rows="3"
            autosize
            maxlength="200"
            show-word-limit
          />
        </van-cell-group>
      </van-form>
    </div>

    <!-- 货币选择器 -->
    <van-popup v-model:show="showCurrencyPicker" position="bottom">
      <van-picker
        :columns="currencyColumns"
        title="选择货币"
        @confirm="onCurrencyConfirm"
        @cancel="showCurrencyPicker = false"
      />
    </van-popup>

    <!-- 转出账户选择器 -->
    <van-popup v-model:show="showFromAccountPicker" position="bottom">
      <van-picker
        :columns="fromAccountColumns"
        title="选择转出账户"
        @confirm="onFromAccountConfirm"
        @cancel="showFromAccountPicker = false"
      />
    </van-popup>

    <!-- 转入账户选择器 -->
    <van-popup v-model:show="showToAccountPicker" position="bottom">
      <van-picker
        :columns="toAccountColumns"
        title="选择转入账户"
        @confirm="onToAccountConfirm"
        @cancel="showToAccountPicker = false"
      />
    </van-popup>

    <!-- 日期选择器 -->
    <van-popup
      v-model:show="showDatePicker"
      position="bottom"
      round
    >
      <van-date-picker
        v-model="selectedDate"
        title="选择日期"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useTransactionsStore } from '@/stores/transactions'
import { useAccountsStore } from '@/stores/accounts'
import { showToast, showConfirmDialog } from 'vant'
import { formatCurrency } from '@/utils/format'
import { getCurrencyOptions, getCurrencySymbol, DEFAULT_CURRENCY } from '@/config/currencies'

const router = useRouter()
const transactionsStore = useTransactionsStore()
const accountsStore = useAccountsStore()

// 响应式数据
const loading = ref(false)
const showDatePicker = ref(false)
const showCurrencyPicker = ref(false)
const showFromAccountPicker = ref(false)
const showToAccountPicker = ref(false)

// 表单数据
const form = reactive({
  amount: '',
  currency: 'MYR',
  fromAccount: '',
  toAccount: '',
  date: '',
  description: '',
  attachment: ''
})

// 日期相关
const selectedDate = ref([])
const minDate = new Date(2020, 0, 1)
const maxDate = new Date(2030, 11, 31)

// 货币数据
const currencies = getCurrencyOptions()

// 计算属性
const currencyColumns = computed(() => currencies)

// 根据选择的货币过滤账户（转出账户）
const fromAccountColumns = computed(() => {
  return accountsStore.accounts
    .filter(account => account.currency === form.currency)
    .map(account => ({
      text: `${account.name} (${account.type}) - ${formatBalance(account.current_balance || account.initial_balance || 0, account.currency)}`,
      value: account.id
    }))
})

// 根据选择的货币过滤账户（转入账户，排除已选择的转出账户）
const toAccountColumns = computed(() => {
  return accountsStore.accounts
    .filter(account =>
      account.currency === form.currency &&
      account.id !== form.fromAccount
    )
    .map(account => ({
      text: `${account.name} (${account.type}) - ${formatBalance(account.current_balance || account.initial_balance || 0, account.currency)}`,
      value: account.id
    }))
})

const selectedFromAccount = computed(() => {
  return accountsStore.accounts.find(acc => acc.id === form.fromAccount)
})

const selectedToAccount = computed(() => {
  return accountsStore.accounts.find(acc => acc.id === form.toAccount)
})

const selectedFromAccountName = computed(() => {
  const account = selectedFromAccount.value
  return account ? `${account.name} (${account.type})` : ''
})

const selectedToAccountName = computed(() => {
  const account = selectedToAccount.value
  return account ? `${account.name} (${account.type})` : ''
})

const currencySymbol = computed(() => {
  return getCurrencySymbol(form.currency)
})

// 格式化余额显示
const formatBalance = (balance, currency = 'MYR') => {
  return formatCurrency(balance, currency, 2)
}

// 初始化
onMounted(async () => {
  // 设置默认日期为今天
  const today = new Date()
  selectedDate.value = [today.getFullYear(), today.getMonth() + 1, today.getDate()]
  form.date = formatDate(today)

  // 加载账户数据
  try {
    await accountsStore.fetchAccountsWithBalances()
  } catch (error) {
    console.error('加载账户失败:', error)
    showToast('加载账户失败')
  }
})

// 方法
const formatDate = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

const onAmountInput = (value) => {
  // 确保金额为正数
  if (value && parseFloat(value) < 0) {
    form.amount = Math.abs(parseFloat(value)).toString()
  }
}

const handleDateClick = () => {
  showDatePicker.value = true
}

const onDateConfirm = () => {
  const [year, month, day] = selectedDate.value
  const dateObj = new Date(year, month - 1, day)
  form.date = formatDate(dateObj)
  showDatePicker.value = false
}

const onCurrencyConfirm = ({ selectedOptions }) => {
  const oldCurrency = form.currency
  const newCurrency = selectedOptions[0]?.value || DEFAULT_CURRENCY

  // 如果货币发生变化，清空账户选择
  if (oldCurrency !== newCurrency) {
    form.currency = newCurrency
    form.fromAccount = ''
    form.toAccount = ''
  }

  showCurrencyPicker.value = false
}

const onFromAccountConfirm = ({ selectedOptions }) => {
  const oldFromAccount = form.fromAccount
  form.fromAccount = selectedOptions[0]?.value || ''

  // 如果转出账户发生变化，且转入账户是同一个，清空转入账户
  if (oldFromAccount !== form.fromAccount && form.toAccount === form.fromAccount) {
    form.toAccount = ''
  }

  showFromAccountPicker.value = false
}

const onToAccountConfirm = ({ selectedOptions }) => {
  form.toAccount = selectedOptions[0]?.value || ''
  showToAccountPicker.value = false
}

const validateForm = () => {
  if (!form.amount || parseFloat(form.amount) <= 0) {
    showToast('请输入有效的转账金额')
    return false
  }

  if (!form.currency) {
    showToast('请选择货币')
    return false
  }

  if (!form.fromAccount) {
    showToast('请选择转出账户')
    return false
  }

  if (!form.toAccount) {
    showToast('请选择转入账户')
    return false
  }

  if (form.fromAccount === form.toAccount) {
    showToast('转出账户和转入账户不能相同')
    return false
  }

  if (!form.date) {
    showToast('请选择转账日期')
    return false
  }

  // 验证转出账户余额是否充足
  const fromAccount = selectedFromAccount.value
  if (fromAccount) {
    const currentBalance = fromAccount.current_balance || fromAccount.initial_balance || 0
    const transferAmount = parseFloat(form.amount)

    if (currentBalance < transferAmount) {
      showToast(`转出账户余额不足，当前余额：${formatBalance(currentBalance, fromAccount.currency)}`)
      return false
    }
  }

  return true
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  loading.value = true

  try {
    const transferData = {
      amount: parseFloat(form.amount),
      currency: form.currency,
      fromAccount: form.fromAccount,
      toAccount: form.toAccount,
      date: form.date,
      description: form.description.trim() || '账户转移',
      attachment: form.attachment.trim()
    }

    await transactionsStore.createAccountTransfer(transferData)

    showToast({
      message: '转账成功',
      type: 'success'
    })

    // 返回上一页
    router.back()

  } catch (error) {
    console.error('转账失败:', error)
    showToast({
      message: error.message || '转账失败',
      type: 'fail'
    })
  } finally {
    loading.value = false
  }
}

const handleBack = async () => {
  // 检查是否有未保存的数据
  const hasData = form.amount || form.fromAccount || form.toAccount

  if (hasData) {
    const result = await showConfirmDialog({
      title: '确认离开',
      message: '您有未保存的数据，确定要离开吗？'
    })

    if (result) {
      router.back()
    }
  } else {
    router.back()
  }
}
</script>

<style scoped>
.transfer-page {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.page-container {
  padding-bottom: 20px;
}

.amount-section {
  background: white;
  padding: 24px 16px;
  margin-bottom: 12px;
  text-align: center;
}

.amount-label {
  font-size: 14px;
  color: #646566;
  margin-bottom: 12px;
}

.amount-input-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.currency-symbol {
  font-size: 24px;
  font-weight: 600;
  color: #323233;
}

:deep(.amount-input .van-field__control) {
  font-size: 32px;
  font-weight: 600;
  text-align: center;
  color: #323233;
}

:deep(.amount-input .van-field__body) {
  border: none;
  background: transparent;
}

.transfer-form {
  margin-bottom: 20px;
}

:deep(.van-cell-group__title) {
  padding: 16px 16px 8px;
  font-size: 14px;
  font-weight: 600;
  color: #323233;
}

:deep(.van-field__label) {
  font-weight: 500;
  color: #323233;
}

:deep(.van-field__control) {
  color: #323233;
}

:deep(.van-field__control::placeholder) {
  color: #c8c9cc;
}

/* 选择器样式 */
:deep(.van-popup) {
  border-radius: 16px 16px 0 0;
}

:deep(.van-picker__toolbar) {
  padding: 16px;
}

:deep(.van-picker__title) {
  font-size: 16px;
  font-weight: 600;
}

:deep(.van-picker__confirm) {
  color: #1989fa;
  font-weight: 600;
}

:deep(.van-picker__cancel) {
  color: #646566;
}

/* 日期选择器样式 */
:deep(.van-date-picker) {
  background: white;
}

:deep(.van-date-picker .van-picker__toolbar) {
  border-bottom: 1px solid #ebedf0;
  background: white;
}

:deep(.van-date-picker .van-picker__columns) {
  background: white;
}

:deep(.van-date-picker .van-picker__column) {
  background: white;
}

:deep(.van-date-picker .van-picker__column-item) {
  color: #323233;
}

/* 账户信息卡片样式 */
:deep(.van-cell__title) {
  font-weight: 500;
}

:deep(.van-cell__value) {
  font-weight: 600;
  color: #07c160;
}

:deep(.van-cell__label) {
  color: #969799;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .amount-input-container {
    flex-direction: column;
    gap: 4px;
  }

  .currency-symbol {
    font-size: 20px;
  }

  :deep(.amount-input .van-field__control) {
    font-size: 28px;
  }
}
</style>
