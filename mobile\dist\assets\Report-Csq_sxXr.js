import{a as R,r as G,l as u,m as de,j as ve,s as O,c as M,e as d,d as a,w as h,f as m,t as l,n as F,q as g,p as U,v as pe,I as A,F as L,k as W,g as me,o as f,i as he}from"./index--MNqwREY.js";import{u as ge}from"./transactions-F5TqZ8Qm.js";import{B as fe}from"./BaseChart-BEnmQZd1.js";import{f as k}from"./format-wz8GKlWC.js";import{_ as _e}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./transactions-CrdnwW_k.js";const ye={class:"report-page"},we={class:"page-container"},Ce={class:"report-type-selector"},be={class:"time-selector"},De={class:"report-overview"},Me={class:"overview-header"},Fe={class:"report-date"},ke={class:"overview-stats"},xe={class:"stat-item"},$e={class:"stat-value income"},Se={class:"stat-item"},Ye={class:"stat-value expense"},Ie={class:"stat-item"},Re={class:"stat-item"},Te={class:"stat-value"},qe={class:"detailed-analysis"},Ee={class:"analysis-section"},Be={class:"chart-container"},Ne={class:"analysis-section"},ze={class:"analysis-section"},Pe={class:"analysis-section"},Ve={class:"health-assessment"},je={class:"health-score"},Oe={class:"score-value"},Ue={class:"score-description"},Ae={class:"health-indicators"},Le={class:"indicator-item"},We={class:"indicator-bar"},Xe={class:"indicator-value"},Ge={class:"indicator-item"},He={class:"indicator-bar"},Je={class:"indicator-value"},Ke={class:"indicator-item"},Qe={class:"indicator-bar"},Ze={class:"indicator-value"},et={class:"analysis-section"},tt={class:"suggestions"},at={class:"suggestion-icon"},nt={class:"suggestion-content"},st={class:"suggestion-title"},ot={class:"suggestion-description"},rt={class:"action-buttons"},lt={__name:"Report",setup(it){me();const T=ge(),X=R(!1),v=R("monthly"),b=R(!1),_=R([]),x=()=>{if(!_.value||_.value.length===0)return new Date;const[e,t=1,n=1]=_.value;return new Date(e,t-1,n)},o=G({totalIncome:0,totalExpense:0,netIncome:0,incomeChange:0,expenseChange:0,netIncomeChange:0,savingsRateChange:0}),q=G({comparison:null}),H=u(()=>{switch(v.value){case"monthly":return"year-month";case"quarterly":return"year-month";case"yearly":return"year";default:return"year-month"}}),J=u(()=>{const e=x();switch(v.value){case"monthly":return`${e.getFullYear()}年${e.getMonth()+1}月`;case"quarterly":const t=Math.floor(e.getMonth()/3)+1;return`${e.getFullYear()}年第${t}季度`;case"yearly":return`${e.getFullYear()}年`;default:return""}}),K=u(()=>{switch(v.value){case"monthly":return"月度财务报告";case"quarterly":return"季度财务报告";case"yearly":return"年度财务报告";default:return"财务报告"}}),Q=u(()=>{const e=x();switch(v.value){case"monthly":const t=new Date(e.getFullYear(),e.getMonth()+1,0).getDate();return`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-01 至 ${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${t}`;case"quarterly":const r=Math.floor(e.getMonth()/3)*3+1,c=r+2;return`${e.getFullYear()}-${String(r).padStart(2,"0")}-01 至 ${e.getFullYear()}-${String(c).padStart(2,"0")}-31`;case"yearly":return`${e.getFullYear()}-01-01 至 ${e.getFullYear()}-12-31`;default:return""}}),p=u(()=>o.totalIncome===0?0:Math.round(o.netIncome/o.totalIncome*100)),$=u(()=>{const e=N().filter(n=>n.type==="expense"),t={};return e.forEach(n=>{t[n.category]=(t[n.category]||0)+n.amount}),Object.entries(t).map(([n,r])=>({name:n,amount:r,percentage:o.totalExpense>0?Math.round(r/o.totalExpense*100):0})).sort((n,r)=>r.amount-n.amount).slice(0,5)}),Z=u(()=>{const e=N().filter(n=>n.type==="income"),t={};return e.forEach(n=>{t[n.category]=(t[n.category]||0)+n.amount}),Object.entries(t).map(([n,r])=>({name:n,amount:r,percentage:o.totalIncome>0?Math.round(r/o.totalIncome*100):0})).sort((n,r)=>r.amount-n.amount)}),y=u(()=>{let e=60;return p.value>=30?e+=20:p.value>=20?e+=15:p.value>=10?e+=10:p.value>=0&&(e+=5),e+=Math.min(B.value/5,10),e+=Math.min(E.value/5,10),Math.min(Math.round(e),100)}),ee=u(()=>y.value>=80?"excellent":y.value>=60?"good":y.value>=40?"fair":"poor"),te=u(()=>y.value>=80?"财务状况优秀":y.value>=60?"财务状况良好":y.value>=40?"财务状况一般":"需要改善财务状况"),E=u(()=>{const e=o.expenseChange;return e<=-10?90:e<=0?80:e<=10?70:e<=20?60:50}),B=u(()=>{const e=Math.abs(o.incomeChange);return e<=5?90:e<=10?80:e<=20?70:e<=30?60:50}),ae=u(()=>{const e=[];return p.value<10&&e.push({id:1,type:"warning",icon:"⚠️",title:"储蓄率偏低",description:"建议将储蓄率提高到收入的10%以上，为未来做好准备。"}),o.expenseChange>20&&e.push({id:2,type:"danger",icon:"🚨",title:"支出增长过快",description:"支出增长超过20%，建议审查和控制非必要支出。"}),p.value>=20&&e.push({id:3,type:"success",icon:"🎉",title:"储蓄表现优秀",description:"储蓄率达到20%以上，可以考虑进行投资理财。"}),$.value.length>0&&$.value[0].percentage>40&&e.push({id:4,type:"info",icon:"💡",title:"支出过于集中",description:`${$.value[0].name}占支出比例过高，建议分散支出结构。`}),e});de(async()=>{const e=new Date;_.value=[e.getFullYear(),e.getMonth()+1],await ne()}),ve([v,_],()=>{S()},{deep:!0});const ne=async()=>{X.value=!0;try{await T.fetchTransactions(),S()}catch(e){console.error("加载数据失败:",e),O("加载数据失败")}finally{X.value=!1}},S=()=>{const e=N(),t=se(),n=e.filter(i=>i.type==="income").reduce((i,s)=>i+s.amount,0),r=e.filter(i=>i.type==="expense").reduce((i,s)=>i+s.amount,0),c=n-r,w=t.filter(i=>i.type==="income").reduce((i,s)=>i+s.amount,0),C=t.filter(i=>i.type==="expense").reduce((i,s)=>i+s.amount,0),D=w-C,P=n>0?c/n*100:0,V=w>0?D/w*100:0;o.totalIncome=n,o.totalExpense=r,o.netIncome=c,o.incomeChange=z(w,n),o.expenseChange=z(C,r),o.netIncomeChange=z(D,c),o.savingsRateChange=Math.round(P-V),oe(e)},N=()=>{const e=x();let t,n;switch(v.value){case"monthly":t=new Date(e.getFullYear(),e.getMonth(),1),n=new Date(e.getFullYear(),e.getMonth()+1,0);break;case"quarterly":const r=Math.floor(e.getMonth()/3);t=new Date(e.getFullYear(),r*3,1),n=new Date(e.getFullYear(),r*3+3,0);break;case"yearly":t=new Date(e.getFullYear(),0,1),n=new Date(e.getFullYear(),11,31);break;default:t=new Date(e.getFullYear(),e.getMonth(),1),n=new Date(e.getFullYear(),e.getMonth()+1,0)}return T.transactions.filter(r=>{const c=new Date(r.date);return c>=t&&c<=n})},se=()=>{const e=x();let t,n;switch(v.value){case"monthly":t=new Date(e.getFullYear(),e.getMonth()-1,1),n=new Date(e.getFullYear(),e.getMonth(),0);break;case"quarterly":const r=Math.floor(e.getMonth()/3);t=new Date(e.getFullYear(),(r-1)*3,1),n=new Date(e.getFullYear(),r*3,0);break;case"yearly":t=new Date(e.getFullYear()-1,0,1),n=new Date(e.getFullYear()-1,11,31);break;default:t=new Date(e.getFullYear(),e.getMonth()-1,1),n=new Date(e.getFullYear(),e.getMonth(),0)}return T.transactions.filter(r=>{const c=new Date(r.date);return c>=t&&c<=n})},oe=e=>{e.filter(t=>t.type==="income"),e.filter(t=>t.type==="expense"),q.comparison={title:{text:"收支对比",left:"center",textStyle:{fontSize:16,fontWeight:"bold"}},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{data:["收入","支出"],bottom:10},series:[{name:"收支对比",type:"pie",radius:["40%","70%"],center:["50%","45%"],data:[{name:"收入",value:o.totalIncome,itemStyle:{color:"#07c160"}},{name:"支出",value:o.totalExpense,itemStyle:{color:"#ee0a24"}}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]}},z=(e,t)=>e===0?t>0?100:0:Math.round((t-e)/e*100),Y=e=>e>0?`+${e}%`:e<0?`${e}%`:"0%",I=(e,t=!1)=>e>0?t?"negative":"positive":e<0?t?"positive":"negative":"neutral",re=e=>e===0?"rank-1":e===1?"rank-2":e===2?"rank-3":"",le=()=>{S()},ie=()=>{b.value=!1,S()},ce=()=>{O("分享功能开发中...")},ue=()=>{O("导出功能开发中...")};return(e,t)=>{const n=m("van-icon"),r=m("van-nav-bar"),c=m("van-tab"),w=m("van-tabs"),C=m("van-cell"),D=m("van-cell-group"),P=m("van-button"),V=m("van-date-picker"),i=m("van-popup");return f(),M("div",ye,[d(r,{title:"财务报告","left-arrow":"",onClickLeft:t[0]||(t[0]=s=>e.$router.back()),fixed:"",placeholder:""},{right:h(()=>[d(n,{name:"share",size:"18",onClick:ce})]),_:1}),a("div",we,[a("div",Ce,[d(w,{active:v.value,"onUpdate:active":t[1]||(t[1]=s=>v.value=s),onChange:le},{default:h(()=>[d(c,{title:"月度报告",name:"monthly"}),d(c,{title:"季度报告",name:"quarterly"}),d(c,{title:"年度报告",name:"yearly"})]),_:1},8,["active"])]),a("div",be,[d(C,{title:"报告时间",value:J.value,"is-link":"",onClick:t[2]||(t[2]=s=>b.value=!0)},null,8,["value"])]),a("div",De,[a("div",Me,[a("h3",null,l(K.value),1),a("div",Fe,l(Q.value),1)]),a("div",ke,[a("div",xe,[t[6]||(t[6]=a("div",{class:"stat-label"},"总收入",-1)),a("div",$e,l(F(k)(o.totalIncome)),1),a("div",{class:g(["stat-change",I(o.incomeChange)])},l(Y(o.incomeChange)),3)]),a("div",Se,[t[7]||(t[7]=a("div",{class:"stat-label"},"总支出",-1)),a("div",Ye,l(F(k)(o.totalExpense)),1),a("div",{class:g(["stat-change",I(o.expenseChange,!0)])},l(Y(o.expenseChange)),3)]),a("div",Ie,[t[8]||(t[8]=a("div",{class:"stat-label"},"净收入",-1)),a("div",{class:g(["stat-value",o.netIncome>=0?"income":"expense"])},l(F(k)(o.netIncome)),3),a("div",{class:g(["stat-change",I(o.netIncomeChange)])},l(Y(o.netIncomeChange)),3)]),a("div",Re,[t[9]||(t[9]=a("div",{class:"stat-label"},"储蓄率",-1)),a("div",Te,l(p.value)+"%",1),a("div",{class:g(["stat-change",I(o.savingsRateChange)])},l(Y(o.savingsRateChange)),3)])])]),a("div",qe,[a("div",Ee,[t[10]||(t[10]=a("div",{class:"section-header"},[a("h4",null,"收支对比")],-1)),a("div",Be,[q.comparison?(f(),U(fe,{key:0,options:q.comparison,height:"200px"},null,8,["options"])):pe("",!0)])]),a("div",Ne,[t[11]||(t[11]=a("div",{class:"section-header"},[a("h4",null,"支出分类排行")],-1)),d(D,{inset:""},{default:h(()=>[(f(!0),M(L,null,W($.value,(s,j)=>(f(),U(C,{key:s.name,title:s.name,label:`占总支出 ${s.percentage}%`,value:F(k)(s.amount)},{icon:h(()=>[a("div",{class:g(["rank-badge",re(j)])},l(j+1),3)]),_:2},1032,["title","label","value"]))),128))]),_:1})]),a("div",ze,[t[13]||(t[13]=a("div",{class:"section-header"},[a("h4",null,"收入来源分析")],-1)),d(D,{inset:""},{default:h(()=>[(f(!0),M(L,null,W(Z.value,(s,j)=>(f(),U(C,{key:s.name,title:s.name,label:`占总收入 ${s.percentage}%`,value:F(k)(s.amount)},{icon:h(()=>t[12]||(t[12]=[a("div",{class:"income-icon"},"💰",-1)])),_:2},1032,["title","label","value"]))),128))]),_:1})]),a("div",Pe,[t[18]||(t[18]=a("div",{class:"section-header"},[a("h4",null,"财务健康度评估")],-1)),a("div",Ve,[a("div",je,[a("div",{class:g(["score-circle",ee.value])},[a("span",Oe,l(y.value),1),t[14]||(t[14]=a("span",{class:"score-label"},"分",-1))],2),a("div",Ue,l(te.value),1)]),a("div",Ae,[a("div",Le,[t[15]||(t[15]=a("div",{class:"indicator-label"},"储蓄率",-1)),a("div",We,[a("div",{class:"indicator-progress",style:A({width:`${Math.min(p.value,100)}%`})},null,4)]),a("div",Xe,l(p.value)+"%",1)]),a("div",Ge,[t[16]||(t[16]=a("div",{class:"indicator-label"},"支出控制",-1)),a("div",He,[a("div",{class:"indicator-progress",style:A({width:`${E.value}%`})},null,4)]),a("div",Je,l(E.value)+"%",1)]),a("div",Ke,[t[17]||(t[17]=a("div",{class:"indicator-label"},"收入稳定性",-1)),a("div",Qe,[a("div",{class:"indicator-progress",style:A({width:`${B.value}%`})},null,4)]),a("div",Ze,l(B.value)+"%",1)])])])]),a("div",et,[t[19]||(t[19]=a("div",{class:"section-header"},[a("h4",null,"理财建议")],-1)),a("div",tt,[(f(!0),M(L,null,W(ae.value,s=>(f(),M("div",{key:s.id,class:g(["suggestion-item",s.type])},[a("div",at,l(s.icon),1),a("div",nt,[a("div",st,l(s.title),1),a("div",ot,l(s.description),1)])],2))),128))])])]),a("div",rt,[d(P,{type:"primary",size:"large",onClick:ue,block:""},{default:h(()=>t[20]||(t[20]=[he(" 导出报告 ")])),_:1,__:[20]})])]),d(i,{show:b.value,"onUpdate:show":t[5]||(t[5]=s=>b.value=s),position:"bottom",round:""},{default:h(()=>[d(V,{modelValue:_.value,"onUpdate:modelValue":t[3]||(t[3]=s=>_.value=s),type:H.value,title:"选择时间",onConfirm:ie,onCancel:t[4]||(t[4]=s=>b.value=!1)},null,8,["modelValue","type"])]),_:1},8,["show"])])}}},ht=_e(lt,[["__scopeId","data-v-65ee305a"]]);export{ht as default};
