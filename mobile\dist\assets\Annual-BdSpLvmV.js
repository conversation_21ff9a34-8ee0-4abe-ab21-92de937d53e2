import{a as S,l as p,m as I,D as q,c as D,e as c,d as t,E as z,f as g,w as G,t as l,F as j,k as H,G as J,g as K,o as k,q as C}from"./index--MNqwREY.js";import{useBudgetsStore as Q}from"./budgets-DJh-v5Y8.js";import{u as W}from"./transactions-F5TqZ8Qm.js";import{_ as X}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./transactions-CrdnwW_k.js";const Z={class:"annual-budget-page"},tt={class:"page-container"},et={class:"filter-section"},st={class:"annual-stats-card"},at={class:"stats-header"},ot={class:"stats-title"},nt={class:"stats-grid"},lt={class:"stat-item"},it={class:"stat-value"},rt={class:"stat-item"},dt={class:"stat-value used"},ut={class:"stat-item"},ct={class:"stat-value remaining"},vt={class:"stat-item"},mt={class:"stat-value"},_t={class:"annual-progress"},pt={class:"monthly-details-card"},gt={class:"monthly-list"},ft=["onClick"],ht={class:"month-info"},wt={class:"month-name"},xt={class:"month-period"},bt={class:"month-amounts"},yt={class:"amount-item"},St={class:"amount-value"},Dt={class:"amount-item"},kt={class:"amount-value used"},Ct={class:"amount-item"},Tt={class:"month-status"},Yt={class:"usage-percentage"},Bt={__name:"Annual",setup(Mt){K();const y=Q(),M=W(),i=S("all"),d=S("all"),T=S(!1),$=p(()=>{const s=new Date().getFullYear(),e=[{text:"全部年份",value:"all"}];for(let n=s-2;n<=s+2;n++)e.push({text:`${n}年`,value:n});return e}),F=[{text:"全部类别",value:"all"},{text:"储蓄",value:"储蓄"},{text:"固定",value:"固定"},{text:"流动",value:"流动"},{text:"债务",value:"债务"}],V=p(()=>i.value==="all"?"全部年份":`${i.value}年`),P=p(()=>d.value==="all"?"全部类别":d.value),E=p(()=>i.value==="all"?new Date().getFullYear():i.value),v=p(()=>{let s=0,e=0;(i.value==="all"?[new Date().getFullYear()]:[i.value]).forEach(f=>{for(let u=1;u<=12;u++){const _=Y(f,u,d.value);s+=_.budget,e+=_.used}});const o=s-e,r=s>0?Math.round(e/s*100):0;return{totalBudget:s,totalUsed:e,totalRemaining:o,usagePercentage:r}}),U=p(()=>{const s=[],e=["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],n=i.value==="all"?new Date().getFullYear():i.value;for(let o=1;o<=12;o++){const r=Y(n,o,d.value);s.push({month:o,name:e[o-1],period:`${n}年${String(o).padStart(2,"0")}月`,...r})}return s.sort((o,r)=>o.month-r.month)}),m=s=>`RM ${s.toFixed(2)}`,L=s=>s>=100?"#ee0a24":s>=90?"#ff976a":s>=80?"#ffd21e":"#07c160",R=s=>({normal:"正常",warning:"警告",danger:"危险",exceeded:"超支"})[s]||"正常",N=s=>{console.log("查看月度详情:",s)},Y=(s,e,n="all")=>{const o=String(e).padStart(2,"0"),r=`${s}-${o}-01`,f=e===12?1:e+1,u=e===12?s+1:s,_=new Date(u,f-1,0).toISOString().split("T")[0];let a=0,h=0;y.budgets&&y.budgets.forEach(w=>{w.start_date<=_&&w.end_date>=r&&(n==="all"||w.category===n)&&(a+=w.amount||0,h+=w.used_amount||0)});const A=a-h,x=a>0?Math.round(h/a*100):0;let b="normal";return x>=100?b="exceeded":x>=90?b="danger":x>=80&&(b="warning"),{budget:a,used:h,remaining:A,usagePercentage:x,status:b}},O=()=>{window.scrollTo({top:0,behavior:"smooth"})},B=()=>{T.value=window.scrollY>300};return I(async()=>{await y.fetchBudgets(),await M.fetchTransactions(),window.addEventListener("scroll",B)}),q(()=>{window.removeEventListener("scroll",B)}),(s,e)=>{const n=g("van-nav-bar"),o=g("van-dropdown-item"),r=g("van-dropdown-menu"),f=g("van-progress"),u=g("van-icon"),_=g("van-back-top");return k(),D("div",Z,[c(n,{title:"年度预算详情","left-arrow":"",fixed:"",placeholder:"","safe-area-inset-top":"",onClickLeft:e[0]||(e[0]=a=>s.$router.back())}),t("div",tt,[t("div",et,[c(r,null,{default:G(()=>[c(o,{modelValue:i.value,"onUpdate:modelValue":e[1]||(e[1]=a=>i.value=a),options:$.value,title:V.value},null,8,["modelValue","options","title"]),c(o,{modelValue:d.value,"onUpdate:modelValue":e[2]||(e[2]=a=>d.value=a),options:F,title:P.value},null,8,["modelValue","title"])]),_:1})]),t("div",st,[t("div",at,[t("div",ot,[e[3]||(e[3]=t("div",{class:"stats-icon"},"📊",-1)),t("h3",null,l(E.value)+"年"+l(d.value==="all"?"":d.value)+"预算概览",1)])]),t("div",nt,[t("div",lt,[e[4]||(e[4]=t("div",{class:"stat-label"},"年度预算",-1)),t("div",it,l(m(v.value.totalBudget)),1)]),t("div",rt,[e[5]||(e[5]=t("div",{class:"stat-label"},"已使用",-1)),t("div",dt,l(m(v.value.totalUsed)),1)]),t("div",ut,[e[6]||(e[6]=t("div",{class:"stat-label"},"剩余",-1)),t("div",ct,l(m(v.value.totalRemaining)),1)]),t("div",vt,[e[7]||(e[7]=t("div",{class:"stat-label"},"使用率",-1)),t("div",mt,l(v.value.usagePercentage)+"%",1)])]),t("div",_t,[c(f,{percentage:Math.min(v.value.usagePercentage,100),color:L(v.value.usagePercentage),"show-pivot":!1,"stroke-width":"6"},null,8,["percentage","color"])])]),t("div",pt,[e[11]||(e[11]=t("div",{class:"details-header"},[t("div",{class:"details-title"},[t("div",{class:"details-icon"},"📋"),t("h3",null,"月度预算详情")])],-1)),t("div",gt,[(k(!0),D(j,null,H(U.value,a=>(k(),D("div",{key:a.month,class:C(["month-row",a.status]),onClick:h=>N(a)},[t("div",ht,[t("div",wt,l(a.name),1),t("div",xt,l(a.period),1)]),t("div",bt,[t("div",yt,[e[8]||(e[8]=t("span",{class:"amount-label"},"预算",-1)),t("span",St,l(m(a.budget)),1)]),t("div",Dt,[e[9]||(e[9]=t("span",{class:"amount-label"},"已用",-1)),t("span",kt,l(m(a.used)),1)]),t("div",Ct,[e[10]||(e[10]=t("span",{class:"amount-label"},"剩余",-1)),t("span",{class:C(["amount-value",{remaining:a.remaining>=0,"over-budget":a.remaining<0}])},l(m(a.remaining)),3)])]),t("div",Tt,[t("div",Yt,l(a.usagePercentage)+"%",1),t("div",{class:C(["status-indicator",a.status])},l(R(a.status)),3)]),c(u,{name:"arrow",class:"arrow-icon"})],10,ft))),128))])])]),z(c(_,{offset:100,teleport:!1,onClick:O},null,512),[[J,T.value]])])}}},Ut=X(Bt,[["__scopeId","data-v-5e371fe5"]]);export{Ut as default};
