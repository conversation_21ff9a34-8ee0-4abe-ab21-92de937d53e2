/*******************************************
 *          语言切换器样式
 *******************************************/
.language-switcher {
  margin-right: var(--spacing-md);
}

.language-select {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  background-color: var(--color-card);
  color: var(--color-text);
  font-size: var(--font-size-sm);
  cursor: pointer;
  outline: none;
  transition: border-color 0.3s;
}

.language-select:hover,
.language-select:focus {
  border-color: var(--color-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .language-switcher {
    margin-right: var(--spacing-xs);
  }
  
  .language-select {
    padding: var(--spacing-xs);
    font-size: var(--font-size-xs);
  }
}
