#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库路径配置模块
统一管理所有数据库文件的路径，避免相对路径问题
"""

import os

def get_project_root():
    """获取项目根目录的绝对路径"""
    # 从当前文件位置推导项目根目录
    current_file = os.path.abspath(__file__)  # config/database_config.py
    config_dir = os.path.dirname(current_file)  # config/
    project_root = os.path.dirname(config_dir)  # 项目根目录
    return project_root

def get_main_db_path():
    """获取主数据库文件路径"""
    project_root = get_project_root()
    return os.path.join(project_root, 'data', 'finance.db')

def get_bot_db_path():
    """获取Bot数据库文件路径"""
    project_root = get_project_root()
    return os.path.join(project_root, 'data', 'bot.db')

def ensure_data_directory():
    """确保data目录存在"""
    project_root = get_project_root()
    data_dir = os.path.join(project_root, 'data')
    os.makedirs(data_dir, exist_ok=True)
    return data_dir

# 导出常用路径
PROJECT_ROOT = get_project_root()
MAIN_DB_PATH = get_main_db_path()
BOT_DB_PATH = get_bot_db_path()

# 确保目录存在
ensure_data_directory()
