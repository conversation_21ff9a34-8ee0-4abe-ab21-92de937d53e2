# ========================================
# 个人财务管理系统 - 统一依赖文件
# 支持 Web版本 + Telegram机器人
# ========================================

# Flask Web框架
Flask==2.3.3
Flask-Cors==4.0.0
Flask-Session==0.5.0
Werkzeug==2.3.7
Jinja2==3.1.2
itsdangerous==2.1.2
click==8.1.7

# 数据库相关
SQLAlchemy==2.0.20

# 用户认证相关
bcrypt==4.0.1

# HTTP请求和API通信
requests==2.31.0

# 工具库
python-dotenv==1.0.0
pytz==2023.3
python-dateutil==2.8.2

# Telegram机器人相关
schedule==1.2.0
colorlog==6.7.0
jieba==0.42.1
