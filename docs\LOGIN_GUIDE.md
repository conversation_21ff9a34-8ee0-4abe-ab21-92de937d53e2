# 钱管家 - 用户登录功能使用指南

## 🎉 新功能介绍

钱管家现在支持多用户登录功能！每个用户都有独立的财务数据，确保数据安全和隐私。

## 🚀 快速开始

### 1. 启动应用

```bash
# 激活虚拟环境
.\venv\Scripts\activate

# 启动应用
python app.py
```

### 2. 访问登录页面

打开浏览器访问：http://localhost:8000

系统会自动跳转到登录页面：http://localhost:8000/login.html

### 3. 默认管理员账户

**首次使用时，系统已创建默认管理员账户：**

- **用户名**: `admin`
- **密码**: `admin123`
- **邮箱**: `<EMAIL>`

⚠️ **重要提醒**: 请登录后立即修改密码！

## 📝 功能说明

### 用户注册

1. 在登录页面点击"立即注册"
2. 填写以下信息：
   - 用户名（3-20位字符）
   - 邮箱地址
   - 显示名称（可选，默认使用用户名）
   - 密码（至少6位，包含字母和数字）
   - 确认密码

3. 点击"注册"按钮
4. 注册成功后自动跳转到登录页面

### 用户登录

1. 输入用户名或邮箱
2. 输入密码
3. 可选择"记住我"
4. 点击"登录"按钮
5. 登录成功后跳转到主页面

### 用户管理

登录后，点击右上角的用户头像可以：

- 查看个人资料
- 退出登录

## 🔒 数据隔离

每个用户的数据完全独立：

- **交易记录**: 只能查看和管理自己的交易
- **账户信息**: 只能访问自己创建的账户
- **预算管理**: 独立的预算设置和监控
- **财务目标**: 个人专属的目标管理

## 🛠️ 技术特性

### 安全特性

- **密码加密**: 使用 bcrypt 加密存储密码
- **会话管理**: 基于 Flask-Session 的安全会话
- **权限控制**: API级别的用户权限验证
- **数据隔离**: 数据库级别的用户数据隔离

### 用户体验

- **响应式设计**: 支持桌面和移动设备
- **实时验证**: 表单字段实时验证
- **密码强度检查**: 可视化密码强度指示器
- **友好提示**: 详细的错误和成功提示

## 📊 数据迁移

系统已自动完成数据迁移：

- 现有数据已关联到默认管理员账户
- 新用户注册后拥有独立的数据空间
- 原有功能完全保持兼容

## 🔧 开发者信息

### API 端点

- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/check` - 检查登录状态
- `GET /api/auth/profile` - 获取用户信息

### 数据库变更

所有主要数据表都添加了 `user_id` 字段：

- `transactions` - 交易记录
- `accounts` - 账户信息
- `budgets` - 预算管理
- `goals` - 财务目标

## 🐛 故障排除

### 常见问题

1. **无法登录**
   - 检查用户名和密码是否正确
   - 确认账户是否已激活

2. **注册失败**
   - 检查用户名和邮箱是否已被使用
   - 确认密码符合强度要求

3. **数据丢失**
   - 检查是否使用正确的用户账户登录
   - 数据按用户隔离，不同用户看到不同数据

### 重置默认账户

如果忘记默认管理员密码，可以重新运行迁移脚本：

```bash
python migrate_database.py
```

## 📞 技术支持

如有问题，请检查：

1. 应用日志文件：`logs/finance_app.log`
2. 数据库文件：`finance.db`
3. 会话文件：`sessions/` 目录

## 🎯 下一步计划

- [ ] 密码重置功能
- [ ] 邮箱验证
- [ ] 用户角色管理
- [ ] 数据导入/导出权限控制
- [ ] 用户活动日志

---

**享受您的个人财务管理之旅！** 💰✨
