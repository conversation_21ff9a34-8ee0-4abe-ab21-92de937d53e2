# 投资账号功能优化说明

## 更新内容

### 1. 账户管理页面优化

#### 新增投资账户分类
- 在总资产卡片中添加了"投资"分类
- 显示投资账户的总资产金额
- 点击投资分类可跳转到投资账号页面

#### 新增投资账号入口
- 在快速操作按钮中添加"投资账号"按钮
- 调整按钮布局以适应三个按钮的显示
- 优化按钮样式和间距

#### 计算投资资产
- 新增 `filteredInvestmentAssets` 计算属性
- 自动统计当前货币下的投资账户总资产

### 2. 投资账号页面 (`/investments`)

#### 页面功能
- **投资总资产卡片**：显示2025年投资总资产概览
- **投资统计**：显示总收益和总回报率
- **投资账户列表**：详细展示每个投资账户的数据

#### 投资账户详细信息
每个投资账户显示以下数据：
- **账户名字**：如 KDI、moomoo、EPF 等
- **初始余额**：账户开设时的初始金额
- **当前余额**：账户当前的总金额
- **入金数**：总投入金额
- **出金数**：总提取金额
- **收益**：投资盈亏金额（正数为盈利，负数为亏损）
- **回报率**：投资回报百分比

#### 货币筛选
- 支持按货币筛选投资账户
- 与账户管理页面保持一致的货币选择功能
- 独立的货币选择状态存储

### 3. 路由配置

#### 新增路由
```javascript
{
  path: '/investments',
  name: 'Investments',
  component: () => import('@/views/Investments/index.vue'),
  meta: {
    title: '投资账号',
    requiresAuth: true
  }
}
```

### 4. 样式优化

#### 投资主题色彩
- 使用绿色渐变主题 (`#059669` 到 `#047857`)
- 与投资理财的概念相符
- 保持与整体设计风格的一致性

#### 响应式设计
- 适配不同屏幕尺寸
- 优化移动端显示效果
- 保持良好的用户体验

### 5. 数据模拟

#### 临时数据生成
由于暂时不需要添加后端逻辑，页面使用模拟数据：
- **入金数**：随机生成 5000-15000 范围
- **出金数**：随机生成 0-3000 范围
- **收益**：随机生成 -500 到 +1500 范围
- **回报率**：随机生成 -5% 到 +15% 范围

#### 数据计算
- 自动计算投资总资产
- 自动计算总收益
- 自动计算平均回报率

### 6. 文件修改列表

#### 新增文件
- `mobile/src/views/Investments/index.vue` - 投资账号页面

#### 修改文件
- `mobile/src/views/Accounts/index.vue` - 账户管理页面
- `mobile/src/router/index.js` - 路由配置

### 7. 使用方法

1. 打开账户管理页面
2. 在总资产卡片中点击"投资"分类，或点击"投资账号"按钮
3. 进入投资账号页面查看详细信息
4. 可以通过右上角的货币选择器切换不同货币的投资账户

### 8. 未来扩展

#### 后端集成
- 添加投资账户数据的API接口
- 实现真实的入金、出金、收益数据
- 添加投资记录的增删改查功能

#### 功能增强
- 添加投资账户的详情页面
- 支持投资记录的时间线查看
- 添加投资收益的图表展示
- 支持投资目标设置和跟踪

## 技术实现

- **Vue 3 Composition API**：现代化的组件开发方式
- **Vant UI 组件库**：移动端优化的UI组件
- **响应式数据绑定**：实时更新投资数据
- **计算属性**：自动计算投资统计数据
- **本地存储**：保存用户的货币选择偏好
