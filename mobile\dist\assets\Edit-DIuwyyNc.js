import{a as d,r as de,l as C,m as me,h as pe,s as l,g as ve,c as V,e as o,w as r,f as c,d as i,o as D,i as h,t as B,n as q}from"./index--MNqwREY.js";import{u as fe}from"./transactions-F5TqZ8Qm.js";import{useAccountsStore as ye}from"./accounts-bk1VVrk3.js";import{t as ge}from"./transactions-CrdnwW_k.js";import{g as be}from"./categories-Zg0JKh5g.js";import{_ as Ce}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{s as A}from"./function-call-w7FI8rRn.js";const _e={class:"edit-transaction-page"},we={key:0,class:"loading-container"},ke={key:1,class:"page-container"},Ve={class:"type-selector"},De={class:"tab-content"},xe={class:"tab-content"},Se={class:"amount-section"},Ue={class:"amount-label"},$e={class:"amount-input-container"},Ye={class:"currency-symbol"},he={class:"action-buttons"},Me={key:2,class:"error-container"},Re={__name:"Edit",setup(Pe){const v=ve(),E=pe(),x=fe(),_=ye(),S=d(!0),U=d(!1),$=d(!1),f=d(!1),y=d(!1),g=d(!1),b=d(!1),u=d(null),t=de({type:"expense",amount:"",description:"",date:"",category:"",account:"",currency:"MYR",attachment:""}),w=d([]),G=new Date(2020,0,1),I=new Date(2030,11,31),z=[{text:"MYR - 马来西亚林吉特",value:"MYR"},{text:"USD - 美元",value:"USD"},{text:"CNY - 人民币",value:"CNY"},{text:"EUR - 欧元",value:"EUR"},{text:"GBP - 英镑",value:"GBP"},{text:"JPY - 日元",value:"JPY"},{text:"SGD - 新加坡元",value:"SGD"}],O=C(()=>{const a=_.accounts.find(m=>m.id===t.account),e=(a==null?void 0:a.name)||"";return be(t.type,e)}),J=C(()=>_.accounts.map(a=>({text:`${a.name} (${a.type})`,value:a.id}))),L=C(()=>z),M=C(()=>{const a=_.accounts.find(e=>e.id===t.account);return a?`${a.name} (${a.type})`:""}),j=C(()=>({MYR:"RM",USD:"$",CNY:"¥",EUR:"€",GBP:"£",JPY:"¥",SGD:"S$"})[t.currency]||t.currency);me(async()=>{await H()});const H=async()=>{S.value=!0;try{const a=E.params.id;await Promise.all([_.fetchAccounts(),K(a)])}catch(a){console.error("加载数据失败:",a),l("加载数据失败")}finally{S.value=!1}},K=async a=>{try{let e=x.transactions.find(s=>s.id==a);if(!e){const s=await ge.getTransaction(a);e=s.data||s}if(e){u.value=e,t.type=e.type,t.amount=e.amount.toString(),t.description=e.description,t.date=e.date,t.category=e.category,t.account=e.account,t.currency=e.currency||"MYR",t.attachment=e.attachment||"";const s=new Date(e.date);w.value=[s.getFullYear(),s.getMonth()+1,s.getDate()]}else l("交易不存在"),v.back()}catch(e){console.error("加载交易失败:",e),l("加载交易失败"),v.back()}},Q=a=>{const e=a.getFullYear(),s=String(a.getMonth()+1).padStart(2,"0"),m=String(a.getDate()).padStart(2,"0");return`${e}-${s}-${m}`},W=a=>{t.category=""},X=a=>{a&&parseFloat(a)<0&&(t.amount=Math.abs(parseFloat(a)).toString())},Z=()=>{f.value=!0},ee=()=>{const[a,e,s]=w.value,m=new Date(a,e-1,s);t.date=Q(m),f.value=!1},te=({selectedOptions:a})=>{var e;t.category=((e=a[0])==null?void 0:e.value)||"",y.value=!1},ae=({selectedOptions:a})=>{var e;t.account=((e=a[0])==null?void 0:e.value)||"",g.value=!1},ne=({selectedOptions:a})=>{var e;t.currency=((e=a[0])==null?void 0:e.value)||"MYR",b.value=!1},oe=()=>!t.amount||parseFloat(t.amount)<=0?(l("请输入有效金额"),!1):t.description.trim()?t.date?t.category?t.account?t.currency?!0:(l("请选择货币"),!1):(l("请选择账户"),!1):(l("请选择交易分类"),!1):(l("请选择交易日期"),!1):(l("请输入交易描述"),!1),R=()=>u.value?t.type!==u.value.type||parseFloat(t.amount)!==u.value.amount||t.description!==u.value.description||t.date!==u.value.date||t.category!==u.value.category||t.account!==u.value.account||t.currency!==(u.value.currency||"MYR")||t.attachment!==(u.value.attachment||""):!1,P=async()=>{if(oe()){if(!R()){l("没有任何修改");return}U.value=!0;try{const a={type:t.type,amount:parseFloat(t.amount),description:t.description.trim(),date:t.date,category:t.category,account:t.account,currency:t.currency,attachment:t.attachment.trim()};await x.updateTransaction(u.value.id,a),l({message:"交易更新成功",type:"success"}),v.back()}catch(a){console.error("更新交易失败:",a),l({message:a.message||"更新交易失败",type:"fail"})}finally{U.value=!1}}},se=async()=>{try{await A({title:"确认删除",message:"确定要删除这条交易记录吗？此操作不可撤销。"}),$.value=!0,await x.deleteTransaction(u.value.id),l({message:"交易删除成功",type:"success"}),v.replace("/transactions")}catch(a){a!=="cancel"&&(console.error("删除交易失败:",a),l({message:a.message||"删除交易失败",type:"fail"}))}finally{$.value=!1}},le=async()=>{if(R())try{await A({title:"确认离开",message:"您有未保存的修改，确定要离开吗？"}),v.back()}catch{}else v.back()};return(a,e)=>{const s=c("van-button"),m=c("van-nav-bar"),re=c("van-loading"),F=c("van-icon"),N=c("van-tab"),ue=c("van-tabs"),p=c("van-field"),T=c("van-cell-group"),ie=c("van-form"),ce=c("van-date-picker"),k=c("van-popup"),Y=c("van-picker");return D(),V("div",_e,[o(m,{title:"编辑交易","left-arrow":"",onClickLeft:le,fixed:"",placeholder:""},{right:r(()=>[o(s,{type:"primary",size:"small",loading:U.value,onClick:P},{default:r(()=>e[21]||(e[21]=[h(" 保存 ")])),_:1,__:[21]},8,["loading"])]),_:1}),S.value?(D(),V("div",we,[o(re,{size:"24px"}),e[22]||(e[22]=i("span",null,"加载中...",-1))])):u.value?(D(),V("div",ke,[i("div",Ve,[o(ue,{active:t.type,"onUpdate:active":e[0]||(e[0]=n=>t.type=n),onChange:W},{default:r(()=>[o(N,{title:"支出",name:"expense"},{default:r(()=>[i("div",De,[o(F,{name:"minus",class:"tab-icon expense"}),e[23]||(e[23]=i("span",null,"记录支出",-1))])]),_:1}),o(N,{title:"收入",name:"income"},{default:r(()=>[i("div",xe,[o(F,{name:"plus",class:"tab-icon income"}),e[24]||(e[24]=i("span",null,"记录收入",-1))])]),_:1})]),_:1},8,["active"])]),o(ie,{onSubmit:P,class:"transaction-form"},{default:r(()=>[i("div",Se,[i("div",Ue,B(t.type==="expense"?"支出金额":"收入金额"),1),i("div",$e,[i("span",Ye,B(j.value),1),o(p,{modelValue:t.amount,"onUpdate:modelValue":e[1]||(e[1]=n=>t.amount=n),type:"number",placeholder:"0.00",class:"amount-input",rules:[{required:!0,message:"请输入金额"}],onInput:X},null,8,["modelValue"])])]),o(T,{inset:"",title:"基本信息"},{default:r(()=>[o(p,{modelValue:t.description,"onUpdate:modelValue":e[2]||(e[2]=n=>t.description=n),label:"描述",placeholder:"请输入交易描述",rules:[{required:!0,message:"请输入交易描述"}],clearable:""},null,8,["modelValue"]),o(p,{modelValue:t.date,"onUpdate:modelValue":e[3]||(e[3]=n=>t.date=n),label:"日期",placeholder:"选择交易日期",readonly:"","is-link":"",onClick:Z,rules:[{required:!0,message:"请选择交易日期"}]},null,8,["modelValue"]),o(p,{modelValue:t.category,"onUpdate:modelValue":e[4]||(e[4]=n=>t.category=n),label:"分类",placeholder:"选择交易分类",readonly:"","is-link":"",onClick:e[5]||(e[5]=n=>y.value=!0),rules:[{required:!0,message:"请选择交易分类"}]},null,8,["modelValue"]),o(p,{modelValue:M.value,"onUpdate:modelValue":e[6]||(e[6]=n=>M.value=n),label:"账户",placeholder:"选择账户",readonly:"","is-link":"",onClick:e[7]||(e[7]=n=>g.value=!0),rules:[{required:!0,message:"请选择账户"}]},null,8,["modelValue"]),o(p,{modelValue:t.currency,"onUpdate:modelValue":e[8]||(e[8]=n=>t.currency=n),label:"货币",placeholder:"选择货币",readonly:"","is-link":"",onClick:e[9]||(e[9]=n=>b.value=!0),rules:[{required:!0,message:"请选择货币"}]},null,8,["modelValue"])]),_:1}),o(T,{inset:"",title:"附加信息"},{default:r(()=>[o(p,{modelValue:t.attachment,"onUpdate:modelValue":e[10]||(e[10]=n=>t.attachment=n),label:"备注",type:"textarea",placeholder:"添加备注信息（可选）",rows:"3",autosize:"",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1}),i("div",he,[o(s,{type:"danger",size:"large",plain:"",onClick:se,loading:$.value},{default:r(()=>e[25]||(e[25]=[h(" 删除交易 ")])),_:1,__:[25]},8,["loading"])])]),_:1})])):(D(),V("div",Me,[e[27]||(e[27]=i("div",{class:"error-icon"},"❌",-1)),e[28]||(e[28]=i("p",null,"交易不存在或已被删除",-1)),o(s,{type:"primary",onClick:e[11]||(e[11]=n=>a.$router.back())},{default:r(()=>e[26]||(e[26]=[h(" 返回 ")])),_:1,__:[26]})])),o(k,{show:f.value,"onUpdate:show":e[14]||(e[14]=n=>f.value=n),position:"bottom",round:""},{default:r(()=>[o(ce,{modelValue:w.value,"onUpdate:modelValue":e[12]||(e[12]=n=>w.value=n),title:"选择日期","min-date":q(G),"max-date":q(I),onConfirm:ee,onCancel:e[13]||(e[13]=n=>f.value=!1)},null,8,["modelValue","min-date","max-date"])]),_:1},8,["show"]),o(k,{show:y.value,"onUpdate:show":e[16]||(e[16]=n=>y.value=n),position:"bottom"},{default:r(()=>[o(Y,{columns:O.value,title:"选择分类",onConfirm:te,onCancel:e[15]||(e[15]=n=>y.value=!1)},null,8,["columns"])]),_:1},8,["show"]),o(k,{show:g.value,"onUpdate:show":e[18]||(e[18]=n=>g.value=n),position:"bottom"},{default:r(()=>[o(Y,{columns:J.value,title:"选择账户",onConfirm:ae,onCancel:e[17]||(e[17]=n=>g.value=!1)},null,8,["columns"])]),_:1},8,["show"]),o(k,{show:b.value,"onUpdate:show":e[20]||(e[20]=n=>b.value=n),position:"bottom"},{default:r(()=>[o(Y,{columns:L.value,title:"选择货币",onConfirm:ne,onCancel:e[19]||(e[19]=n=>b.value=!1)},null,8,["columns"])]),_:1},8,["show"])])}}},Ge=Ce(Re,[["__scopeId","data-v-8fecdd71"]]);export{Ge as default};
