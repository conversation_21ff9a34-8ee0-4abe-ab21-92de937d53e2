/*******************************************
 *          附件相关样式
 *******************************************/

/* 附件预览区域 */
.attachment-preview {
  margin-top: var(--spacing-sm);
  min-height: 60px;
  border: 1px dashed var(--color-border);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-sm);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 附件缩略图 */
.attachment-thumbnail {
  max-width: 100%;
  max-height: 100px;
  object-fit: contain;
}

/* 文件信息 */
.file-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--color-text-light);
  font-size: var(--font-size-sm);
}

.file-icon {
  font-size: var(--font-size-lg);
}

/* 附件链接 */
.attachment-link {
  cursor: pointer;
  color: var(--color-primary);
  text-decoration: none;
  font-size: var(--font-size-lg);
}

.attachment-link:hover {
  color: var(--color-primary-dark);
}

/* 模态框 */
.modal {
  display: flex;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  align-items: center;
  justify-content: center;
}

.modal-content {
  background-color: var(--color-card-bg);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
  max-width: 80%;
  max-height: 80%;
  overflow: auto;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

/* 关闭按钮 */
.close-btn {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  font-size: 24px;
  font-weight: bold;
  color: var(--color-text-light);
  cursor: pointer;
}

.close-btn:hover {
  color: var(--color-primary);
}

/* 附件图片 */
.attachment-image {
  max-width: 100%;
  max-height: 70vh;
  display: block;
  margin: 0 auto;
}

/* 下载链接 */
.download-link {
  display: inline-block;
  margin-top: var(--spacing-md);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--color-primary);
  color: white;
  text-decoration: none;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  transition: background-color 0.3s;
}

.download-link:hover {
  background-color: var(--color-primary-dark);
}
