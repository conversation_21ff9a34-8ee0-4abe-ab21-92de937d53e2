/*******************************************
 *          净资产分配样式
 *******************************************/

.net-asset-allocation {
  background-color: var(--color-card-bg);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

.net-asset-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  flex-wrap: wrap;
}

.net-asset-header h3 {
  margin: 0;
  font-size: var(--font-size-md);
  color: var(--color-text);
}

.net-asset-summary {
  display: flex;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.net-asset-total,
.net-asset-allocated,
.net-asset-remaining {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.net-asset-total span:first-child,
.net-asset-allocated span:first-child,
.net-asset-remaining span:first-child {
  color: var(--color-text-light);
  font-size: var(--font-size-sm);
}

.net-asset-total span:last-child {
  font-weight: 600;
  color: var(--color-primary);
  font-size: var(--font-size-md);
}

.net-asset-allocated span:last-child {
  font-weight: 600;
  color: var(--color-warning);
  font-size: var(--font-size-md);
}

.net-asset-remaining span:last-child {
  font-weight: 600;
  color: var(--color-success);
  font-size: var(--font-size-md);
}

.net-asset-input {
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--color-border);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .net-asset-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .net-asset-summary {
    margin-top: var(--spacing-sm);
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}
