# Telegram机器人用户绑定功能

## 功能概述

新增的用户绑定功能允许Telegram机器人用户与网站用户账户进行关联，实现数据隔离和权限管理。

## 主要变更

### 1. 数据库结构更新

**机器人数据库 (`bot/bot.db`)**
- `bot_users` 表新增字段：
  - `website_user_id`: 绑定的网站用户ID
  - `website_username`: 绑定的网站用户名
  - `is_bound`: 绑定状态标志
- 移除字段：
  - `is_admin`: 不再使用环境变量管理员权限

### 2. 新增功能模块

**用户绑定验证 (`bot/website_auth.py`)**
- 验证网站用户名是否存在
- 获取用户详细信息
- 检查管理员权限（基于网站用户系统）

**数据库迁移 (`bot/migrate_bot_database.py`)**
- 自动更新现有数据库结构
- 安全地添加新字段
- 移除废弃的管理员字段

### 3. 机器人命令更新

**新增命令**
- `/bind 用户名` - 绑定网站账户
- `/unbind` - 解绑网站账户
- `/status` - 查看绑定状态

**更新的命令**
- `/start` - 根据绑定状态显示不同欢迎信息
- `/help` - 包含绑定相关帮助信息

### 4. 权限管理变更

**移除的配置**
- `TELEGRAM_ADMIN_CHAT_IDS` 环境变量
- 基于chat_id的管理员权限

**新的权限系统**
- 基于网站用户系统的权限管理
- 第一个注册的网站用户自动成为管理员
- 可扩展的权限检查机制

## 使用流程

### 首次使用

1. 用户启动机器人：`/start`
2. 机器人提示需要绑定账户
3. 用户执行绑定：`/bind 用户名`
4. 系统验证用户名并完成绑定
5. 用户可以使用所有功能

### 绑定管理

```bash
# 绑定账户
/bind admin

# 查看绑定状态
/status

# 解绑账户
/unbind

# 重新绑定
/bind new_username
```

## 数据隔离

### 交易数据
- 所有交易记录按 `user_id` 隔离
- 用户只能查看自己的数据
- 账户、预算、交易历史完全隔离

### 查询功能
- 余额查询：只显示用户自己的账户
- 预算查询：只显示用户自己的预算
- 交易记录：只显示用户自己的交易

## 安全特性

### 绑定限制
- 每个网站用户只能绑定一个Telegram账户
- 每个Telegram用户只能绑定一个网站账户
- 绑定前验证网站用户是否存在且激活

### 数据保护
- 未绑定用户无法访问任何财务数据
- 绑定信息加密存储
- 自动检查绑定状态有效性

## 部署和迁移

### 自动迁移
```bash
# 启动时自动执行迁移
python start_bot_with_migration.py
```

### 手动迁移
```bash
# 仅执行数据库迁移
python bot/migrate_bot_database.py
```

### 测试绑定功能
```bash
# 运行绑定功能测试
python test_bot_binding.py
```

## 兼容性

### 向后兼容
- 现有机器人用户数据保持不变
- 现有功能正常工作（需要先绑定）
- 数据库结构平滑升级

### 环境变量清理
可以安全移除以下环境变量：
- `TELEGRAM_ADMIN_CHAT_IDS`

## 故障排除

### 常见问题

**1. 绑定失败**
- 检查网站用户名是否正确
- 确认网站用户是否激活
- 检查是否已被其他用户绑定

**2. 数据查询为空**
- 确认用户已正确绑定
- 检查网站是否有对应用户的数据
- 验证数据库连接正常

**3. 权限问题**
- 管理员权限基于网站用户系统
- 第一个注册的用户自动为管理员
- 可在网站端管理用户权限

### 日志检查
```bash
# 查看机器人日志
tail -f logs/bot.log

# 查看绑定相关日志
grep "绑定\|bind" logs/bot.log
```

## 开发说明

### 扩展绑定验证
可以在 `bot/website_auth.py` 中添加更多验证逻辑：
- 密码验证
- 二次验证
- 权限级别检查

### 自定义权限系统
可以修改 `is_admin_user` 方法实现自定义权限逻辑：
- 基于用户组
- 基于角色
- 基于特定字段

## 总结

用户绑定功能实现了：
✅ 完整的用户数据隔离
✅ 安全的权限管理
✅ 简单的绑定流程
✅ 向后兼容性
✅ 自动数据库迁移

移除了：
❌ 环境变量管理员配置
❌ 基于chat_id的权限系统
❌ 数据混合访问风险
