<template>
  <div class="login-page">
    <!-- 专业背景 -->
    <div class="professional-bg">
      <div class="grid-pattern"></div>
      <div class="accent-line line-1"></div>
      <div class="accent-line line-2"></div>
      <div class="accent-line line-3"></div>
    </div>

    <!-- 主容器 -->
    <div class="main-container">
      <!-- 顶部品牌区 -->
      <div class="brand-section">
        <div class="brand-container">
          <div class="logo-professional">
            <div class="logo-frame">
              <div class="logo-inner">💰</div>
            </div>
          </div>
          <div class="brand-info">
            <h1 class="brand-title">钱管家</h1>
            <p class="brand-subtitle">专业财务管理平台</p>
            <div class="brand-line"></div>
          </div>
        </div>
      </div>

      <!-- 登录面板 -->
      <div class="login-panel">
        <div class="panel-container">
          <div class="panel-header">
            <h2 class="panel-title">账户登录</h2>
            <p class="panel-subtitle">请输入您的凭据以访问系统</p>
          </div>

          <van-form @submit="handleLogin" class="professional-form">
            <div class="form-group">
              <label class="form-label">
                <van-icon name="user-o" class="label-icon" />
                用户名 / 邮箱
              </label>
              <van-field
                v-model="form.username"
                name="username"
                placeholder="请输入用户名或邮箱地址"
                :rules="[{ required: true, message: '请输入用户名或邮箱' }]"
                clearable
                class="professional-field"
              />
            </div>

            <div class="form-group">
              <label class="form-label">
                <van-icon name="lock" class="label-icon" />
                密码
              </label>
              <van-field
                v-model="form.password"
                type="password"
                name="password"
                placeholder="请输入您的密码"
                :rules="[{ required: true, message: '请输入密码' }]"
                clearable
                class="professional-field"
              />
            </div>

            <div class="form-controls">
              <div class="control-left">
                <van-checkbox v-model="form.rememberMe" class="professional-checkbox">
                  <span class="checkbox-text">保持登录状态</span>
                </van-checkbox>
              </div>
              <div class="control-right">
                <van-button type="default" size="mini" plain class="forgot-password">
                  忘记密码？
                </van-button>
              </div>
            </div>

            <van-button
              block
              type="primary"
              native-type="submit"
              :loading="loading"
              loading-text="验证中..."
              size="large"
              class="login-submit"
            >
              <span class="submit-text">
                <van-icon name="arrow" class="submit-icon" />
                登录系统
              </span>
            </van-button>
          </van-form>

          <!-- 分割区域 -->
          <div class="section-divider">
            <div class="divider-content">
              <div class="divider-line-left"></div>
              <span class="divider-text">或使用以下方式</span>
              <div class="divider-line-right"></div>
            </div>
          </div>

          <!-- 快速访问 -->
          <div class="quick-access">
            <div class="access-grid">
              <div class="access-item">
                <div class="access-icon wechat-pro">
                  <van-icon name="wechat" />
                </div>
                <span class="access-label">微信登录</span>
              </div>
              <div class="access-item">
                <div class="access-icon sms-pro">
                  <van-icon name="phone-o" />
                </div>
                <span class="access-label">短信验证</span>
              </div>
              <div class="access-item">
                <div class="access-icon scan-pro">
                  <van-icon name="scan" />
                </div>
                <span class="access-label">扫码登录</span>
              </div>
            </div>
          </div>

          <!-- 注册区域 -->
          <div class="register-area">
            <div class="register-content">
              <span class="register-prompt">没有账户？</span>
              <van-button
                type="primary"
                size="small"
                plain
                @click="$router.push('/register')"
                class="register-action"
              >
                申请账户
              </van-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部信息 -->
      <div class="footer-section">
        <div class="footer-content">
          <div class="security-badge">
            <van-icon name="shield-o" />
            <span>安全登录</span>
          </div>
          <div class="version-display">
            <span>版本 1.0.0</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { showToast } from 'vant'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 表单数据
const form = reactive({
  username: '',
  password: '',
  rememberMe: false
})

// 加载状态
const loading = ref(false)

// 处理登录
const handleLogin = async () => {
  if (!form.username || !form.password) {
    showToast('请填写完整的登录信息')
    return
  }

  loading.value = true
  
  try {
    await authStore.login({
      username: form.username,
      password: form.password
    })

    showToast({
      message: '登录成功',
      type: 'success'
    })

    // 登录成功后跳转
    const redirect = route.query.redirect || '/'
    router.replace(redirect)
    
  } catch (error) {
    console.error('登录失败:', error)
    showToast({
      message: error.message || '登录失败，请检查用户名和密码',
      type: 'fail'
    })
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
  position: relative;
  overflow: hidden;
}

.professional-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.grid-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(52, 152, 219, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(52, 152, 219, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: grid-move 20s linear infinite;
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

.accent-line {
  position: absolute;
  background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.3), transparent);
  height: 2px;
}

.line-1 {
  top: 20%;
  left: 0;
  right: 0;
  animation: line-slide 8s ease-in-out infinite;
}

.line-2 {
  top: 50%;
  left: 0;
  right: 0;
  animation: line-slide 8s ease-in-out infinite 2s;
}

.line-3 {
  top: 80%;
  left: 0;
  right: 0;
  animation: line-slide 8s ease-in-out infinite 4s;
}

@keyframes line-slide {
  0%, 100% { opacity: 0; transform: translateX(-100%); }
  50% { opacity: 1; transform: translateX(0); }
}

.main-container {
  position: relative;
  z-index: 2;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 24px 20px;
}

.brand-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 180px;
}

.brand-container {
  text-align: center;
}

.logo-professional {
  margin-bottom: 24px;
}

.logo-frame {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  position: relative;
  box-shadow: 0 8px 32px rgba(52, 152, 219, 0.3);
}

.logo-frame::before {
  content: '';
  position: absolute;
  inset: 3px;
  background: #2c3e50;
  border-radius: 13px;
}

.logo-inner {
  font-size: 36px;
  position: relative;
  z-index: 1;
}

.brand-info {
  color: white;
}

.brand-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
  letter-spacing: 1px;
}

.brand-subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 16px 0;
  font-weight: 400;
}

.brand-line {
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  margin: 0 auto;
  border-radius: 2px;
}

.login-panel {
  flex: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.panel-container {
  width: 100%;
  max-width: 400px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 32px 28px;
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.panel-header {
  text-align: center;
  margin-bottom: 32px;
}

.panel-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.panel-subtitle {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  color: #2c3e50;
  font-size: 14px;
  font-weight: 600;
}

.label-icon {
  margin-right: 8px;
  color: #3498db;
  font-size: 16px;
}

.form-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 28px 0;
}

.checkbox-text {
  color: #2c3e50;
  font-size: 14px;
  margin-left: 8px;
}

.forgot-password {
  color: #3498db;
  border: none;
  font-size: 14px;
  font-weight: 500;
}

.login-submit {
  height: 52px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  border: none;
  border-radius: 12px;
  margin: 28px 0;
  font-size: 16px;
  font-weight: 600;
  box-shadow: 0 8px 24px rgba(52, 152, 219, 0.3);
}

.submit-text {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: white;
}

.submit-icon {
  font-size: 14px;
}

.section-divider {
  margin: 28px 0;
}

.divider-content {
  display: flex;
  align-items: center;
}

.divider-line-left,
.divider-line-right {
  flex: 1;
  height: 1px;
  background: linear-gradient(to right, transparent, #bdc3c7, transparent);
}

.divider-text {
  color: #7f8c8d;
  font-size: 12px;
  margin: 0 16px;
  white-space: nowrap;
}

.quick-access {
  margin: 24px 0;
}

.access-grid {
  display: flex;
  justify-content: space-around;
}

.access-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: transform 0.2s;
}

.access-item:active {
  transform: scale(0.95);
}

.access-icon {
  width: 44px;
  height: 44px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
}

.wechat-pro {
  background: linear-gradient(135deg, #07c160, #00a854);
}

.sms-pro {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.scan-pro {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.access-label {
  color: #7f8c8d;
  font-size: 12px;
  font-weight: 500;
}

.register-area {
  margin-top: 28px;
  text-align: center;
}

.register-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.register-prompt {
  color: #7f8c8d;
  font-size: 14px;
}

.register-action {
  color: #3498db;
  border-color: #3498db;
  font-size: 14px;
  font-weight: 500;
}

.footer-section {
  padding: 20px 0;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.security-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

.version-display {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
}

/* 自定义字段样式 */
:deep(.professional-field) {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  transition: all 0.3s ease;
}

:deep(.professional-field:focus-within) {
  border-color: #3498db;
  background: white;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

:deep(.professional-field .van-field__control) {
  font-size: 15px;
  color: #2c3e50;
}

:deep(.professional-checkbox .van-checkbox__icon) {
  border-color: #3498db;
  border-radius: 4px;
}

:deep(.professional-checkbox .van-checkbox__icon--checked) {
  background: linear-gradient(135deg, #3498db, #2980b9);
  border-color: transparent;
}
</style>
