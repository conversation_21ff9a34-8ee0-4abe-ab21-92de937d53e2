import { defineStore } from 'pinia'
import { accountsAPI } from '@/api/accounts'

export const useAccountsStore = defineStore('accounts', {
  state: () => ({
    accounts: [],
    loading: false
  }),

  getters: {
    totalAssets: (state) => {
      return state.accounts.reduce((total, account) => {
        return total + (account.current_balance || account.initial_balance || 0)
      }, 0)
    },

    accountsByType: (state) => {
      const grouped = {}
      state.accounts.forEach(account => {
        if (!grouped[account.type]) {
          grouped[account.type] = []
        }
        grouped[account.type].push(account)
      })
      return grouped
    },

    cashAssets: (state) => {
      return state.accounts
        .filter(account => account.type === 'cash')
        .reduce((total, account) => total + (account.current_balance || account.initial_balance || 0), 0)
    },

    bankAssets: (state) => {
      return state.accounts
        .filter(account => account.type === 'bank')
        .reduce((total, account) => total + (account.current_balance || account.initial_balance || 0), 0)
    }
  },

  actions: {
    async fetchAccounts() {
      this.loading = true
      try {
        const accounts = await accountsAPI.getAccounts()
        this.accounts = Array.isArray(accounts) ? accounts : []
        return this.accounts
      } catch (error) {
        console.error('获取账户列表失败:', error)
        this.accounts = []
        throw error
      } finally {
        this.loading = false
      }
    },

    async fetchAccountsWithBalances() {
      this.loading = true
      try {
        const response = await accountsAPI.getAllBalances()
        this.accounts = Array.isArray(response.accounts) ? response.accounts : []
        return this.accounts
      } catch (error) {
        console.error('获取账户余额失败:', error)
        this.accounts = []
        throw error
      } finally {
        this.loading = false
      }
    },

    async createAccount(data) {
      try {
        const response = await accountsAPI.createAccount(data)

        // 创建成功后重新获取账户列表
        if (response && response.success) {
          await this.fetchAccountsWithBalances()
        }

        return response
      } catch (error) {
        console.error('创建账户失败:', error)
        throw error
      }
    },

    async updateAccount(id, data) {
      try {
        const response = await accountsAPI.updateAccount(id, data)

        // 更新成功后重新获取账户列表
        if (response && response.success) {
          await this.fetchAccountsWithBalances()
        }

        return response
      } catch (error) {
        console.error('更新账户失败:', error)
        throw error
      }
    },

    async deleteAccount(id) {
      try {
        const response = await accountsAPI.deleteAccount(id)

        // 删除成功后重新获取账户列表
        if (response && response.success) {
          await this.fetchAccountsWithBalances()
        }

        return response
      } catch (error) {
        console.error('删除账户失败:', error)
        throw error
      }
    }
  }
})
