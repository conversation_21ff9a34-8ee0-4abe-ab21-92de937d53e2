import api from './index'

export const budgetsAPI = {
  // 获取预算列表
  getBudgets() {
    return api.get('/budgets/')
  },

  // 获取单个预算
  getBudget(id) {
    return api.get(`/budgets/${id}`)
  },

  // 创建预算
  createBudget(data) {
    return api.post('/budgets/', data)
  },

  // 更新预算
  updateBudget(id, data) {
    return api.put(`/budgets/${id}`, data)
  },

  // 删除预算
  deleteBudget(id) {
    return api.delete(`/budgets/${id}`)
  },

  // 获取预算使用情况统计
  getBudgetStats(params = {}) {
    return api.get('/budgets/stats', { params })
  },

  // 检查预算状态
  checkBudgetStatus(category) {
    return api.get(`/budgets/check/${category}`)
  }
}
