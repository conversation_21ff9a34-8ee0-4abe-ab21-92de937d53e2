/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 16px 24px;
    border-radius: 12px;
    color: white;
    font-weight: 500;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.12),
        0 4px 16px rgba(0, 0, 0, 0.08);
    z-index: 1000;
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    max-width: 380px;
    min-width: 280px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 14px;
    line-height: 1.4;
}

.notification.show {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.notification.info {
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-left: 4px solid #2980b9;
}

.notification.success {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    border-left: 4px solid #27ae60;
}

.notification.warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    border-left: 4px solid #e67e22;
}

.notification.error {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    border-left: 4px solid #c0392b;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateY(0) translateX(0) scale(1); }
    25% { transform: translateY(0) translateX(-2px) scale(1); }
    75% { transform: translateY(0) translateX(2px) scale(1); }
}

/* 添加图标支持 */
.notification::before {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-right: 12px;
    vertical-align: middle;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    filter: brightness(0) invert(1);
}

.notification.info::before {
    content: 'ℹ️';
    filter: none;
}

.notification.success::before {
    content: '✅';
    filter: none;
}

.notification.warning::before {
    content: '⚠️';
    filter: none;
}

.notification.error::before {
    content: '❌';
    filter: none;
}

/* 通知栏计数标记 */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 20px;
}

/* 预算弹窗样式 */
.budget-alert-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.budget-alert-overlay.show {
    opacity: 1;
}

.budget-alert {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.budget-alert-overlay.show .budget-alert {
    transform: scale(1);
}

.budget-alert-header {
    display: flex;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.budget-alert.warning .budget-alert-header {
    background-color: #fff3cd;
    border-bottom-color: #ffeaa7;
}

.budget-alert.danger .budget-alert-header {
    background-color: #f8d7da;
    border-bottom-color: #f5c6cb;
}

.budget-alert-icon {
    font-size: 24px;
    margin-right: 15px;
}

.budget-alert-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.budget-alert.warning .budget-alert-title {
    color: #856404;
}

.budget-alert.danger .budget-alert-title {
    color: #721c24;
}

.budget-alert-content {
    padding: 20px;
}

.budget-alert-message {
    margin: 0 0 20px 0;
    font-size: 16px;
    line-height: 1.5;
    color: #333;
}

.budget-alert-details {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.budget-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.budget-detail:last-child {
    margin-bottom: 0;
}

.budget-detail .label {
    font-weight: 500;
    color: #666;
}

.budget-detail .value {
    font-weight: 600;
    color: #333;
}

.budget-detail .value.warning {
    color: #f39c12;
}

.budget-detail .value.danger {
    color: #e74c3c;
}

.budget-alert-actions {
    display: flex;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid #eee;
    justify-content: flex-end;
}

.budget-alert-actions .btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.budget-alert-actions .btn-secondary {
    background-color: #6c757d;
    color: white;
}

.budget-alert-actions .btn-secondary:hover {
    background-color: #5a6268;
}

.budget-alert-actions .btn-primary {
    background-color: #007bff;
    color: white;
}

.budget-alert-actions .btn-primary:hover {
    background-color: #0056b3;
}

/* 通知历史弹窗样式 */
.notification-history-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.notification-history-overlay.show {
    opacity: 1;
}

.notification-history-box {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.notification-history-overlay.show .notification-history-box {
    transform: scale(1);
}

.notification-history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.notification-history-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.close-btn:hover {
    background-color: #f0f0f0;
    color: #333;
}

.notification-history-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.notification-item {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    position: relative;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    background-color: #f0f8ff;
    border-left: 4px solid #007bff;
}

.notification-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.notification-title {
    font-weight: 600;
    color: #333;
}

.notification-date {
    font-size: 12px;
    color: #999;
}

.notification-item-content {
    color: #666;
    line-height: 1.4;
    white-space: pre-wrap;
}

.unread-indicator {
    position: absolute;
    top: 50%;
    right: 15px;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background-color: #007bff;
    border-radius: 50%;
}

.notification-history-footer {
    display: flex;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid #eee;
    justify-content: flex-end;
}

.notification-history-footer .btn {
    padding: 8px 16px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.notification-history-footer .btn-secondary {
    background-color: #6c757d;
    color: white;
}

.notification-history-footer .btn-secondary:hover {
    background-color: #5a6268;
}

.notification-history-footer .btn-danger {
    background-color: #dc3545;
    color: white;
}

.notification-history-footer .btn-danger:hover {
    background-color: #c82333;
}

.loading, .error, .empty {
    text-align: center;
    padding: 40px 20px;
    color: #999;
    font-style: italic;
}

.error {
    color: #e74c3c;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .budget-alert,
    .notification-history-box {
        width: 95%;
        max-height: 90vh;
    }

    .budget-alert-actions,
    .notification-history-footer {
        flex-direction: column;
    }

    .budget-alert-actions .btn,
    .notification-history-footer .btn {
        width: 100%;
    }
}
