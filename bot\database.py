#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Bot专属数据库管理
"""

import sqlite3
import os
import sys
import logging
from typing import List, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.database_config import BOT_DB_PATH

logger = logging.getLogger(__name__)

class BotDatabase:
    def __init__(self, db_path: str = None):
        """初始化Bot数据库"""
        if db_path is None:
            db_path = BOT_DB_PATH

        self.db_path = db_path
        # 确保目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        self.init_database()

    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn

    def init_database(self):
        """初始化数据库表"""
        conn = self.get_connection()
        try:
            # 创建用户表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS bot_users (
                    chat_id INTEGER PRIMARY KEY,
                    username TEXT,
                    first_name TEXT,
                    last_name TEXT,
                    website_user_id TEXT,
                    website_username TEXT,
                    is_bound BOOLEAN DEFAULT FALSE,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建消息日志表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS message_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    chat_id INTEGER,
                    message_type TEXT,
                    message_content TEXT,
                    direction TEXT, -- 'incoming' or 'outgoing'
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (chat_id) REFERENCES bot_users (chat_id)
                )
            ''')

            # 创建通知设置表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS notification_settings (
                    chat_id INTEGER PRIMARY KEY,
                    budget_notifications BOOLEAN DEFAULT TRUE,
                    transaction_notifications BOOLEAN DEFAULT FALSE,
                    daily_summary BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (chat_id) REFERENCES bot_users (chat_id)
                )
            ''')

            conn.commit()
            logger.info("Bot数据库初始化完成")

        except Exception as e:
            logger.error(f"初始化Bot数据库失败: {e}")
            conn.rollback()
        finally:
            conn.close()

    def add_user(self, chat_id: int, username: str = None, first_name: str = None,
                 last_name: str = None) -> bool:
        """添加或更新用户"""
        conn = self.get_connection()
        try:
            # 检查用户是否已存在
            existing_user = conn.execute(
                'SELECT chat_id FROM bot_users WHERE chat_id = ?',
                (chat_id,)
            ).fetchone()

            if existing_user:
                # 更新现有用户
                conn.execute('''
                    UPDATE bot_users
                    SET username = ?, first_name = ?, last_name = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE chat_id = ?
                ''', (username, first_name, last_name, chat_id))
                logger.info(f"更新用户: {chat_id}")
            else:
                # 添加新用户
                conn.execute('''
                    INSERT INTO bot_users (chat_id, username, first_name, last_name)
                    VALUES (?, ?, ?, ?)
                ''', (chat_id, username, first_name, last_name))

                # 为新用户创建默认通知设置
                conn.execute('''
                    INSERT INTO notification_settings (chat_id)
                    VALUES (?)
                ''', (chat_id,))

                logger.info(f"添加新用户: {chat_id}")

            conn.commit()
            return True

        except Exception as e:
            logger.error(f"添加/更新用户失败: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def get_all_users(self) -> List[dict]:
        """获取所有用户"""
        conn = self.get_connection()
        try:
            users = conn.execute(
                'SELECT * FROM bot_users WHERE is_active = TRUE ORDER BY created_at'
            ).fetchall()
            return [dict(user) for user in users]
        except Exception as e:
            logger.error(f"获取用户列表失败: {e}")
            return []
        finally:
            conn.close()

    def get_admin_users(self) -> List[dict]:
        """获取所有管理员用户"""
        conn = self.get_connection()
        try:
            admins = conn.execute(
                'SELECT * FROM bot_users WHERE is_admin = TRUE AND is_active = TRUE'
            ).fetchall()
            return [dict(admin) for admin in admins]
        except Exception as e:
            logger.error(f"获取管理员列表失败: {e}")
            return []
        finally:
            conn.close()

    def set_admin(self, chat_id: int, is_admin: bool = True) -> bool:
        """设置用户管理员权限"""
        conn = self.get_connection()
        try:
            conn.execute(
                'UPDATE bot_users SET is_admin = ?, updated_at = CURRENT_TIMESTAMP WHERE chat_id = ?',
                (is_admin, chat_id)
            )
            conn.commit()
            logger.info(f"设置用户 {chat_id} 管理员权限: {is_admin}")
            return True
        except Exception as e:
            logger.error(f"设置管理员权限失败: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def log_message(self, chat_id: int, message_type: str, content: str, direction: str):
        """记录消息日志"""
        conn = self.get_connection()
        try:
            conn.execute('''
                INSERT INTO message_logs (chat_id, message_type, message_content, direction)
                VALUES (?, ?, ?, ?)
            ''', (chat_id, message_type, content, direction))
            conn.commit()
        except Exception as e:
            logger.error(f"记录消息日志失败: {e}")
        finally:
            conn.close()

    def get_notification_settings(self, chat_id: int) -> Optional[dict]:
        """获取用户通知设置"""
        conn = self.get_connection()
        try:
            settings = conn.execute(
                'SELECT * FROM notification_settings WHERE chat_id = ?',
                (chat_id,)
            ).fetchone()
            return dict(settings) if settings else None
        except Exception as e:
            logger.error(f"获取通知设置失败: {e}")
            return None
        finally:
            conn.close()

    def bind_website_user(self, chat_id: int, website_user_id: str, website_username: str) -> bool:
        """绑定网站用户"""
        conn = self.get_connection()
        try:
            # 检查是否已经绑定其他用户
            existing_binding = conn.execute(
                'SELECT chat_id FROM bot_users WHERE website_user_id = ? AND chat_id != ?',
                (website_user_id, chat_id)
            ).fetchone()

            if existing_binding:
                logger.warning(f"网站用户 {website_username} 已绑定到其他Telegram用户")
                return False

            # 更新绑定信息
            conn.execute('''
                UPDATE bot_users
                SET website_user_id = ?, website_username = ?, is_bound = 1,
                    updated_at = CURRENT_TIMESTAMP
                WHERE chat_id = ?
            ''', (website_user_id, website_username, chat_id))

            conn.commit()
            logger.info(f"用户 {chat_id} 成功绑定网站用户: {website_username}")
            return True

        except Exception as e:
            logger.error(f"绑定网站用户失败: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def unbind_website_user(self, chat_id: int) -> bool:
        """解绑网站用户"""
        conn = self.get_connection()
        try:
            conn.execute('''
                UPDATE bot_users
                SET website_user_id = NULL, website_username = NULL, is_bound = 0,
                    updated_at = CURRENT_TIMESTAMP
                WHERE chat_id = ?
            ''', (chat_id,))

            conn.commit()
            logger.info(f"用户 {chat_id} 已解绑网站用户")
            return True

        except Exception as e:
            logger.error(f"解绑网站用户失败: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def get_user_binding(self, chat_id: int) -> Optional[dict]:
        """获取用户绑定信息"""
        conn = self.get_connection()
        try:
            user = conn.execute(
                'SELECT website_user_id, website_username, is_bound FROM bot_users WHERE chat_id = ?',
                (chat_id,)
            ).fetchone()
            return dict(user) if user else None
        except Exception as e:
            logger.error(f"获取用户绑定信息失败: {e}")
            return None
        finally:
            conn.close()

    def is_user_bound(self, chat_id: int) -> bool:
        """检查用户是否已绑定"""
        binding = self.get_user_binding(chat_id)
        return binding and binding.get('is_bound', False)

# 全局数据库实例
bot_db = BotDatabase()
