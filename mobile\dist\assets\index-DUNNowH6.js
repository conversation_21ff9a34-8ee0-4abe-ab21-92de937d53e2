import{a as g,r as W,m as gt,j as ht,s as L,c as k,e as i,d as n,w as m,f as h,t as y,n as P,q as $,p as E,g as ft,o as _,i as T}from"./index--MNqwREY.js";import{u as yt}from"./transactions-F5TqZ8Qm.js";import{useAccountsStore as _t}from"./accounts-bk1VVrk3.js";import{B}from"./BaseChart-BEnmQZd1.js";import{f as x,a as Ct}from"./format-wz8GKlWC.js";import{_ as xt}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./transactions-CrdnwW_k.js";const wt={class:"statistics-page"},Dt={class:"page-container"},bt={class:"time-selector"},kt={class:"stats-overview"},Tt={class:"stats-grid"},St={class:"stat-card primary"},zt={class:"stat-content"},Ft={class:"stat-value"},Mt={class:"stat-card danger"},Yt={class:"stat-content"},It={class:"stat-value"},$t={class:"stat-card success"},Et={class:"stat-content"},Bt={class:"stat-value"},At={class:"stat-card info"},qt={class:"stat-content"},Nt={class:"stat-value"},Ot={class:"charts-section"},Pt={class:"chart-container"},Rt={class:"chart-header"},jt={class:"chart-content"},Vt={key:1,class:"chart-loading"},Ut={class:"chart-container"},Wt={class:"chart-header"},Lt={class:"chart-content"},Xt={key:1,class:"chart-loading"},Gt={class:"chart-container"},Ht={class:"chart-header"},Jt={class:"chart-content"},Kt={key:1,class:"chart-loading"},Qt={class:"chart-container"},Zt={class:"chart-header"},te={class:"chart-content"},ee={key:1,class:"chart-loading"},ne={class:"quick-actions"},ae={class:"settings-content"},se={class:"settings-header"},oe={__name:"index",setup(ie){const S=ft(),A=yt(),R=_t(),j=g(!1),w=g("month"),D=g("line"),b=g(!1),z=g(!1),X=g(!1),G=g(!1),H=g(!1),V=g(new Date),J=g("RM"),K=g("默认"),Q=g(2),l=W({totalIncome:0,totalExpense:0,netIncome:0,transactionCount:0,incomeChange:0,expenseChange:0,netIncomeChange:0,countChange:0}),v=W({trend:null,expenseCategory:null,incomeCategory:null,accountDistribution:null});gt(async()=>{await F()}),ht(w,()=>{w.value==="custom"?b.value=!0:F()});const F=async()=>{j.value=!0;try{await A.fetchTransactions(),await R.fetchAccounts(),Z(),et()}catch(e){console.error("加载统计数据失败:",e),L("加载数据失败")}finally{j.value=!1}},Z=()=>{const e=q(),t=tt(),o=e.filter(u=>u.type==="income").reduce((u,f)=>u+f.amount,0),s=e.filter(u=>u.type==="expense").reduce((u,f)=>u+f.amount,0),a=o-s,r=e.length,d=t.filter(u=>u.type==="income").reduce((u,f)=>u+f.amount,0),c=t.filter(u=>u.type==="expense").reduce((u,f)=>u+f.amount,0),C=d-c,N=t.length;l.totalIncome=o,l.totalExpense=s,l.netIncome=a,l.transactionCount=r,l.incomeChange=M(d,o),l.expenseChange=M(c,s),l.netIncomeChange=M(C,a),l.countChange=M(N,r)},q=()=>{const e=new Date;let t,o;switch(w.value){case"month":t=new Date(e.getFullYear(),e.getMonth(),1),o=new Date(e.getFullYear(),e.getMonth()+1,0);break;case"quarter":const s=Math.floor(e.getMonth()/3);t=new Date(e.getFullYear(),s*3,1),o=new Date(e.getFullYear(),s*3+3,0);break;case"year":t=new Date(e.getFullYear(),0,1),o=new Date(e.getFullYear(),11,31);break;default:t=new Date(e.getFullYear(),e.getMonth(),1),o=new Date(e.getFullYear(),e.getMonth()+1,0)}return A.transactions.filter(s=>{const a=new Date(s.date);return a>=t&&a<=o})},tt=()=>{const e=new Date;let t,o;switch(w.value){case"month":t=new Date(e.getFullYear(),e.getMonth()-1,1),o=new Date(e.getFullYear(),e.getMonth(),0);break;case"quarter":const s=Math.floor(e.getMonth()/3);t=new Date(e.getFullYear(),(s-1)*3,1),o=new Date(e.getFullYear(),s*3,0);break;case"year":t=new Date(e.getFullYear()-1,0,1),o=new Date(e.getFullYear()-1,11,31);break;default:t=new Date(e.getFullYear(),e.getMonth()-1,1),o=new Date(e.getFullYear(),e.getMonth(),0)}return A.transactions.filter(s=>{const a=new Date(s.date);return a>=t&&a<=o})},M=(e,t)=>e===0?t>0?100:0:Math.round((t-e)/e*100),et=()=>{const e=q();U(e),nt(e),at(e),st(e)},U=e=>{const t=ot(e),o=Object.keys(t).sort(),s=o.map(r=>t[r].filter(c=>c.type==="income").reduce((c,C)=>c+C.amount,0)),a=o.map(r=>t[r].filter(c=>c.type==="expense").reduce((c,C)=>c+C.amount,0));v.trend={title:{text:"收支趋势",left:"center",textStyle:{fontSize:16,fontWeight:"bold"}},tooltip:{trigger:"axis",formatter:r=>{let d=`${r[0].axisValue}<br/>`;return r.forEach(c=>{d+=`${c.seriesName}: ${x(c.value)}<br/>`}),d}},legend:{data:["收入","支出"],bottom:10},xAxis:{type:"category",data:o.map(r=>Ct(r,"MM-DD"))},yAxis:{type:"value",axisLabel:{formatter:r=>x(r,"",0)}},series:[{name:"收入",type:D.value,data:s,itemStyle:{color:"#07c160"},smooth:!0},{name:"支出",type:D.value,data:a,itemStyle:{color:"#ee0a24"},smooth:!0}]}},nt=e=>{const t=e.filter(a=>a.type==="expense"),o={};t.forEach(a=>{o[a.category]=(o[a.category]||0)+a.amount});const s=Object.entries(o).map(([a,r])=>({name:a,value:r})).sort((a,r)=>r.value-a.value);v.expenseCategory={title:{text:"支出分类",left:"center",textStyle:{fontSize:16,fontWeight:"bold"}},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left",data:s.map(a=>a.name)},series:[{name:"支出分类",type:"pie",radius:["40%","70%"],center:["60%","50%"],data:s,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]}},at=e=>{const t=e.filter(a=>a.type==="income"),o={};t.forEach(a=>{o[a.category]=(o[a.category]||0)+a.amount});const s=Object.entries(o).map(([a,r])=>({name:a,value:r})).sort((a,r)=>r.value-a.value);v.incomeCategory={title:{text:"收入来源",left:"center",textStyle:{fontSize:16,fontWeight:"bold"}},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left",data:s.map(a=>a.name)},series:[{name:"收入来源",type:"pie",radius:["40%","70%"],center:["60%","50%"],data:s,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]}},st=e=>{const t={};e.forEach(s=>{const a=R.accounts.find(d=>d.id===s.account),r=a?a.name:"未知账户";t[r]=(t[r]||0)+s.amount});const o=Object.entries(t).map(([s,a])=>({name:s,value:a})).sort((s,a)=>a.value-s.value);v.accountDistribution={title:{text:"账户分布",left:"center",textStyle:{fontSize:16,fontWeight:"bold"}},tooltip:{trigger:"axis",formatter:s=>`${s[0].axisValue}: ${x(s[0].value)}`},xAxis:{type:"category",data:o.map(s=>s.name),axisLabel:{rotate:45}},yAxis:{type:"value",axisLabel:{formatter:s=>x(s,"",0)}},series:[{name:"金额",type:"bar",data:o.map(s=>s.value),itemStyle:{color:"#1989fa"}}]}},ot=e=>{const t={};return e.forEach(o=>{const s=o.date.split("T")[0];t[s]||(t[s]=[]),t[s].push(o)}),t},Y=e=>e>0?`+${e}%`:e<0?`${e}%`:"0%",I=(e,t=!1)=>e>0?t?"negative":"positive":e<0?t?"positive":"negative":"neutral",it=()=>{F()},rt=()=>{b.value=!1,F()},lt=()=>{D.value=D.value==="line"?"bar":"line",U(q())},ct=()=>{S.push("/statistics/category")},ut=()=>{S.push("/statistics/income")},dt=()=>{S.push("/statistics/account")},pt=()=>{S.push("/statistics/report")},mt=()=>{L("导出功能开发中...")};return(e,t)=>{const o=h("van-icon"),s=h("van-nav-bar"),a=h("van-tab"),r=h("van-tabs"),d=h("van-button"),c=h("van-loading"),C=h("van-grid-item"),N=h("van-grid"),u=h("van-datetime-picker"),f=h("van-popup"),O=h("van-cell"),vt=h("van-cell-group");return _(),k("div",wt,[i(s,{title:"数据统计",fixed:"",placeholder:"","safe-area-inset-top":""},{right:m(()=>[i(o,{name:"setting-o",size:"18",onClick:t[0]||(t[0]=p=>z.value=!0)})]),_:1}),n("div",Dt,[n("div",bt,[i(r,{active:w.value,"onUpdate:active":t[1]||(t[1]=p=>w.value=p),onChange:it},{default:m(()=>[i(a,{title:"本月",name:"month"}),i(a,{title:"本季",name:"quarter"}),i(a,{title:"本年",name:"year"}),i(a,{title:"自定义",name:"custom"})]),_:1},8,["active"])]),n("div",kt,[n("div",Tt,[n("div",St,[t[11]||(t[11]=n("div",{class:"stat-icon"},"💰",-1)),n("div",zt,[t[10]||(t[10]=n("div",{class:"stat-label"},"总收入",-1)),n("div",Ft,y(P(x)(l.totalIncome)),1),n("div",{class:$(["stat-change",I(l.incomeChange)])},y(Y(l.incomeChange)),3)])]),n("div",Mt,[t[13]||(t[13]=n("div",{class:"stat-icon"},"💸",-1)),n("div",Yt,[t[12]||(t[12]=n("div",{class:"stat-label"},"总支出",-1)),n("div",It,y(P(x)(l.totalExpense)),1),n("div",{class:$(["stat-change",I(l.expenseChange,!0)])},y(Y(l.expenseChange)),3)])]),n("div",$t,[t[15]||(t[15]=n("div",{class:"stat-icon"},"📊",-1)),n("div",Et,[t[14]||(t[14]=n("div",{class:"stat-label"},"净收入",-1)),n("div",Bt,y(P(x)(l.netIncome)),1),n("div",{class:$(["stat-change",I(l.netIncomeChange)])},y(Y(l.netIncomeChange)),3)])]),n("div",At,[t[17]||(t[17]=n("div",{class:"stat-icon"},"📈",-1)),n("div",qt,[t[16]||(t[16]=n("div",{class:"stat-label"},"交易笔数",-1)),n("div",Nt,y(l.transactionCount),1),n("div",{class:$(["stat-change",I(l.countChange)])},y(Y(l.countChange)),3)])])])]),n("div",Ot,[n("div",Pt,[n("div",Rt,[t[18]||(t[18]=n("h3",null,"收支趋势",-1)),i(d,{size:"mini",type:"primary",plain:"",onClick:lt},{default:m(()=>[T(y(D.value==="line"?"柱状图":"折线图"),1)]),_:1})]),n("div",jt,[v.trend?(_(),E(B,{key:0,options:v.trend,height:"240px"},null,8,["options"])):(_(),k("div",Vt,[i(c,{size:"24px"}),t[19]||(t[19]=n("span",null,"加载中...",-1))]))])]),n("div",Ut,[n("div",Wt,[t[21]||(t[21]=n("h3",null,"支出分类",-1)),i(d,{size:"mini",type:"primary",plain:"",onClick:ct},{default:m(()=>t[20]||(t[20]=[T(" 详情 ")])),_:1,__:[20]})]),n("div",Lt,[v.expenseCategory?(_(),E(B,{key:0,options:v.expenseCategory,height:"240px"},null,8,["options"])):(_(),k("div",Xt,[i(c,{size:"24px"}),t[22]||(t[22]=n("span",null,"加载中...",-1))]))])]),n("div",Gt,[n("div",Ht,[t[24]||(t[24]=n("h3",null,"收入来源",-1)),i(d,{size:"mini",type:"primary",plain:"",onClick:ut},{default:m(()=>t[23]||(t[23]=[T(" 详情 ")])),_:1,__:[23]})]),n("div",Jt,[v.incomeCategory?(_(),E(B,{key:0,options:v.incomeCategory,height:"240px"},null,8,["options"])):(_(),k("div",Kt,[i(c,{size:"24px"}),t[25]||(t[25]=n("span",null,"加载中...",-1))]))])]),n("div",Qt,[n("div",Zt,[t[27]||(t[27]=n("h3",null,"账户分布",-1)),i(d,{size:"mini",type:"primary",plain:"",onClick:dt},{default:m(()=>t[26]||(t[26]=[T(" 详情 ")])),_:1,__:[26]})]),n("div",te,[v.accountDistribution?(_(),E(B,{key:0,options:v.accountDistribution,height:"240px"},null,8,["options"])):(_(),k("div",ee,[i(c,{size:"24px"}),t[28]||(t[28]=n("span",null,"加载中...",-1))]))])])]),n("div",ne,[i(N,{"column-num":2,gutter:12},{default:m(()=>[i(C,{onClick:pt},{default:m(()=>[i(o,{name:"description",size:"24"}),t[29]||(t[29]=n("span",null,"生成报告",-1))]),_:1,__:[29]}),i(C,{onClick:mt},{default:m(()=>[i(o,{name:"download",size:"24"}),t[30]||(t[30]=n("span",null,"导出数据",-1))]),_:1,__:[30]})]),_:1})])]),i(f,{show:b.value,"onUpdate:show":t[4]||(t[4]=p=>b.value=p),position:"bottom",round:""},{default:m(()=>[i(u,{modelValue:V.value,"onUpdate:modelValue":t[2]||(t[2]=p=>V.value=p),type:"date",title:"选择时间范围",onConfirm:rt,onCancel:t[3]||(t[3]=p=>b.value=!1)},null,8,["modelValue"])]),_:1},8,["show"]),i(f,{show:z.value,"onUpdate:show":t[9]||(t[9]=p=>z.value=p),position:"bottom",round:""},{default:m(()=>[n("div",ae,[n("div",se,[t[32]||(t[32]=n("h3",null,"统计设置",-1)),i(d,{type:"primary",size:"small",onClick:t[5]||(t[5]=p=>z.value=!1)},{default:m(()=>t[31]||(t[31]=[T(" 完成 ")])),_:1,__:[31]})]),i(vt,null,{default:m(()=>[i(O,{title:"默认货币",value:J.value,"is-link":"",onClick:t[6]||(t[6]=p=>X.value=!0)},null,8,["value"]),i(O,{title:"图表主题",value:K.value,"is-link":"",onClick:t[7]||(t[7]=p=>G.value=!0)},null,8,["value"]),i(O,{title:"数据精度",value:Q.value+"位小数","is-link":"",onClick:t[8]||(t[8]=p=>H.value=!0)},null,8,["value"])]),_:1})])]),_:1},8,["show"])])}}},ve=xt(oe,[["__scopeId","data-v-569f221d"]]);export{ve as default};
