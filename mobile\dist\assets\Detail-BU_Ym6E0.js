import{a as v,m as q,c as g,e as a,w as i,f as l,d as s,t as r,p,v as $,h as G,s as u,g as I,o as c,i as _}from"./index--MNqwREY.js";import{u as J}from"./subscriptions-4RI1wB9c.js";import{S as j,g as A}from"./ServiceLogo-DKV-znac.js";import{_ as F}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{s as x}from"./function-call-w7FI8rRn.js";const H={class:"subscription-detail-page"},K={key:0,class:"loading-container"},O={key:1,class:"page-container"},Q={class:"subscription-card"},W={class:"subscription-header"},X={class:"service-info"},Z={class:"subscription-details"},ee={class:"subscription-name"},te={class:"service-type"},se={class:"subscription-status"},ae={class:"subscription-amount"},ne={class:"amount-display"},ie={class:"currency"},oe={class:"amount"},ce={class:"cycle"},le={class:"billing-info"},re={class:"next-billing"},ue={class:"cell-icon"},_e={class:"cell-icon"},de={class:"cell-icon"},ve={class:"cell-icon"},pe={class:"cell-icon"},me={class:"cell-icon"},ge={class:"action-section"},ye={class:"action-buttons"},fe={key:2,class:"error-container"},be={__name:"Detail",setup(he){const D=G(),B=I(),m=J(),e=v(null),y=v(!0),f=v(!1),b=v(!1),h=v(!1),L=t=>t?A(t).name:"未知服务",M=t=>({monthly:"月",quarterly:"季",yearly:"年",weekly:"周"})[t]||t,N=t=>({CNY:"¥",USD:"$",MYR:"RM",EUR:"€",GBP:"£",JPY:"¥"})[t]||"¥",k=t=>t?new Date(t).toLocaleDateString("zh-CN"):"",R=()=>{B.push(`/subscription/edit/${D.params.id}`)},z=async()=>{try{await x({title:"确认续费",message:"确定要续费这个订阅吗？"}),f.value=!0,await m.renewSubscription(e.value.id),u({message:"续费成功",type:"success"}),await w()}catch(t){t!=="cancel"&&u({message:t.message||"续费失败",type:"fail"})}finally{f.value=!1}},T=async()=>{try{const t=e.value.is_active?"暂停":"恢复";await x({title:`确认${t}`,message:`确定要${t}这个订阅吗？`}),b.value=!0,await m.updateSubscription(e.value.id,{is_active:!e.value.is_active}),u({message:`${t}成功`,type:"success"}),await w()}catch(t){t!=="cancel"&&u({message:t.message||"操作失败",type:"fail"})}finally{b.value=!1}},V=async()=>{try{await x({title:"确认删除",message:"删除后无法恢复，确定要删除这个订阅吗？",confirmButtonText:"确认删除",confirmButtonColor:"#ee0a24"}),h.value=!0,await m.deleteSubscription(e.value.id),u({message:"删除成功",type:"success"}),B.back()}catch(t){t!=="cancel"&&u({message:t.message||"删除失败",type:"fail"})}finally{h.value=!1}},w=async()=>{try{y.value=!0,e.value=await m.getSubscription(D.params.id)}catch{u({message:"获取订阅详情失败",type:"fail"})}finally{y.value=!1}};return q(()=>{w()}),(t,n)=>{const o=l("van-icon"),Y=l("van-nav-bar"),E=l("van-loading"),S=l("van-tag"),d=l("van-cell"),P=l("van-cell-group"),C=l("van-button"),U=l("van-empty");return c(),g("div",H,[a(Y,{title:"订阅详情","left-arrow":"",fixed:"",placeholder:"","safe-area-inset-top":"",onClickLeft:n[0]||(n[0]=ke=>t.$router.back())},{right:i(()=>[a(o,{name:"edit",size:"18",onClick:R})]),_:1}),y.value?(c(),g("div",K,[a(E,{size:"24px",vertical:""},{default:i(()=>n[1]||(n[1]=[_("加载中...")])),_:1,__:[1]})])):e.value?(c(),g("div",O,[n[7]||(n[7]=s("div",{class:"page-header"},[s("h1",{class:"page-title"},"订阅详情"),s("p",{class:"page-description"},"查看和管理您的订阅信息")],-1)),s("div",Q,[s("div",W,[s("div",X,[a(j,{"service-name":e.value.name,"service-type":e.value.service_type,size:"medium"},null,8,["service-name","service-type"]),s("div",Z,[s("h2",ee,r(e.value.name),1),s("p",te,r(L(e.value.service_type)),1)])]),s("div",se,[e.value.is_due_soon?(c(),p(S,{key:0,type:"warning",class:"status-tag"},{default:i(()=>[_(r(e.value.days_until_billing)+"天后到期 ",1)]),_:1})):e.value.is_active?(c(),p(S,{key:2,type:"success",class:"status-tag"},{default:i(()=>n[3]||(n[3]=[_(" 正常 ")])),_:1,__:[3]})):(c(),p(S,{key:1,type:"default",class:"status-tag"},{default:i(()=>n[2]||(n[2]=[_(" 已暂停 ")])),_:1,__:[2]}))])]),s("div",ae,[s("div",ne,[s("span",ie,r(N(e.value.currency)),1),s("span",oe,r(e.value.amount),1),s("span",ce,r(M(e.value.billing_cycle)),1)]),s("div",le,[s("span",re," 下次扣费: "+r(k(e.value.next_billing_date)),1)])])]),a(P,{inset:"",title:"📋 详细信息"},{default:i(()=>[a(d,{title:"服务类型",value:L(e.value.service_type)},{icon:i(()=>[s("div",ue,[a(o,{name:"apps-o"})])]),_:1},8,["value"]),a(d,{title:"计费周期",value:M(e.value.billing_cycle)},{icon:i(()=>[s("div",_e,[a(o,{name:"clock-o"})])]),_:1},8,["value"]),a(d,{title:"开始日期",value:k(e.value.start_date)},{icon:i(()=>[s("div",de,[a(o,{name:"calendar-o"})])]),_:1},8,["value"]),a(d,{title:"下次扣费",value:k(e.value.next_billing_date)},{icon:i(()=>[s("div",ve,[a(o,{name:"credit-pay"})])]),_:1},8,["value"]),a(d,{title:"货币类型",value:e.value.currency},{icon:i(()=>[s("div",pe,[a(o,{name:"gold-coin-o"})])]),_:1},8,["value"]),e.value.description?(c(),p(d,{key:0,title:"备注",value:e.value.description},{icon:i(()=>[s("div",me,[a(o,{name:"notes-o"})])]),_:1},8,["value"])):$("",!0)]),_:1}),s("div",ge,[n[6]||(n[6]=s("h3",{class:"action-title"},"🛠️ 操作",-1)),s("div",ye,[e.value.is_due_soon?(c(),p(C,{key:0,type:"primary",block:"",onClick:z,loading:f.value,class:"action-btn primary-btn"},{default:i(()=>[a(o,{name:"credit-pay"}),n[4]||(n[4]=_(" 立即续费 "))]),_:1,__:[4]},8,["loading"])):$("",!0),a(C,{type:"warning",block:"",plain:"",onClick:T,loading:b.value,class:"action-btn warning-btn"},{default:i(()=>[a(o,{name:e.value.is_active?"pause-circle-o":"play-circle-o"},null,8,["name"]),_(" "+r(e.value.is_active?"暂停订阅":"恢复订阅"),1)]),_:1},8,["loading"]),a(C,{type:"danger",block:"",plain:"",onClick:V,loading:h.value,class:"action-btn danger-btn"},{default:i(()=>[a(o,{name:"delete-o"}),n[5]||(n[5]=_(" 删除订阅 "))]),_:1,__:[5]},8,["loading"])])])])):(c(),g("div",fe,[a(U,{description:"订阅不存在或已被删除"})]))])}}},Be=F(be,[["__scopeId","data-v-47b754e8"]]);export{Be as default};
