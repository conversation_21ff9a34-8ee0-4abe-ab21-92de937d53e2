/* 全局样式文件 */

/* 自定义CSS变量 */
:root {
  --primary-color: #1989fa;
  --success-color: #07c160;
  --warning-color: #ff976a;
  --danger-color: #ee0a24;
  --text-color: #323233;
  --text-color-2: #646566;
  --text-color-3: #969799;
  --border-color: #ebedf0;
  --background-color: #f7f8fa;
  --white: #ffffff;
}

/* 通用工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

/* 间距工具类 */
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.ml-1 { margin-left: 8px; }
.ml-2 { margin-left: 16px; }
.mr-1 { margin-right: 8px; }
.mr-2 { margin-right: 16px; }

.pt-1 { padding-top: 8px; }
.pt-2 { padding-top: 16px; }
.pb-1 { padding-bottom: 8px; }
.pb-2 { padding-bottom: 16px; }
.pl-1 { padding-left: 8px; }
.pl-2 { padding-left: 16px; }
.pr-1 { padding-right: 8px; }
.pr-2 { padding-right: 16px; }

/* 文字颜色 */
.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-muted { color: var(--text-color-3); }

/* 金额显示样式 */
.amount {
  font-weight: 600;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.amount.income {
  color: var(--success-color);
}

.amount.expense {
  color: var(--danger-color);
}

/* 卡片样式 */
.card {
  background: var(--white);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 页面容器 */
.page-container {
  padding: 16px;
  min-height: calc(100vh - 50px); /* 减去底部导航栏高度 */
  background-color: var(--background-color);
}

/* 安全区域适配 */
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-color-3);
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-color-3);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

/* 图表容器 */
.chart-container {
  background: var(--white);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--text-color);
}

/* 确保导航栏始终在最前面 */
.van-nav-bar {
  z-index: 9999 !important;
}

.van-nav-bar--fixed {
  z-index: 9999 !important;
}
