#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
交易相关API蓝图
"""

from flask import Blueprint, request, jsonify, g, Response, make_response, session
import datetime
import uuid
import logging
import sqlite3
import csv
import io
import json
import os
import sys
import threading
from concurrent.futures import ThreadPoolExecutor

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.database_config import MAIN_DB_PATH

# 添加bot模块到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from bot.utils.budget_checker import budget_checker
    from bot.telegram_webhook import telegram_bot
    from bot.config.bot_config import config
    # 检查是否禁用预算检查
    BUDGET_NOTIFICATION_ENABLED = not config.DISABLE_BUDGET_CHECK
except ImportError as e:
    logging.warning(f"无法导入预算通知模块: {e}")
    BUDGET_NOTIFICATION_ENABLED = False

# 创建蓝图
bp = Blueprint('transactions', __name__)
logger = logging.getLogger(__name__)

# 创建线程池用于异步任务
executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="budget_check")

def get_db_connection():
    """获取数据库连接"""
    if 'db' not in g:
        g.db = sqlite3.connect(MAIN_DB_PATH)
        g.db.row_factory = sqlite3.Row
        # 启用外键约束
        g.db.execute('PRAGMA foreign_keys = ON')
    return g.db

def login_required(f):
    """登录验证装饰器"""
    from functools import wraps

    @wraps(f)
    def decorated(*args, **kwargs):
        if not session.get('logged_in') or not session.get('user_id'):
            logger.warning(f"未登录用户访问API: {request.path}")
            return jsonify({'success': False, 'error': '请先登录'}), 401
        return f(*args, **kwargs)
    return decorated

def get_current_user_id():
    """获取当前登录用户ID"""
    return session.get('user_id')

def _async_budget_check_and_notify(transaction_data):
    """异步执行预算检查和通知发送"""
    try:
        if not BUDGET_NOTIFICATION_ENABLED:
            return

        # 检查预算
        budget_notification = budget_checker.check_budget_after_transaction(transaction_data)

        # 如果有预算通知，发送到Telegram管理员
        if budget_notification and telegram_bot.enabled:
            # 获取管理员chat_id并发送通知
            admin_chat_ids = telegram_bot.get_admin_chat_ids()
            if admin_chat_ids:
                for chat_id in admin_chat_ids:
                    telegram_bot.send_message(chat_id, budget_notification['message'])
                    logger.info(f"已发送预算通知到Telegram管理员: {chat_id}")
            else:
                logger.warning("没有找到管理员用户，无法发送预算通知")

    except Exception as e:
        logger.error(f"异步预算检查和通知时出错: {str(e)}")
        # 不影响主流程，只记录错误

@bp.route('/test-notification', methods=['POST'])
def test_notification():
    """测试预算通知功能"""
    if not BUDGET_NOTIFICATION_ENABLED:
        return jsonify({'error': '预算通知功能未启用'}), 400

    if not telegram_bot.enabled:
        return jsonify({'error': 'Telegram机器人未启用'}), 400

    # 使用新的管理员ID获取方法
    admin_chat_ids = telegram_bot.get_admin_chat_ids()
    if not admin_chat_ids:
        return jsonify({'error': '没有找到管理员用户，请确保有网站管理员已绑定Telegram账户'}), 400

    test_message = "🧪 这是一条测试消息，用于验证预算通知功能是否正常工作。"

    success_count = 0
    for chat_id in admin_chat_ids:
        if telegram_bot.send_message(chat_id, test_message):
            success_count += 1
            logger.info(f"测试消息发送成功到管理员: {chat_id}")
        else:
            logger.error(f"测试消息发送失败到管理员: {chat_id}")

    return jsonify({
        'success': True,
        'message': f'测试消息已发送到 {success_count}/{len(admin_chat_ids)} 个管理员',
        'admin_chat_ids': list(admin_chat_ids)
    })

@bp.route('/', methods=['GET'])
@login_required
def get_transactions():
    """获取当前用户的所有交易"""
    user_id = get_current_user_id()

    # 获取查询参数
    account = request.args.get('account')
    transaction_type = request.args.get('type')
    category = request.args.get('category')
    description = request.args.get('description')  # 新增：按描述精确查询
    start_date = request.args.get('start_date')    # 新增：开始日期
    end_date = request.args.get('end_date')        # 新增：结束日期
    limit = request.args.get('limit', type=int)

    logger.info(f"获取用户 {user_id} 的交易，筛选条件: account={account}, type={transaction_type}, category={category}, description={description}, start_date={start_date}, end_date={end_date}, limit={limit}")

    conn = get_db_connection()

    # 构建查询条件
    where_conditions = ['user_id = ?']
    params = [user_id]

    if account:
        where_conditions.append('account = ?')
        params.append(account)

    if transaction_type:
        where_conditions.append('type = ?')
        params.append(transaction_type)

    if category:
        where_conditions.append('category = ?')
        params.append(category)

    if description:
        where_conditions.append('description = ?')  # 精确匹配描述
        params.append(description)

    if start_date:
        where_conditions.append('date >= ?')
        params.append(start_date)

    if end_date:
        where_conditions.append('date <= ?')
        params.append(end_date)

    # 构建完整查询
    query = f"SELECT * FROM transactions WHERE {' AND '.join(where_conditions)} ORDER BY date DESC"

    if limit:
        query += f" LIMIT {limit}"

    transactions = conn.execute(query, params).fetchall()
    return jsonify([dict(tx) for tx in transactions])

@bp.route('/<transaction_id>', methods=['GET'])
@login_required
def get_transaction(transaction_id):
    """获取单个交易"""
    user_id = get_current_user_id()
    logger.info(f"用户 {user_id} 获取交易: {transaction_id}")
    conn = get_db_connection()
    transaction = conn.execute(
        'SELECT * FROM transactions WHERE id = ? AND user_id = ?',
        (transaction_id, user_id)
    ).fetchone()

    if transaction is None:
        return jsonify({'error': '交易不存在'}), 404

    return jsonify(dict(transaction))

@bp.route('/', methods=['POST'])
@login_required
def create_transaction():
    """创建交易"""
    data = request.json
    user_id = get_current_user_id()
    logger.info(f"用户 {user_id} 创建/更新交易: {data.get('id', '新交易')}")

    # 验证必填字段
    required_fields = ['date', 'type', 'category', 'description', 'account', 'amount', 'currency']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'缺少必填字段: {field}'}), 400

    # 生成ID和时间戳
    transaction_id = data.get('id') or str(uuid.uuid4())
    now = datetime.datetime.now().isoformat()

    conn = get_db_connection()

    # 检查是否存在相同ID的交易（只检查当前用户的交易）
    existing = conn.execute(
        'SELECT id FROM transactions WHERE id = ? AND user_id = ?',
        (transaction_id, user_id)
    ).fetchone()

    if existing:
        # 更新现有交易
        conn.execute('''
        UPDATE transactions
        SET date = ?, type = ?, category = ?, description = ?, account = ?, amount = ?, currency = ?,
            attachment = ?, updated_at = ?
        WHERE id = ? AND user_id = ?
        ''', (
            data['date'], data['type'], data['category'], data['description'], data['account'],
            data['amount'], data['currency'], data.get('attachment', ''), now, transaction_id, user_id
        ))
        logger.info(f"用户 {user_id} 更新交易: {transaction_id}")
    else:
        # 创建新交易
        conn.execute('''
        INSERT INTO transactions (id, user_id, date, type, category, description, account, amount, currency,
                                attachment, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            transaction_id, user_id, data['date'], data['type'], data['category'], data['description'],
            data['account'], data['amount'], data['currency'], data.get('attachment', ''), now, now
        ))
        logger.info(f"用户 {user_id} 创建新交易: {transaction_id}")

    conn.commit()

    # 清除相关缓存以确保数据一致性
    try:
        from bot.handlers.transaction_handler import transaction_handler
        # 清除余额缓存（因为余额已改变）
        transaction_handler.invalidate_balance_cache()
        # 如果是支出，清除预算缓存
        if data['type'] == 'expense':
            transaction_handler.invalidate_budget_cache(data['category'])
    except Exception as cache_error:
        logger.warning(f"清除缓存失败: {str(cache_error)}")

    # 异步执行预算检查和通知（不阻塞响应）
    if data['type'] == 'expense':
        try:
            executor.submit(_async_budget_check_and_notify, data)
        except Exception as e:
            logger.warning(f"提交异步预算检查任务失败: {str(e)}")

    # 立即返回成功响应
    return jsonify({'id': transaction_id, 'success': True})



@bp.route('/<transaction_id>', methods=['PUT'])
@login_required
def update_transaction(transaction_id):
    """更新交易"""
    user_id = get_current_user_id()
    data = request.json
    logger.info(f"用户 {user_id} 更新交易: {transaction_id}")

    # 验证必填字段
    required_fields = ['date', 'type', 'category', 'description', 'account', 'amount', 'currency']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'缺少必填字段: {field}'}), 400

    conn = get_db_connection()

    # 检查交易是否存在且属于当前用户
    existing = conn.execute(
        'SELECT id FROM transactions WHERE id = ? AND user_id = ?',
        (transaction_id, user_id)
    ).fetchone()

    if not existing:
        return jsonify({'error': '交易不存在'}), 404

    # 更新交易
    now = datetime.datetime.now().isoformat()
    conn.execute('''
    UPDATE transactions
    SET date = ?, type = ?, category = ?, description = ?, account = ?, amount = ?, currency = ?,
        attachment = ?, updated_at = ?
    WHERE id = ? AND user_id = ?
    ''', (
        data['date'], data['type'], data['category'], data['description'], data['account'],
        data['amount'], data['currency'], data.get('attachment', ''), now, transaction_id, user_id
    ))

    conn.commit()

    # 清除相关缓存以确保数据一致性
    try:
        from bot.handlers.transaction_handler import transaction_handler
        # 清除余额缓存（因为余额已改变）
        transaction_handler.invalidate_balance_cache()
        # 如果是支出，清除预算缓存
        if data['type'] == 'expense':
            transaction_handler.invalidate_budget_cache(data['category'])
    except Exception as cache_error:
        logger.warning(f"清除缓存失败: {str(cache_error)}")

    # 异步执行预算检查和通知（不阻塞响应）
    if data['type'] == 'expense':
        try:
            executor.submit(_async_budget_check_and_notify, data)
        except Exception as e:
            logger.warning(f"提交异步预算检查任务失败: {str(e)}")

    # 获取更新后的交易数据并返回
    updated_transaction = conn.execute(
        'SELECT * FROM transactions WHERE id = ? AND user_id = ?',
        (transaction_id, user_id)
    ).fetchone()

    return jsonify(dict(updated_transaction))

@bp.route('/<transaction_id>', methods=['DELETE'])
@login_required
def delete_transaction(transaction_id):
    """删除交易"""
    user_id = get_current_user_id()
    logger.info(f"用户 {user_id} 删除交易: {transaction_id}")
    conn = get_db_connection()
    result = conn.execute(
        'DELETE FROM transactions WHERE id = ? AND user_id = ?',
        (transaction_id, user_id)
    )
    conn.commit()

    if result.rowcount == 0:
        return jsonify({'error': '交易不存在或无权限删除'}), 404

    return jsonify({'success': True})

@bp.route('/export/csv', methods=['GET'])
@login_required
def export_transactions_csv():
    """导出当前用户的交易记录为CSV格式"""
    user_id = get_current_user_id()
    logger.info(f"用户 {user_id} 导出交易记录为CSV")
    conn = get_db_connection()
    transactions = conn.execute(
        'SELECT * FROM transactions WHERE user_id = ? ORDER BY date DESC',
        (user_id,)
    ).fetchall()

    # 获取当前用户的账户信息，用于将账户ID转换为账户名称
    accounts = conn.execute(
        'SELECT id, name FROM accounts WHERE user_id = ?',
        (user_id,)
    ).fetchall()
    account_map = {account['id']: account['name'] for account in accounts}

    # 创建二进制内存文件，使用UTF-8-BOM编码
    output = io.BytesIO()
    # 写入UTF-8 BOM标记，使Excel能正确识别中文
    output.write(b'\xef\xbb\xbf')

    # 创建CSV写入器，使用UTF-8编码
    # 使用StringIO作为中间缓冲区
    temp_output = io.StringIO()
    writer = csv.writer(temp_output, dialect='excel')

    # 写入CSV头部
    writer.writerow(['日期', '类型', '类别', '描述', '账户', '金额', '货币'])

    # 交易类型映射（英文到中文）
    type_map = {
        'income': '收入',
        'expense': '支出'
    }

    # 写入交易数据
    for tx in transactions:
        tx_dict = dict(tx)

        # 将账户ID转换为账户名称
        account_name = account_map.get(tx_dict['account'], tx_dict['account'])

        # 将交易类型转换为中文
        tx_type = type_map.get(tx_dict['type'], tx_dict['type'])

        writer.writerow([
            tx_dict['date'],
            tx_type,  # 使用中文类型
            tx_dict['category'],
            tx_dict['description'],
            account_name,  # 使用账户名称而非ID
            tx_dict['amount'],
            tx_dict['currency']
        ])

    # 将StringIO的内容写入BytesIO
    temp_output.seek(0)
    output.write(temp_output.getvalue().encode('utf-8'))

    # 创建响应
    output.seek(0)
    response = make_response(output.getvalue())
    response.headers["Content-Disposition"] = f"attachment; filename=transactions_{datetime.datetime.now().strftime('%Y%m%d')}.csv"
    response.headers["Content-type"] = "text/csv; charset=utf-8"

    return response

@bp.route('/import/csv', methods=['POST'])
@login_required
def import_transactions_csv():
    """从CSV导入交易记录到当前用户"""
    user_id = get_current_user_id()
    logger.info(f"用户 {user_id} 从CSV导入交易记录")

    if 'file' not in request.files:
        return jsonify({'error': '没有上传文件'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': '没有选择文件'}), 400

    if not file.filename.endswith('.csv'):
        return jsonify({'error': '请上传CSV文件'}), 400

    try:
        # 读取CSV文件内容
        file_content = file.stream.read()

        # 检测BOM标记并正确解码
        if file_content.startswith(b'\xef\xbb\xbf'):
            # 如果有BOM标记，跳过这3个字节
            file_content = file_content[3:]

        # 解码为UTF-8
        decoded_content = file_content.decode('utf-8', errors='replace')

        # 创建CSV读取器
        stream = io.StringIO(decoded_content)
        csv_reader = csv.reader(stream)

        # 跳过标题行
        next(csv_reader)

        conn = get_db_connection()

        # 获取当前用户的账户信息，用于将账户名称转换为账户ID
        accounts = conn.execute(
            'SELECT id, name FROM accounts WHERE user_id = ?',
            (user_id,)
        ).fetchall()
        account_name_to_id = {account['name']: account['id'] for account in accounts}

        # 交易类型映射（中文到英文）
        type_map = {
            '收入': 'income',
            '支出': 'expense'
        }

        conn.execute('BEGIN TRANSACTION')

        imported_count = 0

        try:
            for row in csv_reader:
                if len(row) < 7:  # 确保行有足够的列
                    continue

                # 解析CSV行
                date, tx_type, category, description, account, amount, currency = row[:7]

                # 验证必填字段
                if not all([date, tx_type, category, description, account, amount, currency]):
                    continue

                # 将中文交易类型转换为英文
                tx_type_en = type_map.get(tx_type, tx_type)

                # 查找账户ID
                account_id = None
                if account in account_name_to_id:
                    account_id = account_name_to_id[account]
                else:
                    # 如果找不到账户，为当前用户创建一个新账户
                    account_id = str(uuid.uuid4())
                    now = datetime.datetime.now().isoformat()
                    conn.execute('''
                    INSERT INTO accounts (id, user_id, name, type, currency, initial_balance, description, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        account_id, user_id, account, 'bank', currency, 0, f'自动创建 - 从CSV导入', now, now
                    ))
                    account_name_to_id[account] = account_id

                # 生成ID和时间戳
                transaction_id = str(uuid.uuid4())
                now = datetime.datetime.now().isoformat()

                # 创建新交易
                conn.execute('''
                INSERT INTO transactions (id, user_id, date, type, category, description, account, amount, currency,
                                        attachment, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    transaction_id, user_id, date, tx_type_en, category, description,
                    account_id, float(amount), currency, '', now, now
                ))

                imported_count += 1

            # 提交事务
            conn.commit()
            logger.info(f"成功导入{imported_count}条交易记录")

            return jsonify({
                'success': True,
                'message': f'成功导入{imported_count}条交易记录',
                'count': imported_count
            })

        except Exception as e:
            # 回滚事务
            conn.rollback()
            logger.error(f"导入CSV失败: {str(e)}")
            return jsonify({'error': f'导入失败: {str(e)}'}), 500

    except Exception as e:
        logger.error(f"处理CSV文件失败: {str(e)}")
        return jsonify({'error': f'处理CSV文件失败: {str(e)}'}), 500
