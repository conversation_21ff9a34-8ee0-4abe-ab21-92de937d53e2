import{a as F,l as d,m as I,s as N,c as w,e as n,d as t,w as V,f as m,t as l,n as b,g as q,o as x}from"./index--MNqwREY.js";import{useBudgetsStore as L}from"./budgets-DJh-v5Y8.js";import{u as O}from"./transactions-F5TqZ8Qm.js";import{f as B}from"./format-wz8GKlWC.js";import{_ as R}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./transactions-CrdnwW_k.js";const j={class:"budgets-page"},G={class:"page-container"},H={key:0,class:"loading-container"},J={key:1},K={class:"budget-overview-card"},Q={class:"overview-header"},W={class:"overview-title"},X={class:"overview-trend"},Z={class:"overview-icon"},tt={class:"compact-stats"},st={class:"stat-row"},et={class:"stat-item"},ot={class:"stat-value"},nt={class:"stat-item"},at={class:"stat-value used"},it={class:"stat-item"},lt={class:"stat-value remaining"},rt={class:"overall-progress"},dt={class:"progress-header"},ct={class:"percentage"},ut={class:"monthly-overview-card"},vt={class:"monthly-header"},_t={class:"monthly-title"},gt={class:"year-selector"},mt={class:"monthly-icon"},pt={class:"entry-content"},ht={class:"entry-info"},ft={class:"entry-subtitle"},yt={class:"quick-actions-card"},wt={class:"actions-header"},bt={class:"actions-icon"},xt={class:"action-buttons"},Bt={__name:"index",setup(kt){const p=q(),i=L(),z=O(),h=F(!1),k=d(()=>i.totalBudgetAmount),S=d(()=>i.totalUsedAmount),c=d(()=>i.totalUsagePercentage);d(()=>{const s=[];for(let e=1;e<=12;e++){const a=M(2025,e);s.push({month:e,name:`${e}月`,budget:a.budget,used:a.used,remaining:a.remaining,usagePercentage:a.usagePercentage,status:a.status})}return s}),I(async()=>{await T(),await D()});const T=async()=>{h.value=!0;try{await i.fetchBudgets()}catch(o){console.error("加载预算失败:",o),N({message:"加载预算失败",type:"fail"})}finally{h.value=!1}},D=async()=>{try{await z.fetchTransactions()}catch(o){console.error("加载交易数据失败:",o)}},C=()=>{p.push("/budget/add")},M=(o,s)=>{const e=String(s).padStart(2,"0"),a=`${o}-${e}-01`,f=s===12?1:s+1,y=s===12?o+1:o,$=new Date(y,f-1,0).toISOString().split("T")[0];let r=0,u=0;i.budgets&&i.budgets.forEach(g=>{g.start_date<=$&&g.end_date>=a&&(r+=g.amount||0,u+=g.used_amount||0)});const E=r-u,v=r>0?Math.round(u/r*100):0;let _="normal";return v>=100?_="exceeded":v>=90?_="danger":v>=80&&(_="warning"),{budget:r,used:u,remaining:E,usagePercentage:v,status:_}},P=d(()=>new Date().getFullYear()),A=()=>{p.push("/budgets/list")},U=()=>{p.push("/budgets/annual")},Y=o=>o>=100?"#ee0a24":o>=90?"#ff976a":o>=80?"#ffd21e":"#07c160";return(o,s)=>{const e=m("van-icon"),a=m("van-nav-bar"),f=m("van-loading"),y=m("van-progress");return x(),w("div",j,[n(a,{title:"预算管理",fixed:"",placeholder:"","safe-area-inset-top":""},{right:V(()=>[n(e,{name:"plus",size:"18",onClick:C})]),_:1}),t("div",G,[h.value?(x(),w("div",H,[n(f,{size:"24px"}),s[0]||(s[0]=t("span",null,"加载中...",-1))])):(x(),w("div",J,[t("div",K,[t("div",Q,[t("div",W,[s[1]||(s[1]=t("h3",null,"预算概览",-1)),t("div",X,[n(e,{name:"chart-trending-o",size:"14"}),t("span",null,l(Math.round(c.value))+"%",1)])]),t("div",Z,[n(e,{name:"gold-coin-o",size:"20"})])]),t("div",tt,[t("div",st,[t("div",et,[s[2]||(s[2]=t("span",{class:"stat-label"},"总预算",-1)),t("span",ot,l(b(B)(k.value)),1)]),t("div",nt,[s[3]||(s[3]=t("span",{class:"stat-label"},"已使用",-1)),t("span",at,l(b(B)(S.value)),1)]),t("div",it,[s[4]||(s[4]=t("span",{class:"stat-label"},"剩余",-1)),t("span",lt,l(b(B)(k.value-S.value)),1)])])]),t("div",rt,[t("div",dt,[s[5]||(s[5]=t("span",null,"总体使用率",-1)),t("span",ct,l(c.value.toFixed(1))+"%",1)]),n(y,{percentage:Math.min(c.value,100),color:Y(c.value),"stroke-width":"6"},null,8,["percentage","color"])])]),t("div",ut,[t("div",vt,[t("div",_t,[s[7]||(s[7]=t("h3",null,"2025年月度预算",-1)),t("div",gt,[n(e,{name:"calendar-o",size:"14"}),s[6]||(s[6]=t("span",null,"2025",-1))])]),t("div",mt,[n(e,{name:"chart-trending-o",size:"20"})])]),t("div",{class:"annual-budget-entry",onClick:U},[t("div",pt,[s[9]||(s[9]=t("div",{class:"entry-icon"},"📊",-1)),t("div",ht,[s[8]||(s[8]=t("div",{class:"entry-title"},"查看今年预算详情",-1)),t("div",ft,l(P.value)+"年月度预算概览",1)]),n(e,{name:"arrow"})])])]),t("div",yt,[t("div",wt,[s[10]||(s[10]=t("div",{class:"actions-title"},[t("h3",null,"快速操作"),t("div",{class:"actions-subtitle"},"管理您的预算")],-1)),t("div",bt,[n(e,{name:"setting-o",size:"20"})])]),t("div",xt,[t("div",{class:"action-button",onClick:A},[s[11]||(s[11]=t("div",{class:"action-icon"},"📊",-1)),s[12]||(s[12]=t("div",{class:"action-text"},[t("div",{class:"action-title"},"查看预算列表"),t("div",{class:"action-desc"},"管理所有预算项目")],-1)),n(e,{name:"arrow"})]),t("div",{class:"action-button",onClick:C},[s[13]||(s[13]=t("div",{class:"action-icon"},"➕",-1)),s[14]||(s[14]=t("div",{class:"action-text"},[t("div",{class:"action-title"},"添加新预算"),t("div",{class:"action-desc"},"创建预算计划")],-1)),n(e,{name:"arrow"})])])])]))])])}}},Pt=R(Bt,[["__scopeId","data-v-81c4c39c"]]);export{Pt as default};
