import{a as k,l as p,m as Z,s as x,c as l,e as i,d as n,w as g,f as c,v as B,p as y,F as ee,k as te,g as ae,o,i as L,q as C,t as r,n as D}from"./index--MNqwREY.js";import{useBudgetsStore as se}from"./budgets-DJh-v5Y8.js";import{u as ne}from"./transactions-F5TqZ8Qm.js";import{f as M}from"./format-wz8GKlWC.js";import{_ as oe}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./transactions-CrdnwW_k.js";const re={class:"budget-list-page"},ce={class:"page-container"},le={key:0,class:"loading-container"},ie={key:1},de={key:0,class:"budget-alerts"},ue={class:"budget-list"},_e={key:0,class:"empty-container"},pe={key:1,class:"budget-cards"},ge=["onClick"],me={class:"card-header"},ve={class:"budget-info"},fe={class:"budget-details"},he={class:"project-name"},ye={class:"budget-meta"},we={class:"category-tag"},ke={class:"period-text"},xe={class:"budget-progress"},Be={class:"progress-header"},Ce={class:"used-amount"},De={class:"total-amount"},Me={class:"progress-bar-container"},$e={class:"progress-percentage"},Ve={class:"remaining-info"},Se={class:"remaining-amount"},Te={class:"remaining-days"},Fe={__name:"List",setup(ze){const $=ae(),V=se(),N=ne(),w=k(!1),d=k(""),m=k("date_asc"),S=p(()=>V.budgets),b=p(()=>{const e=new Set;S.value.forEach(s=>{if(s.start_date){const u=F(s.start_date);e.add(u)}});const t=Array.from(e).sort();return[{text:"全部月份",value:""},...t.map(s=>({text:s,value:s}))]}),A=[{text:"默认排序",value:"date_asc"},{text:"月份升序",value:"date_asc"},{text:"月份降序",value:"date_desc"},{text:"金额升序",value:"amount_asc"},{text:"金额降序",value:"amount_desc"},{text:"使用率升序",value:"usage_asc"},{text:"使用率降序",value:"usage_desc"}],v=p(()=>{let e=[...S.value];return d.value&&(e=e.filter(t=>F(t.start_date)===d.value)),e.sort((t,s)=>{switch(m.value){case"date_asc":return new Date(t.start_date)-new Date(s.start_date);case"date_desc":return new Date(s.start_date)-new Date(t.start_date);case"amount_asc":return(t.amount||0)-(s.amount||0);case"amount_desc":return(s.amount||0)-(t.amount||0);case"usage_asc":return(t.usage_percentage||0)-(s.usage_percentage||0);case"usage_desc":return(s.usage_percentage||0)-(t.usage_percentage||0);default:return new Date(t.start_date)-new Date(s.start_date)}}),e}),f=p(()=>v.value.filter(e=>{const t=e.usage_percentage||0;return t>=80&&t<100})),h=p(()=>v.value.filter(e=>(e.usage_percentage||0)>=100));Z(async()=>{await I(),await O()});const I=async()=>{w.value=!0;try{await V.fetchBudgets()}catch(e){console.error("加载预算失败:",e),x({message:"加载预算失败",type:"fail"})}finally{w.value=!1}},O=async()=>{try{await N.fetchTransactions()}catch(e){console.error("加载交易数据失败:",e)}},T=()=>{$.push("/budget/add")},R=e=>{$.push(`/budget/detail/${e.id}`)},U=()=>{const e=h.value.map(t=>t.category).join("、");x({message:`超支预算：${e}`,type:"fail",duration:3e3})},W=()=>{const e=f.value.map(t=>t.category).join("、");x({message:`接近上限预算：${e}`,type:"warning",duration:3e3})},F=e=>{if(!e)return"";const t=new Date(e),s=t.getFullYear(),u=t.getMonth()+1;return`${s}年${String(u).padStart(2,"0")}月`},Y=()=>{d.value="",m.value="date_asc"},q=e=>({储蓄:"💰",固定:"🏠",流动:"🛒",债务:"💳"})[e]||"💰",P=e=>({normal:"正常",warning:"警告",danger:"危险",exceeded:"超支"})[e]||"正常",G=e=>e>=100?"#ee0a24":e>=90?"#ff976a":e>=80?"#ffd21e":"#07c160",H=(e,t)=>{if(!e)return"";const s=new Date(e);return`${s.getFullYear()}年${s.getMonth()+1}月`},J=e=>{if(!e.end_date)return"";const t=new Date,u=new Date(e.end_date)-t,_=Math.ceil(u/(1e3*60*60*24));return _<0?"已过期":_===0?"今天结束":_<=7?`${_}天后结束`:""};return(e,t)=>{const s=c("van-icon"),u=c("van-nav-bar"),_=c("van-loading"),z=c("van-dropdown-item"),K=c("van-dropdown-menu"),j=c("van-notice-bar"),E=c("van-button"),Q=c("van-empty"),X=c("van-progress");return o(),l("div",re,[i(u,{title:"预算列表","left-arrow":"",onClickLeft:t[0]||(t[0]=a=>e.$router.back()),fixed:"",placeholder:"","safe-area-inset-top":""},{right:g(()=>[i(s,{name:"plus",size:"18",onClick:T})]),_:1}),n("div",ce,[w.value?(o(),l("div",le,[i(_,{size:"24px"}),t[3]||(t[3]=n("span",null,"加载中...",-1))])):(o(),l("div",ie,[i(K,null,{default:g(()=>[i(z,{modelValue:d.value,"onUpdate:modelValue":t[1]||(t[1]=a=>d.value=a),options:b.value,title:"全部月份"},null,8,["modelValue","options"]),i(z,{modelValue:m.value,"onUpdate:modelValue":t[2]||(t[2]=a=>m.value=a),options:A,title:"默认排序"},null,8,["modelValue"])]),_:1}),f.value.length>0||h.value.length>0?(o(),l("div",de,[h.value.length>0?(o(),y(j,{key:0,"left-icon":"warning-o",color:"#ee0a24",background:"#fef0f0",text:`${h.value.length}个预算已超支`,onClick:U},null,8,["text"])):B("",!0),f.value.length>0?(o(),y(j,{key:1,"left-icon":"info-o",color:"#ff976a",background:"#fff7cc",text:`${f.value.length}个预算接近上限`,onClick:W},null,8,["text"])):B("",!0)])):B("",!0),n("div",ue,[v.value.length===0?(o(),l("div",_e,[i(Q,{image:"search",description:d.value?"该月份没有预算数据":"还没有设置预算"},{default:g(()=>[d.value?(o(),y(E,{key:1,type:"default",size:"small",onClick:Y},{default:g(()=>t[5]||(t[5]=[L(" 查看所有预算 ")])),_:1,__:[5]})):(o(),y(E,{key:0,type:"primary",size:"small",onClick:T},{default:g(()=>t[4]||(t[4]=[L(" 创建第一个预算 ")])),_:1,__:[4]}))]),_:1},8,["description"])])):(o(),l("div",pe,[(o(!0),l(ee,null,te(v.value,a=>(o(),l("div",{key:a.id,class:C(["budget-card",a.status]),onClick:je=>R(a)},[n("div",me,[n("div",ve,[n("div",{class:C(["budget-icon",a.status])},r(q(a.category)),3),n("div",fe,[n("h4",he,r(a.project_name||a.category),1),n("p",ye,[n("span",we,r(a.category),1),n("span",ke,r(H(a.start_date,a.end_date)),1)])])]),n("div",{class:C(["status-indicator",a.status])},r(P(a.status)),3)]),n("div",xe,[n("div",Be,[n("span",Ce,"已用 "+r(D(M)(a.used_amount||0)),1),n("span",De,"总计 "+r(D(M)(a.amount)),1)]),n("div",Me,[i(X,{percentage:Math.min(a.usage_percentage||0,100),color:G(a.usage_percentage||0),"show-pivot":!1,"stroke-width":"6",class:"progress-bar"},null,8,["percentage","color"]),n("span",$e,r(Math.round(a.usage_percentage||0))+"%",1)]),n("div",Ve,[n("span",Se," 剩余 "+r(D(M)(a.amount-(a.used_amount||0))),1),n("span",Te,r(J(a)),1)])])],10,ge))),128))]))])]))])])}}},Oe=oe(Fe,[["__scopeId","data-v-ccebdb1a"]]);export{Oe as default};
