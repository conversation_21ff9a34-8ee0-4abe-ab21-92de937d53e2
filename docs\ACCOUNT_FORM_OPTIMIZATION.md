# 账户表单优化说明

## 更新内容

### 1. 字段排序调整
表单字段顺序已调整为：
1. **货币** - 首先选择货币类型
2. **账户类型** - 根据货币选择对应的账户类型
3. **账户名称** - 根据货币和账户类型显示预定义选项
4. **初始余额** - 最后输入金额

### 2. 账户类型优化
**保留的账户类型：**
- 🏦 银行账户 (bank)
- 💵 现金 (cash)
- 💰 投资账户 (investment)
- 🏪 电子钱包 (ewallet)

**删除的账户类型：**
- 💳 信用卡 (credit)
- 📱 其他 (other)

### 3. 账户名称预定义选项

#### 马币 (MYR) 账户名称：
- **银行账户**: Maybank, HLB, PBB, GXBank
- **电子钱包**: TNG eWallet
- **投资账户**: KDI, moomoo, EPF
- **现金**: Cash

#### 美金 (USD) 账户名称：
- **银行账户**: ABA
- **其他类型**: Cash (默认选项)

#### 其他货币 (CNY, EUR, GBP, JPY, SGD)：
- **所有类型**: Cash (默认选项)

### 4. 交互逻辑
- 选择货币后，账户类型和账户名称会重置
- 选择账户类型后，账户名称会重置
- 账户名称选项根据当前选择的货币和账户类型动态显示
- 所有字段都是必填项

### 5. 文件修改
- `mobile/src/views/Accounts/Add.vue` - 添加账户页面
- `mobile/src/views/Accounts/Edit.vue` - 编辑账户页面

## 使用方法

1. 打开添加/编辑账户页面
2. 首先选择货币（如 MYR 或 USD）
3. 然后选择账户类型（如银行账户）
4. 最后从预定义列表中选择账户名称（如 Maybank）
5. 输入初始余额
6. 保存账户

## 技术实现

- 使用 Vue 3 Composition API
- 响应式数据绑定
- 计算属性动态生成选项
- Vant UI 组件库的 Picker 选择器
- 表单验证和错误处理
