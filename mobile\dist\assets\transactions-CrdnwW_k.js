import{K as n}from"./index--MNqwREY.js";const s={getTransactions(t={}){return n.get("/transactions/",{params:t})},getTransaction(t){return n.get(`/transactions/${t}`)},createTransaction(t){return n.post("/transactions/",t)},updateTransaction(t,a){return n.put(`/transactions/${t}`,a)},deleteTransaction(t){return n.delete(`/transactions/${t}`)},getStats(t={}){return n.get("/transactions/stats",{params:t})},exportTransactions(t={}){return n.get("/transactions/export",{params:t})}};export{s as t};
