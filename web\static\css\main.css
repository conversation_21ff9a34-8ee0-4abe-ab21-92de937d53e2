/*******************************************
 *          主样式文件
 *******************************************/
@import 'variables.css';
@import 'components/sidebar.css';
@import 'components/dashboard.css';
@import 'components/transactions.css';
@import 'components/accounts.css';
@import 'components/budgets.css';
@import 'components/goals.css';
@import 'components/storage-mode.css';
@import 'components/loading.css';
@import 'components/sync-manager.css';
@import 'components/settings.css';
@import 'components/notifications.css';
@import 'components/attachments.css';
@import 'components/settings-about.css';
@import 'components/net-asset.css';

/* 页面整体布局及通用样式 */
html, body {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  background-color: var(--color-background);
  color: var(--color-text);
  height: 100%;
}

body {
  display: flex;
  min-height: 100vh;
  overflow: hidden; /* 防止整体滚动 */
}

*, *::before, *::after {
  box-sizing: inherit;
}

/* 右侧主内容样式 */
.main-content {
  flex: 1;
  padding: var(--spacing-lg) var(--spacing-xl);
  background-color: var(--color-background);
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow-y: auto; /* 允许内容区域滚动 */
}

/* 顶部条 */
.topbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius);
  background-color: var(--color-card-bg);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.topbar-left h1 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--color-primary);
}

.date {
  font-size: var(--font-size-sm);
  color: var(--color-text-light);
  margin-top: var(--spacing-xs);
  font-weight: 500;
}

.topbar-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.search-btn,
.notification-btn {
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-border);
  font-size: var(--font-size-md);
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  position: relative; /* 添加相对定位，作为通知徽章的定位基准 */
}

.search-btn:hover,
.notification-btn:hover {
  background-color: var(--color-primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.user-profile {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-border);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.user-profile:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.user-avatar {
  width: 40px;
  height: 40px;
  background-color: var(--color-primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  box-shadow: 0 2px 5px rgba(58, 90, 140, 0.3);
}

.user-name {
  font-weight: 600;
  font-size: var(--font-size-base);
  color: var(--color-text);
}

.user-role {
  font-size: var(--font-size-xs);
  color: var(--color-text-light);
  font-weight: 500;
}

/* 页面切换 */
.page {
  display: none;
}

.page.active {
  display: block;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-info {
    display: none;
  }
}
