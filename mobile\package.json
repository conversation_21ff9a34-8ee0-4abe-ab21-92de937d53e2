{"name": "finance-mobile", "version": "1.0.0", "description": "个人财务管理系统 - 手机版", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@vant/touch-emulator": "^1.4.0", "@vueuse/core": "^10.5.0", "axios": "^1.6.0", "echarts": "^5.4.3", "pinia": "^2.1.7", "vant": "^4.8.0", "vue": "^3.4.0", "vue-echarts": "^6.6.1", "vue-router": "^4.2.5", "vuedraggable": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-prettier": "^8.0.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "prettier": "^3.0.3", "vite": "^5.0.0", "vite-svg-loader": "^5.1.0"}}