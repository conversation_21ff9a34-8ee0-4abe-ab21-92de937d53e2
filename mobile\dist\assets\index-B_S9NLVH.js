import{a as d,l as m,m as ee,y as se,s as te,c,e as o,d as t,w as a,f as u,p as C,t as v,n as b,v as D,F as M,k as B,g as ne,o as l,i as _,q as L}from"./index--MNqwREY.js";import{useAccountsStore as oe}from"./accounts-bk1VVrk3.js";import{g as ae,f as w}from"./format-wz8GKlWC.js";import{_ as le}from"./_plugin-vue_export-helper-DlAUqK2U.js";const ie={class:"accounts-page"},re={class:"nav-actions"},de={class:"page-container"},ce={class:"total-assets-card"},ue={class:"assets-header"},ve={class:"assets-title"},pe={class:"assets-trend"},me={class:"assets-icon"},be={class:"total-amount"},_e={class:"currency"},fe={class:"amount"},ke={class:"assets-breakdown-container"},ye={class:"breakdown-item"},we={class:"breakdown-info"},ge={class:"breakdown-value"},Ce={class:"breakdown-item"},$e={class:"breakdown-info"},he={class:"breakdown-value"},Ae={class:"breakdown-info"},Se={class:"breakdown-value"},ze={class:"breakdown-item"},Me={class:"breakdown-info"},Be={class:"breakdown-value"},We={key:0,class:"breakdown-indicators"},xe={class:"assets-actions"},Ie={key:0,class:"loading-container"},Re={key:1,class:"empty-container"},Ne={class:"currency-picker"},De={class:"picker-header"},Le={__name:"index",setup(Pe){const P=ne(),$=oe(),h=d(!1),f=d(!1),r=d(localStorage.getItem("accounts-currency")||"MYR"),k=d(null),A=d(!1),W=d(0),x=d([0,1]),T=d([{code:"MYR",name:"马来西亚林吉特",symbol:"RM"},{code:"USD",name:"美元",symbol:"$"},{code:"CNY",name:"人民币",symbol:"¥"},{code:"EUR",name:"欧元",symbol:"€"},{code:"GBP",name:"英镑",symbol:"£"},{code:"JPY",name:"日元",symbol:"¥"},{code:"SGD",name:"新加坡元",symbol:"S$"}]),I=m(()=>$.accounts),y=m(()=>$.accounts.filter(s=>s.currency===r.value)),E=m(()=>y.value.reduce((s,e)=>s+(e.current_balance||e.initial_balance||0),0)),F=m(()=>y.value.filter(s=>s.type==="cash").reduce((s,e)=>s+(e.current_balance||e.initial_balance||0),0)),U=m(()=>y.value.filter(s=>s.type==="bank").reduce((s,e)=>s+(e.current_balance||e.initial_balance||0),0)),V=m(()=>y.value.filter(s=>s.type==="investment").reduce((s,e)=>s+(e.current_balance||e.initial_balance||0),0)),Y=m(()=>y.value.filter(s=>s.type==="ewallet").reduce((s,e)=>s+(e.current_balance||e.initial_balance||0),0));ee(async()=>{await J(),await se(),G()});const G=()=>{if(!k.value)return;const s=k.value,e=s.offsetWidth,i=s.scrollWidth;A.value=i>e,A.value&&s.addEventListener("scroll",q)},q=()=>{if(!k.value)return;const s=k.value,e=s.scrollLeft,i=s.offsetWidth,p=s.scrollWidth,S=Math.ceil(p/i),z=Math.round(e/(p-i)*(S-1));W.value=Math.max(0,Math.min(z,x.value.length-1))},J=async()=>{h.value=!0;try{await $.fetchAccountsWithBalances()}catch(s){console.error("加载账户失败:",s),te({message:"加载账户失败",type:"fail"})}finally{h.value=!1}},j=s=>({bank:"银行账户",cash:"现金",credit:"信用卡",investment:"投资账户",ewallet:"电子钱包",other:"其他"})[s]||s,H=s=>({bank:"🏦",cash:"💵",credit:"💳",investment:"📈",ewallet:"🏪",other:"💰"})[s]||"💰",K=s=>{P.push(`/account/detail/${s.id}`)},O=s=>{r.value=s,localStorage.setItem("accounts-currency",s),f.value=!1},Q=s=>s?Number(s).toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}):"0.00";return(s,e)=>{const i=u("van-button"),p=u("van-icon"),S=u("van-nav-bar"),z=u("van-loading"),R=u("van-cell"),N=u("van-cell-group"),X=u("van-floating-bubble"),Z=u("van-popup");return l(),c("div",ie,[o(S,{title:"账户管理",fixed:"",placeholder:"","safe-area-inset-top":""},{right:a(()=>[t("div",re,[o(i,{size:"mini",type:"primary",plain:"",onClick:e[0]||(e[0]=n=>f.value=!0),class:"currency-btn"},{default:a(()=>[_(v(r.value),1)]),_:1}),o(p,{name:"plus",size:"18",onClick:e[1]||(e[1]=n=>s.$router.push("/account/add"))})])]),_:1}),t("div",de,[t("div",ce,[t("div",ue,[t("div",ve,[e[11]||(e[11]=t("h3",null,"总资产",-1)),t("div",pe,[o(p,{name:"arrow-up",size:"12"}),e[10]||(e[10]=t("span",null,"****%",-1))])]),t("div",me,[o(p,{name:"gold-coin-o",size:"24"})])]),t("div",be,[t("span",_e,v(b(ae)(r.value)),1),t("span",fe,v(Q(E.value)),1)]),t("div",ke,[t("div",{class:"assets-breakdown",ref_key:"breakdownRef",ref:k},[t("div",ye,[e[13]||(e[13]=t("div",{class:"breakdown-icon cash"},"💵",-1)),t("div",we,[e[12]||(e[12]=t("span",{class:"breakdown-label"},"现金",-1)),t("span",ge,v(b(w)(F.value,r.value)),1)])]),t("div",Ce,[e[15]||(e[15]=t("div",{class:"breakdown-icon bank"},"🏦",-1)),t("div",$e,[e[14]||(e[14]=t("span",{class:"breakdown-label"},"银行",-1)),t("span",he,v(b(w)(U.value,r.value)),1)])]),t("div",{class:"breakdown-item clickable",onClick:e[2]||(e[2]=n=>s.$router.push("/investments"))},[e[17]||(e[17]=t("div",{class:"breakdown-icon investment"},"📈",-1)),t("div",Ae,[e[16]||(e[16]=t("span",{class:"breakdown-label"},"投资",-1)),t("span",Se,v(b(w)(V.value,r.value)),1)])]),t("div",ze,[e[19]||(e[19]=t("div",{class:"breakdown-icon ewallet"},"🏪",-1)),t("div",Me,[e[18]||(e[18]=t("span",{class:"breakdown-label"},"电子钱包",-1)),t("span",Be,v(b(w)(Y.value,r.value)),1)])])],512),A.value?(l(),c("div",We,[(l(!0),c(M,null,B(x.value,(n,g)=>(l(),c("div",{key:g,class:L(["indicator",{active:W.value===g}])},null,2))),128))])):D("",!0)]),t("div",xe,[o(i,{size:"small",type:"primary",plain:"",onClick:e[3]||(e[3]=n=>s.$router.push("/account/add"))},{default:a(()=>e[20]||(e[20]=[_(" 添加账户 ")])),_:1,__:[20]}),o(i,{size:"small",type:"primary",plain:"",onClick:e[4]||(e[4]=n=>s.$router.push("/investments"))},{default:a(()=>e[21]||(e[21]=[_(" 投资账号 ")])),_:1,__:[21]}),o(i,{size:"small",type:"primary",plain:"",onClick:e[5]||(e[5]=n=>s.$router.push("/statistics"))},{default:a(()=>e[22]||(e[22]=[_(" 查看统计 ")])),_:1,__:[22]})])]),h.value?(l(),c("div",Ie,[o(z,{size:"24px"}),e[23]||(e[23]=t("span",null,"加载中...",-1))])):I.value.length===0?(l(),c("div",Re,[e[25]||(e[25]=t("div",{class:"empty-icon"},"🏦",-1)),e[26]||(e[26]=t("p",null,"暂无账户",-1)),o(i,{type:"primary",size:"small",onClick:e[6]||(e[6]=n=>s.$router.push("/account/add"))},{default:a(()=>e[24]||(e[24]=[_(" 添加账户 ")])),_:1,__:[24]})])):(l(),C(N,{key:2,inset:"",title:"我的账户"},{default:a(()=>[(l(!0),c(M,null,B(I.value,n=>(l(),C(R,{key:n.id,title:n.name,label:j(n.type),value:b(w)(n.current_balance||n.initial_balance,n.currency),"is-link":"",onClick:g=>K(n)},{icon:a(()=>[t("div",{class:L(["account-icon",n.type])},v(H(n.type)),3)]),_:2},1032,["title","label","value","onClick"]))),128))]),_:1}))]),o(X,{axis:"xy",icon:"plus",gap:{x:24,y:80},onClick:e[7]||(e[7]=n=>s.$router.push("/account/add"))}),o(Z,{show:f.value,"onUpdate:show":e[9]||(e[9]=n=>f.value=n),position:"bottom",round:""},{default:a(()=>[t("div",Ne,[t("div",De,[e[28]||(e[28]=t("h3",null,"选择货币",-1)),o(i,{size:"mini",type:"primary",onClick:e[8]||(e[8]=n=>f.value=!1)},{default:a(()=>e[27]||(e[27]=[_(" 确定 ")])),_:1,__:[27]})]),o(N,null,{default:a(()=>[(l(!0),c(M,null,B(T.value,n=>(l(),C(R,{key:n.code,title:n.name,label:n.code,value:n.symbol,clickable:"",onClick:g=>O(n.code)},{"right-icon":a(()=>[r.value===n.code?(l(),C(p,{key:0,name:"success",color:"#1989fa"})):D("",!0)]),_:2},1032,["title","label","value","onClick"]))),128))]),_:1})])]),_:1},8,["show"])])}}},Ve=le(Le,[["__scopeId","data-v-e2f3c839"]]);export{Ve as default};
