const c=t=>({MYR:"RM",CNY:"¥",USD:"$",EUR:"€",GBP:"£",JPY:"¥",KRW:"₩",SGD:"S$"})[t]||t,u=(t,$="MYR",r=2)=>{if(t==null||isNaN(t))return`${c($)} 0.00`;const e=Number(t);return`${c($)} ${e.toFixed(r).replace(/\B(?=(\d{3})+(?!\d))/g,",")}`},m=(t,$="YYYY-MM-DD")=>{if(!t)return"";const r=new Date(t);if(isNaN(r.getTime()))return"";const e=r.getFullYear(),n=String(r.getMonth()+1).padStart(2,"0"),s=String(r.getDate()).padStart(2,"0"),a=String(r.getHours()).padStart(2,"0"),o=String(r.getMinutes()).padStart(2,"0"),Y=String(r.getSeconds()).padStart(2,"0");switch($){case"YYYY-MM-DD":return`${e}-${n}-${s}`;case"YYYY/MM/DD":return`${e}/${n}/${s}`;case"MM-DD":return`${n}-${s}`;case"YYYY-MM-DD HH:mm":return`${e}-${n}-${s} ${a}:${o}`;case"YYYY-MM-DD HH:mm:ss":return`${e}-${n}-${s} ${a}:${o}:${Y}`;case"HH:mm":return`${a}:${o}`;default:return`${e}-${n}-${s}`}};export{m as a,u as f,c as g};
