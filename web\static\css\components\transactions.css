/*******************************************
 *          Transactions 交易管理样式
 *******************************************/
.transaction-container {
  background-color: var(--color-card);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

.transaction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.transaction-title {
  font-size: var(--font-size-lg);
  font-weight: 700;
}

.transaction-form {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-xs);
  color: var(--color-text-light);
}

.form-group input,
.form-group select {
  padding: var(--spacing-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-base);
  background-color: #f9fafc;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(78, 115, 223, 0.1);
}

.form-submit {
  align-self: flex-end;
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-size: var(--font-size-base);
  transition: background-color 0.3s;
}

.form-submit:hover {
  background-color: var(--color-primary-dark);
}

.transaction-table {
  width: 100%;
  border-collapse: collapse;
}

.transaction-table th,
.transaction-table td {
  padding: var(--spacing-sm) var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
}

.transaction-table th {
  font-weight: 600;
  color: var(--color-text-light);
  font-size: var(--font-size-sm);
}

.transaction-table td {
  font-size: var(--font-size-base);
}

.transaction-table tr:last-child td {
  border-bottom: none;
}

.transaction-type {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 20px;
  font-size: var(--font-size-xs);
  background-color: var(--color-bg-highlight);
  color: var(--color-text-highlight);
}

.transaction-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.transaction-action-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--color-text-light);
  transition: color 0.3s;
}

.transaction-action-btn:hover {
  color: var(--color-primary);
}

.action-button {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  color: var(--color-text);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-button:hover {
  background-color: var(--color-hover);
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.action-button i {
  margin-right: var(--spacing-sm);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .transaction-form {
    grid-template-columns: 1fr;
  }
}
