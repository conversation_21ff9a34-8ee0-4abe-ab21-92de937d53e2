import{a as b,j as f,c as d,d as v,e as i,f as r,w as l,h as g,g as x,o as p,F as V,k as w,i as T,t as k}from"./index--MNqwREY.js";import{_ as y}from"./_plugin-vue_export-helper-DlAUqK2U.js";const C={class:"layout"},B={class:"main-content"},N={__name:"index",setup(A){const n=g(),u=x(),s=[{name:"dashboard",title:"仪表盘",icon:"chart-trending-o",path:"/"},{name:"transactions",title:"交易",icon:"bill-o",path:"/transactions"},{name:"accounts",title:"账户",icon:"credit-pay",path:"/accounts"},{name:"budgets",title:"预算",icon:"bag-o",path:"/budgets"},{name:"profile",title:"我的",icon:"user-o",path:"/profile"}],c=b("dashboard");f(()=>n.path,()=>{const o=n.path,t=s.find(a=>a.path===o);t&&(c.value=t.name)},{immediate:!0});const m=o=>{const t=s.find(a=>a.name===o);t&&n.path!==t.path&&u.push(t.path)};return(o,t)=>{const a=r("router-view"),_=r("van-tabbar-item"),h=r("van-tabbar");return p(),d("div",C,[v("div",B,[i(a)]),i(h,{modelValue:c.value,"onUpdate:modelValue":t[0]||(t[0]=e=>c.value=e),onChange:m,fixed:"",placeholder:"","safe-area-inset-bottom":""},{default:l(()=>[(p(),d(V,null,w(s,e=>i(_,{key:e.name,name:e.name,icon:e.icon,to:e.path},{default:l(()=>[T(k(e.title),1)]),_:2},1032,["name","icon","to"])),64))]),_:1},8,["modelValue"])])}}},D=y(N,[["__scopeId","data-v-eae49ba2"]]);export{D as default};
