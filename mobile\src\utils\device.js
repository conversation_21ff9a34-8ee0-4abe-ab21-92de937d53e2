/**
 * 设备检测和自适应工具
 */

// 检测是否为移动设备
export const isMobile = () => {
  const userAgent = navigator.userAgent.toLowerCase()
  const mobileKeywords = [
    'android', 'iphone', 'ipad', 'ipod', 'blackberry',
    'windows phone', 'mobile', 'webos', 'opera mini'
  ]

  // 检查User Agent中的移动设备关键词
  const hasMobileUA = mobileKeywords.some(keyword => userAgent.includes(keyword))

  // 检查是否为开发者工具的设备模拟
  const isDevToolsEmulation = window.navigator.userAgentData?.mobile ||
                              /Mobile|Android/i.test(navigator.userAgent)

  return hasMobileUA || isDevToolsEmulation
}

// 检测是否为平板设备
export const isTablet = () => {
  const userAgent = navigator.userAgent.toLowerCase()
  return /ipad|android(?!.*mobile)|tablet/i.test(userAgent)
}

// 检测是否为桌面设备
export const isDesktop = () => {
  return !isMobile() && !isTablet()
}

// 获取设备类型
export const getDeviceType = () => {
  if (isMobile()) return 'mobile'
  if (isTablet()) return 'tablet'
  return 'desktop'
}

// 检测屏幕尺寸
export const getScreenSize = () => {
  const width = window.innerWidth
  
  if (width < 768) return 'small'   // 手机
  if (width < 1024) return 'medium' // 平板
  return 'large'                    // 桌面
}

// 检测是否支持触摸
export const isTouchDevice = () => {
  return 'ontouchstart' in window ||
         navigator.maxTouchPoints > 0 ||
         navigator.msMaxTouchPoints > 0 ||
         // 检测开发者工具的设备模拟
         window.DeviceMotionEvent !== undefined ||
         window.DeviceOrientationEvent !== undefined
}

// 智能重定向逻辑
export const smartRedirect = () => {
  const currentPort = window.location.port
  const currentHost = window.location.hostname
  const deviceType = getDeviceType()
  const screenSize = getScreenSize()
  const touchDevice = isTouchDevice()

  console.log('设备检测结果:', {
    deviceType,
    screenSize,
    touchDevice,
    currentPort,
    userAgent: navigator.userAgent,
    screenWidth: window.innerWidth
  })

  // 如果已经在正确的端口，不需要重定向
  if (shouldUseMobileVersion() && currentPort === '3000') return false
  if (!shouldUseMobileVersion() && currentPort === '8000') return false

  // 应该使用手机版但不在3000端口，重定向到手机版
  if (shouldUseMobileVersion() && currentPort !== '3000') {
    const mobileUrl = `http://${currentHost}:3000${window.location.pathname}${window.location.search}`
    console.log('重定向到手机版:', mobileUrl)
    return mobileUrl
  }

  // 应该使用网页版但不在8000端口，重定向到网页版
  if (!shouldUseMobileVersion() && currentPort !== '8000') {
    const webUrl = `http://${currentHost}:8000${window.location.pathname}${window.location.search}`
    console.log('重定向到网页版:', webUrl)
    return webUrl
  }

  return false
}

// 判断是否应该使用手机版
export const shouldUseMobileVersion = () => {
  const deviceType = getDeviceType()
  const screenSize = getScreenSize()
  const touchDevice = isTouchDevice()
  const screenWidth = window.innerWidth
  const userAgent = navigator.userAgent.toLowerCase()

  // 明确的移动设备
  if (deviceType === 'mobile') return true

  // 检测开发者工具的移动设备模拟
  const isDevToolsMobile = screenWidth <= 768 && (
    // Chrome DevTools 设备模拟特征
    userAgent.includes('mobile') ||
    // 检查是否在开发者工具中（开发者工具会占用额外空间）
    window.outerHeight - window.innerHeight > 200 ||
    // 检查屏幕比例（手机通常是竖屏）
    (screenWidth <= 414 && window.innerHeight > window.innerWidth) ||
    // 强制检测：如果屏幕很小就认为是手机
    screenWidth <= 768
  )

  if (isDevToolsMobile) return true

  // 小屏幕设备 (强制使用手机版)
  if (screenWidth <= 480) return true

  // 中等屏幕 + 触摸设备
  if (screenWidth <= 768 && touchDevice) return true

  // 平板设备且屏幕较小
  if (deviceType === 'tablet' && screenWidth <= 768) return true

  return false
}

// 显示设备信息（调试用）
export const getDeviceInfo = () => {
  const screenWidth = window.innerWidth
  const userAgent = navigator.userAgent.toLowerCase()

  return {
    userAgent: navigator.userAgent,
    deviceType: getDeviceType(),
    screenSize: getScreenSize(),
    isMobile: isMobile(),
    isTablet: isTablet(),
    isDesktop: isDesktop(),
    isTouchDevice: isTouchDevice(),
    shouldUseMobile: shouldUseMobileVersion(),
    screenWidth: window.innerWidth,
    screenHeight: window.innerHeight,
    outerWidth: window.outerWidth,
    outerHeight: window.outerHeight,
    currentPort: window.location.port,
    currentHost: window.location.hostname,
    // 调试信息
    debug: {
      hasTouch: 'ontouchstart' in window,
      maxTouchPoints: navigator.maxTouchPoints,
      hasDeviceMotion: window.DeviceMotionEvent !== undefined,
      hasDeviceOrientation: window.DeviceOrientationEvent !== undefined,
      windowSizeDiff: window.outerHeight - window.innerHeight,
      isPortrait: window.innerHeight > window.innerWidth,
      userAgentMobile: userAgent.includes('mobile'),
      screenRatio: (window.innerHeight / window.innerWidth).toFixed(2)
    }
  }
}

// 自适应样式类
export const getAdaptiveClasses = () => {
  const classes = []
  
  classes.push(`device-${getDeviceType()}`)
  classes.push(`screen-${getScreenSize()}`)
  
  if (isTouchDevice()) classes.push('touch-device')
  if (isMobile()) classes.push('mobile-device')
  if (isTablet()) classes.push('tablet-device')
  if (isDesktop()) classes.push('desktop-device')
  
  return classes.join(' ')
}
