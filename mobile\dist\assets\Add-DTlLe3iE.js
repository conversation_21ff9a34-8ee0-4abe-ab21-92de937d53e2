import{a as v,r as le,l as d,m as se,s,c as re,e as a,d as u,w as l,f as r,g as ue,o as R,i as ce,t as N,p as ie,v as de,n as B}from"./index--MNqwREY.js";import{u as me}from"./transactions-F5TqZ8Qm.js";import{useAccountsStore as pe}from"./accounts-bk1VVrk3.js";import{g as ve}from"./categories-Zg0JKh5g.js";import{_ as fe}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{s as ye}from"./function-call-w7FI8rRn.js";import"./transactions-CrdnwW_k.js";const we={class:"add-transaction-page"},ge={class:"page-container"},Ce={class:"type-selector"},be={class:"tab-content"},ke={class:"tab-content"},Ve={class:"amount-section"},De={class:"amount-label"},he={class:"amount-input-container"},_e={class:"currency-symbol"},xe={__name:"Add",setup(Ae){const h=ue(),A=me(),c=pe(),_=v(!1),y=v(!1),w=v(!1),g=v(!1),C=v(!1),b=v(!1),t=le({type:"expense",amount:"",description:"",date:"",category:"",account:"",currency:"MYR",attachment:"",withdrawAccount:""}),f=v([]),F=new Date(2020,0,1),q=new Date(2030,11,31),G=[{text:"MYR - 马来西亚林吉特",value:"MYR"},{text:"USD - 美元",value:"USD"},{text:"CNY - 人民币",value:"CNY"},{text:"EUR - 欧元",value:"EUR"},{text:"GBP - 英镑",value:"GBP"},{text:"JPY - 日元",value:"JPY"},{text:"SGD - 新加坡元",value:"SGD"}],I=d(()=>{const o=c.accounts.find(p=>p.id===t.account),e=(o==null?void 0:o.name)||"";return ve(t.type,e)}),T=d(()=>c.accounts.map(o=>({text:`${o.name} (${o.type})`,value:o.id}))),W=d(()=>G),E=d(()=>c.accounts.filter(o=>o.currency==="MYR"&&(o.type==="bank"||o.type==="ewallet")&&o.id!==t.account).map(o=>({text:`${o.name} (${o.type})`,value:o.id}))),$=d(()=>{const o=c.accounts.find(e=>e.id===t.account);return o?`${o.name} (${o.type})`:""}),O=d(()=>{const o=c.accounts.find(e=>e.id===t.account);return(o==null?void 0:o.name)==="KDI"}),x=d(()=>O.value&&t.type==="expense"&&t.category==="出金"),U=d(()=>{const o=c.accounts.find(e=>e.id===t.withdrawAccount);return o?`${o.name} (${o.type})`:""}),J=d(()=>({MYR:"RM",USD:"$",CNY:"¥",EUR:"€",GBP:"£",JPY:"¥",SGD:"S$"})[t.currency]||t.currency);se(async()=>{const o=new Date;f.value=[o.getFullYear(),o.getMonth()+1,o.getDate()],t.date=S(o);try{await c.fetchAccounts(),c.accounts.length>0?t.account=c.accounts[0].id:s({message:"请先创建一个账户",type:"fail",duration:3e3})}catch(e){console.error("加载账户失败:",e),s("加载账户失败")}});const S=o=>{const e=o.getFullYear(),i=String(o.getMonth()+1).padStart(2,"0"),p=String(o.getDate()).padStart(2,"0");return`${e}-${i}-${p}`},K=o=>{t.category="",t.withdrawAccount=""},z=o=>{o&&parseFloat(o)<0&&(t.amount=Math.abs(parseFloat(o)).toString())},j=()=>{console.log("日期字段被点击"),console.log("当前selectedDate:",f.value),console.log("当前form.date:",t.date),y.value=!0},L=()=>{console.log("日期确认:",f.value);const[o,e,i]=f.value,p=new Date(o,e-1,i),k=S(p);console.log("格式化后的日期:",k),t.date=k,y.value=!1},H=({selectedOptions:o})=>{var e;t.category=((e=o[0])==null?void 0:e.value)||"",t.category!=="出金"&&(t.withdrawAccount=""),w.value=!1},Q=({selectedOptions:o})=>{var i;const e=t.account;t.account=((i=o[0])==null?void 0:i.value)||"",e!==t.account&&(t.category="",t.withdrawAccount=""),g.value=!1},X=({selectedOptions:o})=>{var e;t.currency=((e=o[0])==null?void 0:e.value)||"MYR",C.value=!1},Z=({selectedOptions:o})=>{var e;t.withdrawAccount=((e=o[0])==null?void 0:e.value)||"",b.value=!1},ee=()=>!t.amount||parseFloat(t.amount)<=0?(s("请输入有效金额"),!1):t.description.trim()?t.date?t.category?t.account?t.currency?x.value&&!t.withdrawAccount?(s("请选择出金账户"),!1):!0:(s("请选择货币"),!1):(s("请选择账户"),!1):(s("请选择交易分类"),!1):(s("请选择交易日期"),!1):(s("请输入交易描述"),!1),Y=async()=>{if(ee()){_.value=!0;try{const o={type:t.type,amount:parseFloat(t.amount),description:t.description.trim(),date:t.date,category:t.category,account:t.account,currency:t.currency,attachment:t.attachment.trim()};x.value&&t.withdrawAccount?await A.createKDIWithdrawTransaction(o,t.withdrawAccount):await A.createTransaction(o),s({message:"交易添加成功",type:"success"}),h.back()}catch(o){console.error("添加交易失败:",o),s({message:o.message||"添加交易失败",type:"fail"})}finally{_.value=!1}}},te=async()=>{t.amount||t.description||t.category?await ye({title:"确认离开",message:"您有未保存的数据，确定要离开吗？"})&&h.back():h.back()};return(o,e)=>{const i=r("van-button"),p=r("van-nav-bar"),k=r("van-icon"),M=r("van-tab"),oe=r("van-tabs"),m=r("van-field"),P=r("van-cell-group"),ne=r("van-form"),ae=r("van-date-picker"),V=r("van-popup"),D=r("van-picker");return R(),re("div",we,[a(p,{title:"添加交易","left-arrow":"",onClickLeft:te,fixed:"",placeholder:""},{right:l(()=>[a(i,{type:"primary",size:"small",loading:_.value,onClick:Y},{default:l(()=>e[24]||(e[24]=[ce(" 保存 ")])),_:1,__:[24]},8,["loading"])]),_:1}),u("div",ge,[u("div",Ce,[a(oe,{active:t.type,"onUpdate:active":e[0]||(e[0]=n=>t.type=n),onChange:K},{default:l(()=>[a(M,{title:"支出",name:"expense"},{default:l(()=>[u("div",be,[a(k,{name:"minus",class:"tab-icon expense"}),e[25]||(e[25]=u("span",null,"记录支出",-1))])]),_:1}),a(M,{title:"收入",name:"income"},{default:l(()=>[u("div",ke,[a(k,{name:"plus",class:"tab-icon income"}),e[26]||(e[26]=u("span",null,"记录收入",-1))])]),_:1})]),_:1},8,["active"])]),a(ne,{onSubmit:Y,class:"transaction-form"},{default:l(()=>[u("div",Ve,[u("div",De,N(t.type==="expense"?"支出金额":"收入金额"),1),u("div",he,[u("span",_e,N(J.value),1),a(m,{modelValue:t.amount,"onUpdate:modelValue":e[1]||(e[1]=n=>t.amount=n),type:"number",placeholder:"0.00",class:"amount-input",rules:[{required:!0,message:"请输入金额"}],onInput:z},null,8,["modelValue"])])]),a(P,{inset:"",title:"基本信息"},{default:l(()=>[a(m,{modelValue:t.description,"onUpdate:modelValue":e[2]||(e[2]=n=>t.description=n),label:"描述",placeholder:"请输入交易描述",rules:[{required:!0,message:"请输入交易描述"}],clearable:""},null,8,["modelValue"]),a(m,{modelValue:t.date,"onUpdate:modelValue":e[3]||(e[3]=n=>t.date=n),label:"日期",placeholder:"选择交易日期",readonly:"","is-link":"",onClick:j,rules:[{required:!0,message:"请选择交易日期"}]},null,8,["modelValue"]),a(m,{modelValue:t.category,"onUpdate:modelValue":e[4]||(e[4]=n=>t.category=n),label:"分类",placeholder:"选择交易分类",readonly:"","is-link":"",onClick:e[5]||(e[5]=n=>w.value=!0),rules:[{required:!0,message:"请选择交易分类"}]},null,8,["modelValue"]),a(m,{modelValue:$.value,"onUpdate:modelValue":e[6]||(e[6]=n=>$.value=n),label:"账户",placeholder:"选择账户",readonly:"","is-link":"",onClick:e[7]||(e[7]=n=>g.value=!0),rules:[{required:!0,message:"请选择账户"}]},null,8,["modelValue"]),a(m,{modelValue:t.currency,"onUpdate:modelValue":e[8]||(e[8]=n=>t.currency=n),label:"货币",placeholder:"选择货币",readonly:"","is-link":"",onClick:e[9]||(e[9]=n=>C.value=!0),rules:[{required:!0,message:"请选择货币"}]},null,8,["modelValue"]),x.value?(R(),ie(m,{key:0,modelValue:U.value,"onUpdate:modelValue":e[10]||(e[10]=n=>U.value=n),label:"出金账户",placeholder:"选择出金账户",readonly:"","is-link":"",onClick:e[11]||(e[11]=n=>b.value=!0),rules:[{required:!0,message:"请选择出金账户"}]},null,8,["modelValue"])):de("",!0)]),_:1}),a(P,{inset:"",title:"附加信息"},{default:l(()=>[a(m,{modelValue:t.attachment,"onUpdate:modelValue":e[12]||(e[12]=n=>t.attachment=n),label:"备注",type:"textarea",placeholder:"添加备注信息（可选）",rows:"3",autosize:"",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1})]),a(V,{show:y.value,"onUpdate:show":e[15]||(e[15]=n=>y.value=n),position:"bottom",round:""},{default:l(()=>[a(ae,{modelValue:f.value,"onUpdate:modelValue":e[13]||(e[13]=n=>f.value=n),title:"选择日期","min-date":B(F),"max-date":B(q),onConfirm:L,onCancel:e[14]||(e[14]=n=>y.value=!1)},null,8,["modelValue","min-date","max-date"])]),_:1},8,["show"]),a(V,{show:w.value,"onUpdate:show":e[17]||(e[17]=n=>w.value=n),position:"bottom"},{default:l(()=>[a(D,{columns:I.value,title:"选择分类",onConfirm:H,onCancel:e[16]||(e[16]=n=>w.value=!1)},null,8,["columns"])]),_:1},8,["show"]),a(V,{show:g.value,"onUpdate:show":e[19]||(e[19]=n=>g.value=n),position:"bottom"},{default:l(()=>[a(D,{columns:T.value,title:"选择账户",onConfirm:Q,onCancel:e[18]||(e[18]=n=>g.value=!1)},null,8,["columns"])]),_:1},8,["show"]),a(V,{show:C.value,"onUpdate:show":e[21]||(e[21]=n=>C.value=n),position:"bottom"},{default:l(()=>[a(D,{columns:W.value,title:"选择货币",onConfirm:X,onCancel:e[20]||(e[20]=n=>C.value=!1)},null,8,["columns"])]),_:1},8,["show"]),a(V,{show:b.value,"onUpdate:show":e[23]||(e[23]=n=>b.value=n),position:"bottom"},{default:l(()=>[a(D,{columns:E.value,title:"选择出金账户",onConfirm:Z,onCancel:e[22]||(e[22]=n=>b.value=!1)},null,8,["columns"])]),_:1},8,["show"])])}}},Ne=fe(xe,[["__scopeId","data-v-a274e646"]]);export{Ne as default};
