/*******************************************
 *          CSS变量定义
 *******************************************/
:root {
  /* 颜色 */
  --color-primary: #3a5a8c;        /* 更柔和的深蓝色 */
  --color-primary-dark: #2d4a76;   /* 深蓝色的暗色调 */
  --color-secondary: #1a1a2e;
  --color-text: #2c3e50;           /* 更深的文本颜色，提高对比度 */
  --color-text-light: #5d7185;     /* 更柔和的次要文本颜色 */
  --color-text-lighter: #8395a7;   /* 最浅的文本颜色 */
  --color-background: #eef2f7;     /* 更柔和的灰蓝色背景 */
  --color-card: #ffffff;           /* 纯白色卡片 */
  --color-card-bg: #ffffff;        /* 纯白色卡片背景 */
  --color-form-bg: #ffffff;        /* 表单背景也使用白色 */
  --color-input-bg: #ffffff;
  --color-table-header: #f5f8fc;   /* 更浅的表头背景 */
  --color-border: #dde5f0;         /* 更柔和的边框颜色 */
  --color-hover: #f5f8fc;          /* 更明显的悬停效果 */
  --color-danger: #e74c3c;

  /* 统计卡片徽章颜色 */
  --color-income: #3a5a8c;         /* 收入颜色 */
  --color-income-bg: #e9f0fa;      /* 收入背景色 */
  --color-expense: #5d7185;        /* 支出颜色 */
  --color-expense-bg: #f0f4f9;     /* 支出背景色 */

  /* 卡片阴影 */
  --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);  /* 更明显的卡片阴影 */

  /* 统一颜色 - 不再区分收入/支出颜色 */
  --color-text-highlight: #3a5a8c; /* 与主色调一致 */
  --color-bg-highlight: #f0f7ff;   /* 浅蓝色背景作为强调背景 */

  /* 尺寸 */
  --sidebar-width: 240px;
  --sidebar-width-mobile: 70px;
  --border-radius: 12px;
  --border-radius-sm: 8px;
  --border-radius-xs: 6px;

  /* 阴影 */
  --shadow-sm: 0 2px 6px rgba(0, 0, 0, 0.06);     /* 小元素阴影 */
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);     /* 中等元素阴影 */

  /* 字体大小 */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.85rem;
  --font-size-base: 0.9rem;
  --font-size-md: 1.1rem;
  --font-size-lg: 1.2rem;
  --font-size-xl: 1.4rem;
  --font-size-xxl: 1.8rem;

  /* 间距 */
  --spacing-xs: 5px;
  --spacing-sm: 10px;
  --spacing-md: 15px;
  --spacing-lg: 20px;
  --spacing-xl: 30px;
}
