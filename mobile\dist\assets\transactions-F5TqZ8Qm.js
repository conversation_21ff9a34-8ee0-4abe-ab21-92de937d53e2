const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/accounts-bk1VVrk3.js","assets/index--MNqwREY.js","assets/index-D6yS58S8.css","assets/budgets-DJh-v5Y8.js","assets/transactions-CrdnwW_k.js"])))=>i.map(i=>d[i]);
import{J as D,_ as A}from"./index--MNqwREY.js";import{t as g}from"./transactions-CrdnwW_k.js";const C=D("transactions",{state:()=>({transactions:[],loading:!1,stats:{totalIncome:0,totalExpense:0,netWorth:0,incomeChange:0,expenseChange:0,netWorthChange:0,todayExpense:0,monthlyBudgetRemaining:0},filters:{currency:"ALL",type:"ALL",category:"ALL",dateRange:null}}),getters:{filteredTransactions:e=>{let t=[...e.transactions];return e.filters.currency!=="ALL"&&(t=t.filter(n=>n.currency===e.filters.currency)),e.filters.type!=="ALL"&&(t=t.filter(n=>n.type===e.filters.type)),e.filters.category!=="ALL"&&(t=t.filter(n=>n.category===e.filters.category)),t.sort((n,s)=>new Date(s.date)-new Date(n.date))},recentTransactions:e=>e.transactions.sort((t,n)=>new Date(n.date)-new Date(t.date)).slice(0,10),transactionsByType:e=>{const t=e.transactions.filter(s=>s.type==="income"),n=e.transactions.filter(s=>s.type==="expense");return{income:t,expense:n}},expensesByCategory:e=>{const t=e.transactions.filter(s=>s.type==="expense"),n={};return t.forEach(s=>{const o=s.category;n[o]||(n[o]={category:o,amount:0,count:0}),n[o].amount+=s.amount,n[o].count+=1}),Object.values(n).sort((s,o)=>o.amount-s.amount)}},actions:{async fetchTransactions(e={}){this.loading=!0;try{console.log("Store: 开始调用 transactionsAPI.getTransactions()");const t=await g.getTransactions(e);console.log("Store: API响应:",t);let n=[];return t&&t.transactions?n=t.transactions:Array.isArray(t)?n=t:t&&t.data&&(n=t.data),console.log("Store: 处理后的交易数据:",n),this.transactions=Array.isArray(n)?n:[],console.log("Store: 设置到store的交易数据:",this.transactions),await this.calculateStats(),this.transactions}catch(t){throw console.error("获取交易列表失败:",t),this.transactions=[],t}finally{this.loading=!1}},async createTransaction(e){try{console.log("Store: 创建交易数据:",e);const t=await g.createTransaction(e);return console.log("Store: 创建交易响应:",t),t&&t.success&&await this.fetchTransactions(),t}catch(t){throw console.error("创建交易失败:",t),t}},async createKDIWithdrawTransaction(e,t){try{console.log("Store: 创建KDI出金双向交易:",{kdiTransactionData:e,withdrawAccountId:t});const{useAccountsStore:n}=await A(async()=>{const{useAccountsStore:u}=await import("./accounts-bk1VVrk3.js");return{useAccountsStore:u}},__vite__mapDeps([0,1,2])),s=n();(!s.accounts||s.accounts.length===0)&&await s.fetchAccounts();const o=s.accounts.find(u=>u.id===t);if(!o)throw new Error("出金账户不存在");const c=await g.createTransaction(e);console.log("Store: KDI出金记录创建响应:",c);const h={type:"income",amount:e.amount,description:`来自KDI出金 - ${e.description}`,date:e.date,category:"投资收益",account:t,currency:o.currency,attachment:e.attachment},i=await g.createTransaction(h);return console.log("Store: 目标账户收入记录创建响应:",i),c&&c.success&&i&&i.success&&await this.fetchTransactions(),{success:!0,kdiTransaction:c,targetTransaction:i}}catch(n){throw console.error("创建KDI出金双向交易失败:",n),n}},async updateTransaction(e,t){try{const n=await g.updateTransaction(e,t),s=n.data||n;if(s){const o=this.transactions.findIndex(c=>c.id===e);o!==-1&&(this.transactions[o]=s),await this.calculateStats()}return s}catch(n){throw console.error("更新交易失败:",n),n}},async deleteTransaction(e){try{await g.deleteTransaction(e),this.transactions=this.transactions.filter(t=>t.id!==e),await this.calculateStats()}catch(t){throw console.error("删除交易失败:",t),t}},async calculateStats(){const e=new Date,t=e.getMonth(),n=e.getFullYear(),s=new Date(e.getFullYear(),e.getMonth(),e.getDate()),o=this.transactions.filter(a=>{const r=new Date(a.date);return new Date(r.getFullYear(),r.getMonth(),r.getDate()).getTime()===s.getTime()}),c=this.transactions.filter(a=>{const r=new Date(a.date);return r.getMonth()===t&&r.getFullYear()===n}),h=t===0?11:t-1,i=t===0?n-1:n,u=this.transactions.filter(a=>{const r=new Date(a.date);return r.getMonth()===h&&r.getFullYear()===i}),l=o.filter(a=>a.type==="expense").reduce((a,r)=>a+r.amount,0),y=this.transactions.filter(a=>a.type==="income").reduce((a,r)=>a+r.amount,0),d=this.transactions.filter(a=>a.type==="expense").reduce((a,r)=>a+r.amount,0),p=c.filter(a=>a.type==="income").reduce((a,r)=>a+r.amount,0),f=c.filter(a=>a.type==="expense").reduce((a,r)=>a+r.amount,0),m=u.filter(a=>a.type==="income").reduce((a,r)=>a+r.amount,0),w=u.filter(a=>a.type==="expense").reduce((a,r)=>a+r.amount,0),S=this.calculatePercentageChange(m,p),x=this.calculatePercentageChange(w,f),T=this.calculatePercentageChange(m-w,p-f),M=await this.calculateMonthlyBudgetRemaining(f);this.stats.todayExpense=l,this.stats.monthlyBudgetRemaining=M,this.stats.totalIncome=y,this.stats.totalExpense=d,this.stats.netWorth=y-d,this.stats.incomeChange=S,this.stats.expenseChange=x,this.stats.netWorthChange=T,console.log("统计数据计算完成:",{todayExpense:l,monthlyBudgetRemaining:M,totalIncome:y,totalExpense:d,netWorth:y-d,currentMonth:{income:p,expense:f},lastMonth:{income:m,expense:w},changes:{incomeChange:S,expenseChange:x,netWorthChange:T}})},calculatePercentageChange(e,t){return e===0?t>0?100:0:Math.round((t-e)/e*100)},async calculateMonthlyBudgetRemaining(e){try{const{useBudgetsStore:t}=await A(async()=>{const{useBudgetsStore:l}=await import("./budgets-DJh-v5Y8.js");return{useBudgetsStore:l}},__vite__mapDeps([3,1,2,4])),n=t();(!n.budgets||n.budgets.length===0)&&await n.fetchBudgets();const s=new Date,o=s.getFullYear(),c=s.getMonth()+1,h=`${o}-${String(c).padStart(2,"0")}-${String(s.getDate()).padStart(2,"0")}`;let i=0;return n.budgets&&n.budgets.length>0&&n.budgets.forEach(l=>{l.start_date<=h&&l.end_date>=h&&(i+=l.amount||0)}),i-e}catch(t){return console.error("计算本月预算剩余失败:",t),0}},setFilter(e,t){this.filters[e]=t},clearFilters(){this.filters={currency:"ALL",type:"ALL",category:"ALL",dateRange:null}}}});export{C as u};
