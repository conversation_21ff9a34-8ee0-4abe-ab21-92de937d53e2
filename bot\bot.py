#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Telegram机器人 - 内联键盘交互式记账
支持账户选择和附件功能
主要bot文件
"""

import requests
import json
import time
import logging
import base64
import os
import uuid
from datetime import datetime
from bot.config.bot_config import config
from bot.handlers.transaction_handler import transaction_handler
from bot.database import bot_db
from bot.website_auth import website_auth

# 配置日志
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(config.LOG_FILE)
    ]
)
logger = logging.getLogger(__name__)

class InteractiveTelegramBot:
    """支持内联键盘的交互式Telegram机器人"""

    def __init__(self):
        self.bot_token = config.TELEGRAM_BOT_TOKEN
        self.enabled = config.TELEGRAM_ENABLED and bool(self.bot_token)
        self.api_url = f"https://api.telegram.org/bot{self.bot_token}" if self.bot_token else ""
        self.last_update_id = 0
        self.running = False

        # 用户状态管理
        self.user_states = {}

        # 类别定义（与网站保持一致）
        self.income_categories = ['工资', '奖金', '投资收益', '礼金', '退款', '其他收入']
        self.expense_categories = ['储蓄', '固定', '流动', '债务']

        # 货币定义
        self.currencies = ['MYR', 'CNY', 'USD', 'EUR', 'GBP', 'JPY', 'KRW', 'SGD']

        if not self.enabled:
            logger.warning("Telegram机器人未启用或Bot Token未配置")

    def send_message(self, chat_id, text, reply_markup=None):
        """发送消息"""
        try:
            url = f"{self.api_url}/sendMessage"
            data = {
                'chat_id': chat_id,
                'text': text,
                'parse_mode': 'Markdown'
            }

            if reply_markup:
                data['reply_markup'] = json.dumps(reply_markup)

            response = requests.post(url, data=data, timeout=3)
            if response.status_code == 200:
                result = response.json()
                if result.get('ok'):
                    return result.get('result')
                else:
                    logger.error(f"发送消息失败: {result.get('description')}")
            else:
                logger.error(f"HTTP请求失败: {response.status_code}")
            return None
        except Exception as e:
            logger.error(f"发送消息时出错: {str(e)}")
            return None

    def edit_message(self, chat_id, message_id, text, reply_markup=None):
        """编辑消息"""
        try:
            url = f"{self.api_url}/editMessageText"
            data = {
                'chat_id': chat_id,
                'message_id': message_id,
                'text': text,
                'parse_mode': 'Markdown'
            }

            if reply_markup:
                data['reply_markup'] = json.dumps(reply_markup)

            response = requests.post(url, data=data, timeout=1.5)  # 减少超时时间
            return response.status_code == 200
        except Exception as e:
            logger.error(f"编辑消息时出错: {str(e)}")
            return False

    def edit_message_async(self, chat_id, message_id, text, reply_markup=None):
        """异步编辑消息（最快版本）"""
        import threading
        def edit_async():
            self.edit_message(chat_id, message_id, text, reply_markup)
        threading.Thread(target=edit_async, daemon=True).start()

    def answer_callback_query(self, callback_query_id, text=None):
        """回答回调查询"""
        try:
            url = f"{self.api_url}/answerCallbackQuery"
            data = {'callback_query_id': callback_query_id}
            if text:
                data['text'] = text

            response = requests.post(url, data=data, timeout=3)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"回答回调查询时出错: {str(e)}")
            return False

    def get_accounts(self, chat_id=None):
        """获取账户列表（超快版本 - 直接数据库查询）"""
        try:
            import time
            import sqlite3
            import os

            # 获取用户ID
            user_id = None
            if chat_id:
                user_binding = self.get_user_binding_info(chat_id)
                logger.info(f"获取账户 - 用户绑定信息: {user_binding}")
                if user_binding and user_binding.get('is_bound'):
                    user_id = user_binding['website_user_id']
                    logger.info(f"获取账户 - 使用用户ID: {user_id}")
                else:
                    logger.warning(f"获取账户 - 用户 {chat_id} 未绑定或绑定无效")
                    user_id = None

            # 使用用户特定的缓存键
            cache_key = f'accounts_cache_{user_id}' if user_id else 'accounts_cache'
            cache_time_key = f'accounts_cache_time_{user_id}' if user_id else 'accounts_cache_time'

            # 使用缓存减少数据库调用 (缩短缓存时间到30秒)
            if hasattr(self, cache_key) and hasattr(self, cache_time_key):
                if time.time() - getattr(self, cache_time_key) < 30:  # 30秒缓存
                    logger.info(f"获取账户 - 使用缓存数据")
                    return getattr(self, cache_key)

            # 确定正确的数据库路径
            # __file__ 是 bot/bot.py
            # 需要回到项目根目录，然后进入data目录: ../data/finance.db
            current_dir = os.path.dirname(__file__)  # bot
            project_dir = os.path.dirname(current_dir)   # 项目根目录
            db_path = os.path.join(project_dir, 'data', 'finance.db')
            logger.info(f"获取账户 - 尝试连接数据库: {db_path}")

            if not os.path.exists(db_path):
                logger.error(f"数据库文件不存在: {db_path}")
                # 返回默认账户避免失败
                return [{'id': 'default', 'name': '默认账户', 'type': 'cash', 'currency': 'MYR'}]

            conn = sqlite3.connect(db_path)
            conn.row_factory = sqlite3.Row

            # 根据用户ID过滤账户
            if user_id:
                logger.info(f"获取账户 - 查询用户 {user_id} 的账户")
                accounts_data = conn.execute('SELECT * FROM accounts WHERE user_id = ? ORDER BY name', (user_id,)).fetchall()
            else:
                logger.info("获取账户 - 查询所有账户（无用户过滤）")
                accounts_data = conn.execute('SELECT * FROM accounts ORDER BY name').fetchall()

            conn.close()

            accounts = [dict(account) for account in accounts_data]
            logger.info(f"获取账户 - 找到 {len(accounts)} 个账户: {[acc['name'] for acc in accounts]}")

            # 缓存结果
            setattr(self, cache_key, accounts)
            setattr(self, cache_time_key, time.time())

            if accounts:
                return accounts
            else:
                logger.warning("获取账户 - 没有找到账户，返回默认账户")
                return [{'id': 'default', 'name': '默认账户', 'type': 'cash', 'currency': 'MYR'}]

        except Exception as e:
            logger.error(f"获取账户列表失败: {str(e)}")
            # 返回默认账户避免失败
            return [{'id': 'default', 'name': '默认账户', 'type': 'cash', 'currency': 'MYR'}]

    def create_inline_keyboard(self, buttons):
        """创建内联键盘"""
        return {
            'inline_keyboard': buttons
        }

    def create_transaction_type_keyboard(self):
        """创建交易类型选择键盘"""
        return self.create_inline_keyboard([
            [
                {'text': '💰 收入', 'callback_data': 'type_income'},
                {'text': '💸 支出', 'callback_data': 'type_expense'}
            ],
            [
                {'text': '🔄 转账', 'callback_data': 'type_transfer'}
            ]
        ])

    def create_category_keyboard(self, transaction_type):
        """创建类别选择键盘"""
        categories = self.income_categories if transaction_type == 'income' else self.expense_categories

        buttons = []
        # 每行2个按钮
        for i in range(0, len(categories), 2):
            row = []
            for j in range(2):
                if i + j < len(categories):
                    category = categories[i + j]
                    row.append({'text': category, 'callback_data': f'category_{category}'})
            buttons.append(row)

        # 添加返回按钮
        buttons.append([{'text': '⬅️ 返回', 'callback_data': 'back_to_type'}])

        return self.create_inline_keyboard(buttons)

    def create_account_keyboard(self, chat_id=None):
        """创建账户选择键盘"""
        accounts = self.get_accounts(chat_id)
        logger.info(f"创建账户键盘 - 获取到 {len(accounts)} 个账户")

        buttons = []
        for account in accounts:
            # 根据账户类型添加图标
            icon = '🏦' if account['type'] == 'bank' else '💵' if account['type'] == 'cash' else '💳'
            text = f"{icon} {account['name']}"
            callback_data = f'account_{account["id"]}'
            logger.info(f"创建账户按钮 - 账户: {account['name']}, ID: {account['id']}, 回调数据: {callback_data}")
            buttons.append([{'text': text, 'callback_data': callback_data}])

        # 添加返回按钮
        buttons.append([{'text': '⬅️ 返回', 'callback_data': 'back_to_category'}])

        logger.info(f"创建账户键盘 - 总共创建了 {len(buttons)} 个按钮")
        return self.create_inline_keyboard(buttons)

    def create_attachment_keyboard(self):
        """创建附件选择键盘"""
        return self.create_inline_keyboard([
            [
                {'text': '📷 上传图片', 'callback_data': 'attachment_photo'},
                {'text': '📎 上传文件', 'callback_data': 'attachment_file'}
            ],
            [
                {'text': '⏭️ 跳过', 'callback_data': 'attachment_skip'},
                {'text': '⬅️ 返回', 'callback_data': 'back_to_description'}
            ]
        ])

    def create_confirm_keyboard(self):
        """创建确认键盘"""
        return self.create_inline_keyboard([
            [
                {'text': '✅ 确认记账', 'callback_data': 'confirm_transaction'},
                {'text': '✏️ 修改', 'callback_data': 'edit_transaction'}
            ],
            [
                {'text': '❌ 取消', 'callback_data': 'cancel_transaction'}
            ]
        ])

    def get_user_state(self, chat_id):
        """获取用户状态"""
        return self.user_states.get(chat_id, {})

    def set_user_state(self, chat_id, state):
        """设置用户状态"""
        self.user_states[chat_id] = state

    def clear_user_state(self, chat_id):
        """清除用户状态"""
        if chat_id in self.user_states:
            del self.user_states[chat_id]

    def check_user_binding(self, chat_id):
        """检查用户是否已绑定网站账户"""
        return bot_db.is_user_bound(chat_id)

    def get_user_binding_info(self, chat_id):
        """获取用户绑定信息"""
        return bot_db.get_user_binding(chat_id)

    def clear_accounts_cache(self, chat_id=None):
        """清除账户缓存"""
        try:
            if chat_id:
                # 获取用户ID
                user_binding = self.get_user_binding_info(chat_id)
                if user_binding and user_binding.get('is_bound'):
                    user_id = user_binding['website_user_id']
                    cache_key = f'accounts_cache_{user_id}'
                    cache_time_key = f'accounts_cache_time_{user_id}'

                    if hasattr(self, cache_key):
                        delattr(self, cache_key)
                    if hasattr(self, cache_time_key):
                        delattr(self, cache_time_key)

                    logger.info(f"已清除用户 {chat_id} 的账户缓存")
            else:
                # 清除所有缓存
                attrs_to_remove = []
                for attr in dir(self):
                    if attr.startswith('accounts_cache'):
                        attrs_to_remove.append(attr)

                for attr in attrs_to_remove:
                    delattr(self, attr)

                logger.info("已清除所有账户缓存")
        except Exception as e:
            logger.error(f"清除账户缓存失败: {str(e)}")

    def start_transaction(self, chat_id):
        """开始记账流程"""
        # 检查用户是否已绑定
        if not self.check_user_binding(chat_id):
            self.send_binding_prompt(chat_id)
            return None

        # 清除账户缓存以获取最新数据
        self.clear_accounts_cache(chat_id)

        # 清除之前的状态
        self.clear_user_state(chat_id)

        # 设置初始状态
        self.set_user_state(chat_id, {
            'step': 'type',
            'data': {}
        })

        text = "💰 *开始记账*\n\n请选择交易类型："
        keyboard = self.create_transaction_type_keyboard()

        return self.send_message(chat_id, text, keyboard)

    def send_binding_prompt(self, chat_id):
        """发送绑定提示"""
        text = """
🔗 *账户绑定*

您需要先绑定网站账户才能使用记账功能。

请使用以下命令绑定您的网站用户名：
`/bind 您的用户名`

例如：`/bind admin`

如果您还没有网站账户，请先到网站注册：
https://your-website.com
        """.strip()

        self.send_message(chat_id, text)

    def handle_bind_command(self, chat_id, command):
        """处理绑定命令"""
        try:
            # 解析命令：/bind username
            parts = command.split()
            if len(parts) != 2:
                self.send_message(chat_id, """
❌ *命令格式错误*

正确格式：`/bind 您的用户名`

例如：`/bind admin`
                """.strip())
                return

            username = parts[1].strip()

            # 检查是否已经绑定
            if self.check_user_binding(chat_id):
                binding_info = self.get_user_binding_info(chat_id)
                self.send_message(chat_id, f"""
🔗 *您已绑定账户*

当前绑定的用户名：`{binding_info['website_username']}`

如需更换绑定，请先使用 `/unbind` 解绑
                """.strip())
                return

            # 验证网站用户名
            user_info = website_auth.verify_username(username)
            if not user_info:
                self.send_message(chat_id, f"""
❌ *用户名不存在*

用户名 `{username}` 在网站中不存在。

请检查用户名是否正确，或先到网站注册账户。
                """.strip())
                return

            # 执行绑定
            success = bot_db.bind_website_user(chat_id, user_info['id'], user_info['username'])

            if success:
                self.send_message(chat_id, f"""
✅ *绑定成功！*

已成功绑定网站用户：`{user_info['username']}`
显示名称：{user_info['display_name']}

现在您可以使用所有机器人功能了！

💰 发送 '记账' 开始记账
📊 发送 '余额' 查询余额
                """.strip())
                logger.info(f"用户 {chat_id} 成功绑定网站用户: {username}")
            else:
                self.send_message(chat_id, """
❌ *绑定失败*

该网站用户可能已被其他Telegram用户绑定。

每个网站用户只能绑定一个Telegram账户。
                """.strip())

        except Exception as e:
            logger.error(f"处理绑定命令时出错: {str(e)}")
            self.send_message(chat_id, "❌ 绑定过程中出错，请稍后重试")

    def handle_unbind_command(self, chat_id):
        """处理解绑命令"""
        try:
            # 检查是否已绑定
            if not self.check_user_binding(chat_id):
                self.send_message(chat_id, """
ℹ️ *未绑定账户*

您还没有绑定网站账户。

使用 `/bind 用户名` 来绑定账户。
                """.strip())
                return

            # 获取绑定信息
            binding_info = self.get_user_binding_info(chat_id)

            # 执行解绑
            success = bot_db.unbind_website_user(chat_id)

            if success:
                self.send_message(chat_id, f"""
✅ *解绑成功*

已解除与网站用户 `{binding_info['website_username']}` 的绑定。

使用 `/bind 用户名` 可重新绑定账户。
                """.strip())
                logger.info(f"用户 {chat_id} 解绑网站用户: {binding_info['website_username']}")
            else:
                self.send_message(chat_id, "❌ 解绑失败，请稍后重试")

        except Exception as e:
            logger.error(f"处理解绑命令时出错: {str(e)}")
            self.send_message(chat_id, "❌ 解绑过程中出错，请稍后重试")

    def handle_status_command(self, chat_id):
        """处理状态查询命令"""
        try:
            if self.check_user_binding(chat_id):
                binding_info = self.get_user_binding_info(chat_id)
                # 获取网站用户详细信息
                user_info = website_auth.get_user_by_id(binding_info['website_user_id'])

                if user_info:
                    is_admin = website_auth.is_admin_user(user_info['id'])
                    admin_status = "是" if is_admin else "否"

                    self.send_message(chat_id, f"""
🔗 *账户绑定状态*

✅ 已绑定网站账户

👤 *用户信息*：
• 用户名：`{user_info['username']}`
• 显示名称：{user_info['display_name']}
• 邮箱：{user_info['email']}
• 管理员：{admin_status}

💰 您可以使用所有机器人功能！
                    """.strip())
                else:
                    self.send_message(chat_id, """
⚠️ *绑定异常*

您的绑定信息存在异常，建议重新绑定。

使用 `/unbind` 解绑后重新 `/bind 用户名`
                    """.strip())
            else:
                self.send_message(chat_id, """
❌ *未绑定账户*

您还没有绑定网站账户。

使用 `/bind 用户名` 来绑定账户。
                """.strip())

        except Exception as e:
            logger.error(f"处理状态查询时出错: {str(e)}")
            self.send_message(chat_id, "❌ 查询状态时出错")

    def handle_callback_query(self, callback_query):
        """处理回调查询（优化版本）"""
        try:
            chat_id = callback_query['message']['chat']['id']
            message_id = callback_query['message']['message_id']
            data = callback_query['data']
            callback_query_id = callback_query['id']

            # 立即回答回调查询，避免用户等待
            import threading
            threading.Thread(target=self.answer_callback_query, args=(callback_query_id,), daemon=True).start()

            # 获取用户状态（使用内存缓存）
            user_state = self.get_user_state(chat_id)

            # 使用字典映射提高查找效率
            callback_handlers = {
                'type_': self.handle_type_selection,
                'category_': self.handle_category_selection,
                'account_': self.handle_account_selection,
                'attachment_': self.handle_attachment_selection,
                'confirm_transaction': lambda cid, mid, _d, us: self.handle_transaction_confirmation(cid, mid, us),
                'skip_description': lambda cid, mid, _d, us: self.handle_skip_description(cid, mid, us),
                'back_': self.handle_back_navigation,
                'cancel_transaction': lambda cid, mid, _d, _us: self.handle_transaction_cancellation(cid, mid),
                'edit_transaction': lambda cid, mid, _d, us: self.handle_edit_transaction(cid, mid, us)
            }

            # 快速查找并执行处理函数
            handler_found = False
            for prefix, handler in callback_handlers.items():
                if data.startswith(prefix) or data == prefix:
                    if prefix in ['confirm_transaction', 'skip_description', 'cancel_transaction', 'edit_transaction']:
                        handler(chat_id, message_id, data, user_state)
                    else:
                        handler(chat_id, message_id, data, user_state)
                    handler_found = True
                    break

            if not handler_found:
                logger.warning(f"未知的回调数据: {data}")

        except Exception as e:
            logger.error(f"处理回调查询时出错: {str(e)}")

    def handle_type_selection(self, chat_id, message_id, data, user_state):
        """处理交易类型选择"""
        transaction_type = data.split('_')[1]  # type_income -> income

        user_state['data']['type'] = transaction_type
        user_state['step'] = 'category'
        self.set_user_state(chat_id, user_state)

        type_text = "收入" if transaction_type == 'income' else "支出" if transaction_type == 'expense' else "转账"
        text = f"💰 *记账 - {type_text}*\n\n请选择类别："
        keyboard = self.create_category_keyboard(transaction_type)

        self.edit_message(chat_id, message_id, text, keyboard)

    def handle_category_selection(self, chat_id, message_id, data, user_state):
        """处理类别选择"""
        category = data.split('_', 1)[1]  # category_餐饮 -> 餐饮

        user_state['data']['category'] = category
        user_state['step'] = 'account'
        self.set_user_state(chat_id, user_state)

        text = f"💰 *记账 - {user_state['data']['type']} - {category}*\n\n请选择账户："
        keyboard = self.create_account_keyboard(chat_id)

        self.edit_message(chat_id, message_id, text, keyboard)

    def handle_account_selection(self, chat_id, message_id, data, user_state):
        """处理账户选择"""
        # 从回调数据中提取账户ID，处理可能的双重前缀问题
        # 例如: account_account_xxx -> account_xxx 或 account_123 -> 123
        parts = data.split('_', 1)  # 只分割第一个下划线
        if len(parts) > 1:
            account_id = parts[1]  # 获取下划线后的所有内容
        else:
            account_id = data

        logger.info(f"处理账户选择 - 回调数据: {data}, 提取的账户ID: {account_id}")

        # 获取账户信息
        accounts = self.get_accounts(chat_id)
        logger.info(f"处理账户选择 - 可用账户: {[{'id': acc['id'], 'name': acc['name']} for acc in accounts]}")

        # 尝试匹配账户ID（支持字符串和整数类型）
        account = None
        for acc in accounts:
            if str(acc['id']) == str(account_id):
                account = acc
                break

        logger.info(f"处理账户选择 - 匹配结果: {account['name'] if account else '未找到'}")

        if account:
            user_state['data']['account'] = account['id']  # 使用原始ID
            user_state['data']['account_name'] = account['name']
            user_state['data']['currency'] = account['currency']
            user_state['step'] = 'amount'
            self.set_user_state(chat_id, user_state)

            text = f"💰 *记账信息*\n\n"
            text += f"类型：{user_state['data']['type']}\n"
            text += f"类别：{user_state['data']['category']}\n"
            text += f"账户：{account['name']}\n\n"
            text += "请输入金额："

            self.edit_message(chat_id, message_id, text)
        else:
            logger.error(f"处理账户选择 - 未找到账户ID: {account_id}")
            self.send_message(chat_id, "❌ 账户选择失败，请重试")

    def handle_attachment_selection(self, chat_id, message_id, data, user_state):
        """处理附件选择"""
        attachment_type = data.split('_')[1]  # attachment_photo -> photo

        if attachment_type == 'skip':
            # 跳过附件，直接确认
            self.show_transaction_confirmation(chat_id, message_id, user_state)
        else:
            user_state['step'] = f'attachment_{attachment_type}'
            self.set_user_state(chat_id, user_state)

            if attachment_type == 'photo':
                text = "📷 请发送图片："
            else:
                text = "📎 请发送文件："

            self.edit_message(chat_id, message_id, text)

    def handle_back_navigation(self, chat_id, message_id, data, user_state):
        """处理返回导航"""
        back_to = data.split('_', 2)[2]  # back_to_type -> type

        if back_to == 'type':
            user_state['step'] = 'type'
            user_state['data'] = {}
            self.set_user_state(chat_id, user_state)

            text = "💰 *开始记账*\n\n请选择交易类型："
            keyboard = self.create_transaction_type_keyboard()
            self.edit_message(chat_id, message_id, text, keyboard)

        elif back_to == 'category':
            user_state['step'] = 'category'
            # 保留type数据
            transaction_type = user_state['data'].get('type')
            user_state['data'] = {'type': transaction_type}
            self.set_user_state(chat_id, user_state)

            type_text = "收入" if transaction_type == 'income' else "支出"
            text = f"💰 *记账 - {type_text}*\n\n请选择类别："
            keyboard = self.create_category_keyboard(transaction_type)
            self.edit_message(chat_id, message_id, text, keyboard)

    def handle_transaction_confirmation(self, chat_id, message_id, user_state):
        """处理交易确认（超快版本）"""
        try:
            # 立即显示处理中状态，提升用户体验
            self.edit_message(chat_id, message_id, "⏳ 正在记账...")

            # 准备交易数据
            transaction_data = user_state['data'].copy()
            transaction_data['date'] = datetime.now().strftime('%Y-%m-%d')

            # 异步处理交易创建，避免阻塞
            import threading
            def process_transaction():
                try:
                    # 创建交易
                    result = transaction_handler._create_transaction(transaction_data, chat_id)

                    if result['success']:
                        # 极简成功消息
                        text = f"✅ 记账成功！{transaction_data['amount']} {transaction_data['currency']}"

                        # 检查是否有预算通知
                        if result.get('budget_notification'):
                            notification = result['budget_notification']
                            # 发送预算通知
                            self.send_message(chat_id, notification['message'])
                            logger.info(f"已发送预算通知到用户: {chat_id}")

                        # 异步清除状态，不阻塞响应
                        threading.Thread(target=lambda: self.clear_user_state(chat_id), daemon=True).start()
                    else:
                        text = f"❌ 记账失败：{result['error']}"

                    # 更新消息
                    self.edit_message(chat_id, message_id, text)

                except Exception as e:
                    logger.error(f"异步处理交易时出错: {str(e)}")
                    error_text = f"❌ 记账失败：{str(e)}"
                    self.edit_message(chat_id, message_id, error_text)

            # 启动异步处理
            threading.Thread(target=process_transaction, daemon=True).start()

        except Exception as e:
            logger.error(f"确认交易时出错: {str(e)}")
            text = f"❌ 记账失败：{str(e)}"
            self.edit_message(chat_id, message_id, text)

    def handle_transaction_cancellation(self, chat_id, message_id):
        """处理交易取消"""
        self.clear_user_state(chat_id)
        text = "❌ 记账已取消"
        self.edit_message(chat_id, message_id, text)

    def handle_skip_description(self, chat_id, message_id, user_state):
        """处理跳过描述"""
        user_state['data']['description'] = ''
        user_state['step'] = 'attachment'
        self.set_user_state(chat_id, user_state)

        text = f"💰 *记账信息*\n\n"
        text += f"类型：{user_state['data']['type']}\n"
        text += f"类别：{user_state['data']['category']}\n"
        text += f"账户：{user_state['data']['account_name']}\n"
        text += f"金额：{user_state['data']['amount']} {user_state['data']['currency']}\n"
        text += f"描述：无\n\n"
        text += "是否需要添加附件？"

        keyboard = self.create_attachment_keyboard()
        self.edit_message(chat_id, message_id, text, keyboard)

    def handle_edit_transaction(self, chat_id, message_id, user_state):
        """处理编辑交易"""
        # 重新开始记账流程，但保留已有数据
        user_state['step'] = 'type'
        self.set_user_state(chat_id, user_state)

        text = "💰 *编辑记账*\n\n请重新选择交易类型："
        keyboard = self.create_transaction_type_keyboard()
        self.edit_message(chat_id, message_id, text, keyboard)

    def show_transaction_confirmation(self, chat_id, message_id, user_state):
        """显示交易确认"""
        data = user_state['data']

        text = "💰 *确认记账信息*\n\n"
        text += f"类型：{data['type']}\n"
        text += f"类别：{data['category']}\n"
        text += f"金额：{data['amount']} {data['currency']}\n"
        text += f"账户：{data['account_name']}\n"
        text += f"描述：{data.get('description', '无')}\n"
        if data.get('attachment'):
            text += "附件：已添加\n"

        keyboard = self.create_confirm_keyboard()
        self.edit_message(chat_id, message_id, text, keyboard)

    def handle_text_message(self, chat_id, text, message_id=None):
        """处理文本消息"""
        user_state = self.get_user_state(chat_id)

        if not user_state:
            # 没有活跃状态，检查是否是命令
            if text.lower() in ['记账', '/add', '/start_transaction']:
                self.start_transaction(chat_id)
            elif text.lower() in ['余额', '/balance']:
                self.handle_balance_query(chat_id)
            elif text.lower() in ['预算', '/budget']:
                self.handle_budget_query(chat_id)
            elif text.lower() in ['帮助', '/help']:
                self.send_help_message(chat_id)
            else:
                # 提示使用正确的命令
                self.send_message(chat_id, "❓ 未识别的命令。发送 '帮助' 查看使用说明，或发送 '记账' 开始记账。")
            return

        step = user_state.get('step')

        if step == 'amount':
            self.handle_amount_input(chat_id, text, user_state, message_id)
        elif step == 'description':
            self.handle_description_input(chat_id, text, user_state, message_id)
        else:
            self.send_message(chat_id, "请使用按钮进行操作，或输入 /help 查看帮助")

    def handle_amount_input(self, chat_id, text, user_state, message_id):
        """处理金额输入"""
        try:
            amount = float(text.strip())
            if amount <= 0:
                self.send_message(chat_id, "❌ 金额必须大于0，请重新输入：")
                return

            user_state['data']['amount'] = amount
            user_state['step'] = 'description'
            self.set_user_state(chat_id, user_state)

            text = f"💰 *记账信息*\n\n"
            text += f"类型：{user_state['data']['type']}\n"
            text += f"类别：{user_state['data']['category']}\n"
            text += f"账户：{user_state['data']['account_name']}\n"
            text += f"金额：{amount} {user_state['data']['currency']}\n\n"
            text += "请输入描述（可选，直接发送'跳过'或点击按钮）："

            # 创建跳过按钮
            keyboard = self.create_inline_keyboard([
                [{'text': '⏭️ 跳过描述', 'callback_data': 'skip_description'}]
            ])

            self.send_message(chat_id, text, keyboard)

        except ValueError:
            self.send_message(chat_id, "❌ 请输入有效的数字金额：")

    def handle_description_input(self, chat_id, text, user_state, message_id):
        """处理描述输入"""
        if text.strip().lower() in ['跳过', 'skip']:
            description = ''
        else:
            description = text.strip()

        user_state['data']['description'] = description
        user_state['step'] = 'attachment'
        self.set_user_state(chat_id, user_state)

        text = f"💰 *记账信息*\n\n"
        text += f"类型：{user_state['data']['type']}\n"
        text += f"类别：{user_state['data']['category']}\n"
        text += f"账户：{user_state['data']['account_name']}\n"
        text += f"金额：{user_state['data']['amount']} {user_state['data']['currency']}\n"
        text += f"描述：{description or '无'}\n\n"
        text += "是否需要添加附件？"

        keyboard = self.create_attachment_keyboard()
        self.send_message(chat_id, text, keyboard)

    def handle_photo_message(self, chat_id, photo_data):
        """处理图片消息"""
        user_state = self.get_user_state(chat_id)

        if not user_state or not user_state.get('step', '').startswith('attachment_'):
            self.send_message(chat_id, "请先开始记账流程")
            return

        try:
            # 获取最大尺寸的图片
            largest_photo = max(photo_data, key=lambda x: x['file_size'])
            file_id = largest_photo['file_id']

            # 获取文件信息
            file_info = self.get_file_info(file_id)
            if file_info:
                # 下载并保存图片
                file_data = self.download_file(file_info['file_path'])
                if file_data:
                    # 转换为base64存储
                    attachment_data = base64.b64encode(file_data).decode('utf-8')
                    user_state['data']['attachment'] = f"data:image/jpeg;base64,{attachment_data}"
                    self.set_user_state(chat_id, user_state)

                    self.send_message(chat_id, "✅ 图片已保存")

                    # 显示确认界面
                    self.show_final_confirmation(chat_id, user_state)
                else:
                    self.send_message(chat_id, "❌ 图片下载失败，请重试")
            else:
                self.send_message(chat_id, "❌ 获取图片信息失败，请重试")

        except Exception as e:
            logger.error(f"处理图片时出错: {str(e)}")
            self.send_message(chat_id, "❌ 处理图片时出错，请重试")

    def handle_document_message(self, chat_id, document_data):
        """处理文档消息"""
        user_state = self.get_user_state(chat_id)

        if not user_state or not user_state.get('step', '').startswith('attachment_'):
            self.send_message(chat_id, "请先开始记账流程")
            return

        try:
            file_id = document_data['file_id']
            file_name = document_data.get('file_name', 'document')

            # 获取文件信息
            file_info = self.get_file_info(file_id)
            if file_info:
                # 下载并保存文件
                file_data = self.download_file(file_info['file_path'])
                if file_data:
                    # 转换为base64存储
                    attachment_data = base64.b64encode(file_data).decode('utf-8')
                    user_state['data']['attachment'] = f"data:application/octet-stream;base64,{attachment_data}"
                    user_state['data']['attachment_name'] = file_name
                    self.set_user_state(chat_id, user_state)

                    self.send_message(chat_id, f"✅ 文件 {file_name} 已保存")

                    # 显示确认界面
                    self.show_final_confirmation(chat_id, user_state)
                else:
                    self.send_message(chat_id, "❌ 文件下载失败，请重试")
            else:
                self.send_message(chat_id, "❌ 获取文件信息失败，请重试")

        except Exception as e:
            logger.error(f"处理文档时出错: {str(e)}")
            self.send_message(chat_id, "❌ 处理文档时出错，请重试")

    def get_file_info(self, file_id):
        """获取文件信息"""
        try:
            url = f"{self.api_url}/getFile"
            params = {'file_id': file_id}

            response = requests.get(url, params=params, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get('ok'):
                    return result.get('result')
            return None
        except Exception as e:
            logger.error(f"获取文件信息时出错: {str(e)}")
            return None

    def download_file(self, file_path):
        """下载文件"""
        try:
            url = f"https://api.telegram.org/file/bot{self.bot_token}/{file_path}"
            response = requests.get(url, timeout=30)
            if response.status_code == 200:
                return response.content
            return None
        except Exception as e:
            logger.error(f"下载文件时出错: {str(e)}")
            return None

    def show_final_confirmation(self, chat_id, user_state):
        """显示最终确认"""
        data = user_state['data']

        text = "💰 *确认记账信息*\n\n"
        text += f"类型：{data['type']}\n"
        text += f"类别：{data['category']}\n"
        text += f"金额：{data['amount']} {data['currency']}\n"
        text += f"账户：{data['account_name']}\n"
        text += f"描述：{data.get('description', '无')}\n"
        if data.get('attachment'):
            attachment_name = data.get('attachment_name', '附件')
            text += f"附件：{attachment_name}\n"

        keyboard = self.create_confirm_keyboard()
        self.send_message(chat_id, text, keyboard)



    def handle_balance_query(self, chat_id):
        """处理余额查询"""
        try:
            # 检查用户是否已绑定
            if not self.check_user_binding(chat_id):
                self.send_binding_prompt(chat_id)
                return

            # 获取用户ID用于清除缓存
            user_binding = self.get_user_binding_info(chat_id)
            user_id = user_binding['website_user_id'] if user_binding and user_binding.get('is_bound') else None

            # 清除余额缓存以获取最新数据
            if user_id:
                transaction_handler.invalidate_balance_cache(user_id)
                logger.info(f"已清除用户 {user_id} 的余额缓存")

            result = transaction_handler.handle_query_message("余额查询", chat_id)
            if result['success']:
                accounts = result['data'].get('accounts', [])
                total = result['data'].get('total', {})

                message = "💰 *账户余额查询*\n\n"
                for account in accounts:
                    message += f"📊 {account['account']}: {account['balance']:.2f} {account['currency']}\n"

                message += "\n💳 *总余额:*\n"
                for currency, amount in total.items():
                    message += f"💰 {amount:.2f} {currency}\n"

                self.send_message(chat_id, message.strip())
            else:
                self.send_message(chat_id, f"❌ {result['error']}")
        except Exception as e:
            logger.error(f"查询余额时出错: {str(e)}")
            self.send_message(chat_id, "❌ 查询余额失败")

    def handle_budget_query(self, chat_id):
        """处理预算查询"""
        try:
            # 检查用户是否已绑定
            if not self.check_user_binding(chat_id):
                self.send_binding_prompt(chat_id)
                return

            result = transaction_handler.handle_query_message("预算查询", chat_id)
            if result['success']:
                budgets = result['data'].get('budgets', [])

                if not budgets:
                    self.send_message(chat_id, "📊 暂无预算设置")
                    return

                message = "📊 *预算使用情况*\n\n"
                for budget in budgets:
                    percentage = int(budget['percentage'] * 100)
                    status_emoji = "🚨" if percentage >= 100 else "⚠️" if percentage >= 80 else "✅"

                    message += f"{status_emoji} *{budget['category']}*\n"
                    message += f"   预算: {budget['budget_amount']} {budget['currency']}\n"
                    message += f"   已用: {budget['used_amount']:.2f} {budget['currency']}\n"
                    message += f"   剩余: {budget['remaining']:.2f} {budget['currency']}\n"
                    message += f"   使用: {percentage}%\n\n"

                self.send_message(chat_id, message.strip())
            else:
                self.send_message(chat_id, f"❌ {result['error']}")
        except Exception as e:
            logger.error(f"查询预算时出错: {str(e)}")
            self.send_message(chat_id, "❌ 查询预算失败")

    def send_help_message(self, chat_id):
        """发送帮助消息"""
        help_text = """
🤖 *财务管理机器人帮助*

🔗 *账户绑定*：
• `/bind 用户名` - 绑定网站账户
• `/unbind` - 解绑网站账户
• `/status` - 查看绑定状态

📝 *交互式记账*：
• 发送 '记账' 或 /add 开始记账
• 按照提示选择类型、类别、账户
• 输入金额和描述
• 可选择上传附件

📊 *查询功能*：
• 发送 '余额' 或 /balance 查询账户余额
• 发送 '预算' 或 /budget 查询预算使用情况

💡 *提示*：
首次使用需要先绑定网站账户！
        """.strip()

        self.send_message(chat_id, help_text)

    def add_user(self, chat_id, username=None, first_name=None, last_name=None):
        """添加用户到数据库（优化版本）"""
        # 异步添加用户，不阻塞主流程
        import threading
        threading.Thread(
            target=lambda: bot_db.add_user(chat_id, username, first_name, last_name),
            daemon=True
        ).start()
        logger.info(f"添加用户: {chat_id}")

    def handle_update(self, update):
        """处理更新"""
        try:
            self.last_update_id = update['update_id']

            # 处理回调查询
            if 'callback_query' in update:
                self.handle_callback_query(update['callback_query'])
                return

            # 处理消息
            if 'message' not in update:
                return

            message = update['message']
            chat_id = message['chat']['id']

            # 获取用户信息
            user = message.get('from', {})
            username = user.get('username')
            first_name = user.get('first_name')
            last_name = user.get('last_name')
            display_name = username or first_name or 'Unknown'

            # 添加用户到数据库
            self.add_user(chat_id, username, first_name, last_name)

            # 处理不同类型的消息
            if 'text' in message:
                text = message['text']
                logger.info(f"收到来自用户 {display_name}({chat_id}) 的消息: {text}")
                # 异步记录消息日志，不阻塞主流程
                import threading
                threading.Thread(
                    target=lambda: bot_db.log_message(chat_id, 'text', text, 'incoming'),
                    daemon=True
                ).start()

                if text.startswith('/'):
                    self.handle_command(chat_id, text)
                else:
                    self.handle_text_message(chat_id, text, message.get('message_id'))

            elif 'photo' in message:
                logger.info(f"收到来自用户 {display_name}({chat_id}) 的图片")
                # 异步记录图片消息日志
                import threading
                threading.Thread(
                    target=lambda: bot_db.log_message(chat_id, 'photo', 'photo_received', 'incoming'),
                    daemon=True
                ).start()
                self.handle_photo_message(chat_id, message['photo'])

            elif 'document' in message:
                logger.info(f"收到来自用户 {display_name}({chat_id}) 的文档")
                # 异步记录文档消息日志
                import threading
                threading.Thread(
                    target=lambda: bot_db.log_message(chat_id, 'document', 'document_received', 'incoming'),
                    daemon=True
                ).start()
                self.handle_document_message(chat_id, message['document'])

        except Exception as e:
            logger.error(f"处理更新时出错: {str(e)}")

    def handle_command(self, chat_id, command):
        """处理命令"""
        try:
            if command == '/start':
                # 检查用户是否已绑定
                if self.check_user_binding(chat_id):
                    binding_info = self.get_user_binding_info(chat_id)
                    welcome_message = f"""
🎉 *欢迎回来！*

👤 当前绑定用户：`{binding_info['website_username']}`

🎯 *交互式记账*
• 发送 '记账' 开始
• 按钮选择，准确便捷
• 支持账户选择和附件上传

📊 *其他功能*
• 余额查询：发送 '余额'
• 预算查询：发送 '预算'
• 帮助信息：发送 '帮助'

开始记账吧！💰
                    """.strip()
                else:
                    welcome_message = """
🎉 *欢迎使用财务管理机器人！*

🔗 *首次使用需要绑定账户*

请使用以下命令绑定您的网站用户名：
`/bind 您的用户名`

例如：`/bind admin`

如果您还没有网站账户，请先到网站注册。

绑定后即可使用所有功能：
• 💰 交互式记账
• 📊 余额查询
• 📈 预算查询

输入 `/help` 查看详细帮助
                    """.strip()

                self.send_message(chat_id, welcome_message)

            elif command in ['/add', '/start_transaction']:
                self.start_transaction(chat_id)

            elif command == '/balance':
                self.handle_balance_query(chat_id)

            elif command == '/budget':
                self.handle_budget_query(chat_id)

            elif command == '/help':
                self.send_help_message(chat_id)

            elif command.startswith('/bind'):
                self.handle_bind_command(chat_id, command)

            elif command == '/unbind':
                self.handle_unbind_command(chat_id)

            elif command == '/status':
                self.handle_status_command(chat_id)

            else:
                self.send_message(chat_id, "❓ 未知命令，输入 /help 查看帮助")

        except Exception as e:
            logger.error(f"处理命令时出错: {str(e)}")
            self.send_message(chat_id, "❌ 处理命令时出错")

    def get_updates(self):
        """获取更新"""
        try:
            url = f"{self.api_url}/getUpdates"
            params = {
                'offset': self.last_update_id + 1,
                'timeout': 30
            }

            response = requests.get(url, params=params, timeout=35)
            if response.status_code == 200:
                result = response.json()
                if result.get('ok'):
                    return result.get('result', [])
            return []
        except Exception as e:
            logger.error(f"获取更新时出错: {str(e)}")
            return []

    def run(self):
        """运行机器人"""
        if not self.enabled:
            logger.error("Telegram机器人未启用，请检查配置")
            return

        logger.info("=" * 60)
        logger.info("交互式Telegram财务管理机器人")
        logger.info("=" * 60)
        logger.info("机器人正在启动...")
        logger.info("=" * 60)

        self.running = True

        try:
            logger.info("Telegram机器人启动成功！")
            logger.info("支持内联键盘交互式记账")
            logger.info("机器人正在监听消息...")

            while self.running:
                try:
                    updates = self.get_updates()
                    for update in updates:
                        self.handle_update(update)
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    logger.error(f"处理更新时出错: {str(e)}")
                    time.sleep(5)

        except KeyboardInterrupt:
            logger.info("机器人已停止")
        except Exception as e:
            logger.error(f"运行机器人时出错: {str(e)}")
        finally:
            self.running = False

# 创建机器人实例
interactive_bot = InteractiveTelegramBot()

if __name__ == '__main__':
    interactive_bot.run()
