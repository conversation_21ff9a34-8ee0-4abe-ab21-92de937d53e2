#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单缓存模块
用于缓存频繁查询的数据
"""

import time
from typing import Any, Optional

class SimpleCache:
    """简单的内存缓存"""

    def __init__(self):
        self.cache = {}
        self.cache_timeout = {}

    def get(self, key: str, timeout: int = 300) -> Optional[Any]:
        """获取缓存数据"""
        if key in self.cache:
            if time.time() - self.cache_timeout[key] < timeout:
                return self.cache[key]
            else:
                # 缓存过期，删除
                del self.cache[key]
                del self.cache_timeout[key]
        return None

    def set(self, key: str, value: Any):
        """设置缓存数据"""
        self.cache[key] = value
        self.cache_timeout[key] = time.time()

    def clear(self):
        """清空缓存"""
        self.cache.clear()
        self.cache_timeout.clear()

    def size(self) -> int:
        """获取缓存大小"""
        return len(self.cache)

# 全局缓存实例
cache = SimpleCache()

def cache_key(*args) -> str:
    """生成缓存键"""
    return "_".join(str(arg) for arg in args)

def cached_query(key: str, query_func, timeout: int = 300):
    """缓存查询结果"""
    result = cache.get(key, timeout)
    if result is None:
        result = query_func()
        cache.set(key, result)
    return result

class BudgetCache:
    """预算专用高性能缓存类"""

    def __init__(self):
        self.budget_data = {}
        self.budget_timeout = {}
        self.calculation_cache = {}
        self.calculation_timeout = {}

    def get_budget_data(self, key, timeout=120):
        """获取预算数据缓存（2分钟缓存）"""
        if key in self.budget_data:
            if time.time() - self.budget_timeout[key] < timeout:
                return self.budget_data[key]
            else:
                del self.budget_data[key]
                del self.budget_timeout[key]
        return None

    def set_budget_data(self, key, value):
        """设置预算数据缓存"""
        self.budget_data[key] = value
        self.budget_timeout[key] = time.time()

    def get_calculation(self, category, start_date, end_date, timeout=300):
        """获取预算计算结果缓存（5分钟缓存）"""
        key = f"{category}_{start_date}_{end_date}"
        if key in self.calculation_cache:
            if time.time() - self.calculation_timeout[key] < timeout:
                return self.calculation_cache[key]
            else:
                del self.calculation_cache[key]
                del self.calculation_timeout[key]
        return None

    def set_calculation(self, category, start_date, end_date, value):
        """设置预算计算结果缓存"""
        key = f"{category}_{start_date}_{end_date}"
        self.calculation_cache[key] = value
        self.calculation_timeout[key] = time.time()

    def clear_category_cache(self, category):
        """清除特定类别的缓存（当有新交易时调用）"""
        keys_to_remove = []
        for key in self.calculation_cache:
            if key.startswith(f"{category}_"):
                keys_to_remove.append(key)

        for key in keys_to_remove:
            if key in self.calculation_cache:
                del self.calculation_cache[key]
            if key in self.calculation_timeout:
                del self.calculation_timeout[key]

    def clear_all(self):
        """清空所有预算缓存"""
        self.budget_data.clear()
        self.budget_timeout.clear()
        self.calculation_cache.clear()
        self.calculation_timeout.clear()

# 全局预算缓存实例
budget_cache = BudgetCache()
