#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
交易处理器
处理记账相关的消息
"""

import requests
import json
import uuid
import logging
from datetime import datetime
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from bot.config.bot_config import config
from bot.utils.budget_checker import budget_checker

# 尝试导入缓存模块，如果失败则创建简单的缓存
try:
    from bot.utils.cache import cache, budget_cache
except ImportError:
    # 创建简单的内存缓存
    class SimpleCache:
        def __init__(self):
            self.cache = {}
            self.cache_timeout = {}

        def get(self, key, timeout=300):
            import time
            if key in self.cache:
                if time.time() - self.cache_timeout[key] < timeout:
                    return self.cache[key]
                else:
                    del self.cache[key]
                    del self.cache_timeout[key]
            return None

        def set(self, key, value):
            import time
            self.cache[key] = value
            self.cache_timeout[key] = time.time()

    cache = SimpleCache()
    budget_cache = None

logger = logging.getLogger(__name__)

class TransactionHandler:
    """交易处理器类"""

    def __init__(self):
        self.api_base_url = config.WEB_API_BASE_URL
        self.api_token = config.WEB_API_TOKEN

    def get_user_id_from_chat_id(self, chat_id):
        """根据chat_id获取绑定的网站用户ID"""
        try:
            from bot.database import bot_db
            binding = bot_db.get_user_binding(chat_id)
            if binding and binding.get('is_bound'):
                return binding['website_user_id']
            return None
        except Exception as e:
            logger.error(f"获取用户绑定信息失败: {e}")
            return None



    def handle_query_message(self, message, chat_id=None):
        """
        处理查询消息

        Args:
            message (str): 用户消息
            chat_id (int): Telegram用户的chat_id

        Returns:
            dict: 查询结果
        """
        result = {
            'success': False,
            'data': None,
            'error': None
        }

        try:
            # 获取用户ID
            user_id = None
            if chat_id:
                user_id = self.get_user_id_from_chat_id(chat_id)
                if not user_id:
                    result['error'] = '用户未绑定网站账户'
                    return result

            message_lower = message.lower().strip()

            if '余额' in message or 'balance' in message_lower:
                # 查询余额
                result = self._query_balance(user_id)
            elif '预算' in message or 'budget' in message_lower:
                # 查询预算
                result = self._query_budget(user_id)
            elif '交易' in message or 'transaction' in message_lower:
                # 查询最近交易
                result = self._query_recent_transactions(user_id)
            else:
                result['error'] = '不支持的查询类型'

        except Exception as e:
            logger.error(f"处理查询消息时出错: {str(e)}")
            result['error'] = f"查询时出错: {str(e)}"

        return result

    def _create_transaction(self, transaction_data, chat_id=None):
        """创建交易记录（超快版本 - 直接数据库操作）"""
        try:
            # 获取用户ID
            user_id = None
            if chat_id:
                user_id = self.get_user_id_from_chat_id(chat_id)
                if not user_id:
                    return {
                        'success': False,
                        'data': None,
                        'error': '用户未绑定网站账户'
                    }
            elif 'user_id' in transaction_data:
                user_id = transaction_data['user_id']
            else:
                return {
                    'success': False,
                    'data': None,
                    'error': '缺少用户信息'
                }

            # 生成交易ID
            transaction_id = f"tx_{uuid.uuid4()}"
            now = datetime.now().isoformat()

            # 直接写入数据库，跳过API请求
            import sqlite3
            import os

            # 确定正确的数据库路径
            # __file__ 是 bot/handlers/transaction_handler.py
            # 需要回到项目根目录，然后进入data目录: ../../data/finance.db
            current_dir = os.path.dirname(__file__)  # bot/handlers
            bot_dir = os.path.dirname(current_dir)   # bot
            project_dir = os.path.dirname(bot_dir)   # 项目根目录
            db_path = os.path.join(project_dir, 'data', 'finance.db')
            logger.info(f"尝试连接数据库: {db_path}")

            if not os.path.exists(db_path):
                logger.error(f"数据库文件不存在: {db_path}")
                return {
                    'success': False,
                    'data': None,
                    'error': f'数据库文件不存在: {db_path}'
                }

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO transactions (
                    id, user_id, date, type, category, description, account, amount, currency, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                transaction_id,
                user_id,
                transaction_data['date'],
                transaction_data['type'],
                transaction_data['category'],
                transaction_data.get('description', ''),
                transaction_data['account'],
                transaction_data['amount'],
                transaction_data['currency'],
                now,
                now
            ))

            conn.commit()
            conn.close()

            # 异步清除缓存，不阻塞响应
            import threading
            def clear_cache_async():
                self.invalidate_balance_cache()
                if transaction_data['type'] == 'expense':
                    self.invalidate_budget_cache(transaction_data['category'])
            threading.Thread(target=clear_cache_async, daemon=True).start()

            # 如果是支出交易，检查预算并返回通知
            budget_notification = None
            if transaction_data['type'] == 'expense':
                try:
                    budget_notification = budget_checker.check_budget_after_transaction(transaction_data)
                    logger.info(f"预算检查完成，通知: {budget_notification is not None}")
                except Exception as e:
                    logger.error(f"预算检查失败: {str(e)}")

            logger.info(f"交易创建成功: {transaction_id}")
            return {
                'success': True,
                'id': transaction_id,
                'budget_notification': budget_notification
            }

        except Exception as e:
            logger.error(f"创建交易时出错: {str(e)}")
            return {
                'success': False,
                'data': None,
                'error': f'创建交易时出错: {str(e)}'
            }

    def _query_balance(self, user_id=None):
        """查询账户余额（高性能优化版本）"""
        try:
            # 使用用户特定的缓存键
            cache_key = f"balance_query_v2_{user_id}" if user_id else "balance_query_v2"
            cached_result = cache.get(cache_key, timeout=300)  # 5分钟缓存
            if cached_result:
                logger.debug("使用缓存的余额数据")
                return cached_result

            # 优先使用直接数据库查询，避免HTTP请求开销
            try:
                result = self._query_balance_direct(user_id)
                if result['success']:
                    # 缓存结果
                    cache.set(cache_key, result)
                    logger.debug("使用直接数据库查询获取余额")
                    return result
            except Exception as db_error:
                logger.warning(f"直接数据库查询失败，回退到API查询: {str(db_error)}")

            # 回退到API查询（优化版本）
            return self._query_balance_api_optimized(user_id)

        except Exception as e:
            logger.error(f"查询余额时出错: {str(e)}")
            return {
                'success': False,
                'data': None,
                'error': f'查询余额时出错: {str(e)}'
            }

    def _query_balance_direct(self, user_id=None):
        """直接数据库查询余额（最快方式）"""
        import sqlite3
        import os

        # 确定正确的数据库路径
        # __file__ 是 bot/handlers/transaction_handler.py
        # 需要回到项目根目录，然后进入data目录: ../../data/finance.db
        current_dir = os.path.dirname(__file__)  # bot/handlers
        bot_dir = os.path.dirname(current_dir)   # bot
        project_dir = os.path.dirname(bot_dir)   # 项目根目录
        db_path = os.path.join(project_dir, 'data', 'finance.db')
        logger.info(f"查询余额 - 尝试连接数据库: {db_path}")

        if not os.path.exists(db_path):
            logger.error(f"数据库文件不存在: {db_path}")
            raise Exception(f'数据库文件不存在: {db_path}')

        # 连接主数据库
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row

        try:
            # 使用优化的SQL查询一次性获取所有数据，添加用户过滤
            if user_id:
                balance_query = '''
                SELECT
                    a.id as account_id,
                    a.name as account_name,
                    a.currency,
                    a.initial_balance,
                    COALESCE(income.total, 0) as income_total,
                    COALESCE(expense.total, 0) as expense_total,
                    (a.initial_balance + COALESCE(income.total, 0) - COALESCE(expense.total, 0)) as current_balance
                FROM accounts a
                LEFT JOIN (
                    SELECT account, SUM(amount) as total
                    FROM transactions
                    WHERE type = 'income' AND user_id = ?
                    GROUP BY account
                ) income ON a.id = income.account
                LEFT JOIN (
                    SELECT account, SUM(amount) as total
                    FROM transactions
                    WHERE type = 'expense' AND user_id = ?
                    GROUP BY account
                ) expense ON a.id = expense.account
                WHERE a.user_id = ?
                ORDER BY a.name
                '''
                results = conn.execute(balance_query, (user_id, user_id, user_id)).fetchall()
            else:
                # 兼容旧版本，不过滤用户
                balance_query = '''
                SELECT
                    a.id as account_id,
                    a.name as account_name,
                    a.currency,
                    a.initial_balance,
                    COALESCE(income.total, 0) as income_total,
                    COALESCE(expense.total, 0) as expense_total,
                    (a.initial_balance + COALESCE(income.total, 0) - COALESCE(expense.total, 0)) as current_balance
                FROM accounts a
                LEFT JOIN (
                    SELECT account, SUM(amount) as total
                    FROM transactions
                    WHERE type = 'income'
                    GROUP BY account
                ) income ON a.id = income.account
                LEFT JOIN (
                    SELECT account, SUM(amount) as total
                    FROM transactions
                    WHERE type = 'expense'
                    GROUP BY account
                ) expense ON a.id = expense.account
                ORDER BY a.name
                '''
                results = conn.execute(balance_query).fetchall()

            balance_info = []
            total_balance = {}

            for row in results:
                account_name = row['account_name']
                currency = row['currency']
                current_balance = float(row['current_balance'])

                balance_info.append({
                    'account': account_name,
                    'balance': current_balance,
                    'currency': currency
                })

                # 累计总余额
                if currency not in total_balance:
                    total_balance[currency] = 0
                total_balance[currency] += current_balance

            return {
                'success': True,
                'data': {
                    'accounts': balance_info,
                    'total': total_balance
                },
                'error': None
            }

        finally:
            conn.close()

    def _query_balance_api_optimized(self):
        """优化的API查询余额（回退方案）"""
        headers = {}
        if self.api_token:
            headers['X-Auth-Token'] = self.api_token

        # 并行请求账户和交易数据
        import concurrent.futures

        def get_accounts():
            response = requests.get(f"{self.api_base_url}/accounts/", headers=headers, timeout=3)
            response.raise_for_status()
            return response.json()

        def get_transactions():
            response = requests.get(f"{self.api_base_url}/transactions/", headers=headers, timeout=3)
            response.raise_for_status()
            return response.json()

        # 并行执行请求
        with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
            accounts_future = executor.submit(get_accounts)
            transactions_future = executor.submit(get_transactions)

            accounts = accounts_future.result()
            transactions = transactions_future.result()

        # 快速计算余额
        balance_info = []
        total_balance = {}

        # 预处理交易数据以提高查找效率
        account_transactions = {}
        for tx in transactions:
            account_id = tx['account']
            if account_id not in account_transactions:
                account_transactions[account_id] = {'income': 0, 'expense': 0}

            amount = float(tx['amount'])
            if tx['type'] == 'income':
                account_transactions[account_id]['income'] += amount
            elif tx['type'] == 'expense':
                account_transactions[account_id]['expense'] += amount

        for account in accounts:
            account_id = account['id']
            account_name = account['name']
            currency = account['currency']
            initial_balance = float(account['initial_balance'])

            # 获取该账户的交易汇总
            tx_summary = account_transactions.get(account_id, {'income': 0, 'expense': 0})
            current_balance = initial_balance + tx_summary['income'] - tx_summary['expense']

            balance_info.append({
                'account': account_name,
                'balance': current_balance,
                'currency': currency
            })

            # 累计总余额
            if currency not in total_balance:
                total_balance[currency] = 0
            total_balance[currency] += current_balance

        result = {
            'success': True,
            'data': {
                'accounts': balance_info,
                'total': total_balance
            },
            'error': None
        }

        # 缓存结果
        cache.set("balance_query_v2", result)
        return result

    def _query_budget(self, user_id=None):
        """查询预算使用情况（优化版本 - 直接数据库查询）"""
        try:
            # 使用用户特定的缓存键
            cache_key = f"budget_query_{user_id}" if user_id else "budget_query"
            cached_result = cache.get(cache_key, timeout=120)  # 2分钟缓存
            if cached_result:
                return cached_result

            # 直接使用数据库查询，避免HTTP请求开销
            result = self._query_budget_direct(user_id)

            # 缓存结果
            cache.set(cache_key, result)
            return result

        except Exception as e:
            logger.error(f"查询预算时出错: {str(e)}")
            return {
                'success': False,
                'data': None,
                'error': f'查询预算时出错: {str(e)}'
            }

    def _query_budget_direct(self, user_id=None):
        """直接从数据库查询预算使用情况（高性能版本）"""
        import sqlite3
        import os

        try:
            # 确定正确的数据库路径
            # __file__ 是 bot/handlers/transaction_handler.py
            # 需要回到项目根目录，然后进入data目录: ../../data/finance.db
            current_dir = os.path.dirname(__file__)  # bot/handlers
            bot_dir = os.path.dirname(current_dir)   # bot
            project_dir = os.path.dirname(bot_dir)   # 项目根目录
            db_path = os.path.join(project_dir, 'data', 'finance.db')
            logger.info(f"查询预算 - 尝试连接数据库: {db_path}")

            if not os.path.exists(db_path):
                logger.error(f"数据库文件不存在: {db_path}")
                raise Exception(f'数据库文件不存在: {db_path}')

            # 连接数据库
            conn = sqlite3.connect(db_path)
            conn.row_factory = sqlite3.Row

            # 获取用户的预算
            if user_id:
                budgets = conn.execute('SELECT * FROM budgets WHERE user_id = ? ORDER BY category', (user_id,)).fetchall()
            else:
                budgets = conn.execute('SELECT * FROM budgets ORDER BY category').fetchall()

            if not budgets:
                conn.close()
                return {
                    'success': True,
                    'data': {'budgets': []},
                    'error': None
                }

            budget_info = []

            # 为每个预算计算使用情况
            for budget in budgets:
                budget_dict = dict(budget)
                category = budget_dict['category']
                start_date = budget_dict['start_date']
                end_date = budget_dict['end_date']

                # 尝试从专用缓存获取计算结果
                cached_calculation = None
                if budget_cache:
                    cached_calculation = budget_cache.get_calculation(category, start_date, end_date)

                if cached_calculation:
                    # 使用缓存的计算结果
                    spent_amount = cached_calculation
                else:
                    # 使用优化的SQL查询直接计算支出总额，添加用户过滤
                    if user_id:
                        total_spent = conn.execute('''
                            SELECT COALESCE(SUM(amount), 0) as total
                            FROM transactions
                            WHERE type = "expense"
                            AND category = ?
                            AND date >= ?
                            AND date <= ?
                            AND user_id = ?
                        ''', (category, start_date, end_date, user_id)).fetchone()
                    else:
                        total_spent = conn.execute('''
                            SELECT COALESCE(SUM(amount), 0) as total
                            FROM transactions
                            WHERE type = "expense"
                            AND category = ?
                            AND date >= ?
                            AND date <= ?
                        ''', (category, start_date, end_date)).fetchone()

                    spent_amount = float(total_spent['total'])

                    # 缓存计算结果
                    if budget_cache:
                        budget_cache.set_calculation(category, start_date, end_date, spent_amount)

                budget_amount = float(budget_dict['amount'])
                remaining = budget_amount - spent_amount
                percentage = spent_amount / budget_amount if budget_amount > 0 else 0

                budget_info.append({
                    'category': category,
                    'budget_amount': budget_amount,
                    'used_amount': spent_amount,
                    'remaining': remaining,
                    'percentage': percentage,
                    'currency': budget_dict['currency']
                })

            conn.close()

            return {
                'success': True,
                'data': {
                    'budgets': budget_info
                },
                'error': None
            }

        except Exception as e:
            logger.error(f"直接查询预算时出错: {str(e)}")
            return {
                'success': False,
                'data': None,
                'error': f'直接查询预算时出错: {str(e)}'
            }

    def _query_recent_transactions(self, limit=10):
        """查询最近的交易记录"""
        try:
            headers = {}
            if self.api_token:
                headers['X-Auth-Token'] = self.api_token

            response = requests.get(f"{self.api_base_url}/transactions/", headers=headers, timeout=10)
            response.raise_for_status()
            transactions = response.json()

            # 按日期排序，获取最近的交易
            sorted_transactions = sorted(transactions, key=lambda x: x['date'], reverse=True)
            recent_transactions = sorted_transactions[:limit]

            return {
                'success': True,
                'data': recent_transactions,
                'error': None
            }

        except Exception as e:
            logger.error(f"查询最近交易时出错: {str(e)}")
            return {
                'success': False,
                'data': None,
                'error': f'查询最近交易时出错: {str(e)}'
            }

    def invalidate_budget_cache(self, category=None):
        """使预算缓存失效（当有新交易时调用）"""
        try:
            # 清除通用预算查询缓存
            cache_key = "budget_query"
            if hasattr(cache, 'cache') and cache_key in cache.cache:
                del cache.cache[cache_key]
                if hasattr(cache, 'cache_timeout') and cache_key in cache.cache_timeout:
                    del cache.cache_timeout[cache_key]

            # 清除专用预算缓存
            if budget_cache:
                if category:
                    # 只清除特定类别的缓存
                    budget_cache.clear_category_cache(category)
                else:
                    # 清除所有预算缓存
                    budget_cache.clear_all()

            logger.info(f"已清除预算缓存: {category if category else '全部'}")

        except Exception as e:
            logger.error(f"清除预算缓存时出错: {str(e)}")

    def invalidate_balance_cache(self, user_id=None):
        """使余额缓存失效（当有新交易时调用）"""
        try:
            if user_id:
                # 清除特定用户的余额缓存
                user_cache_key = f"balance_query_v2_{user_id}"
                if hasattr(cache, 'cache') and user_cache_key in cache.cache:
                    del cache.cache[user_cache_key]
                    if hasattr(cache, 'cache_timeout') and user_cache_key in cache.cache_timeout:
                        del cache.cache_timeout[user_cache_key]
                logger.info(f"已清除用户 {user_id} 的余额缓存")
            else:
                # 清除所有余额查询缓存
                keys_to_remove = []
                if hasattr(cache, 'cache'):
                    for key in cache.cache.keys():
                        if key.startswith('balance_query'):
                            keys_to_remove.append(key)

                for key in keys_to_remove:
                    if key in cache.cache:
                        del cache.cache[key]
                    if hasattr(cache, 'cache_timeout') and key in cache.cache_timeout:
                        del cache.cache_timeout[key]

                logger.info("已清除所有余额缓存")

        except Exception as e:
            logger.error(f"清除余额缓存时出错: {str(e)}")

    def invalidate_all_cache(self):
        """清除所有缓存（当有新交易时调用）"""
        try:
            # 清除余额缓存
            self.invalidate_balance_cache()

            # 清除预算缓存
            self.invalidate_budget_cache()

            logger.info("已清除所有缓存")

        except Exception as e:
            logger.error(f"清除所有缓存时出错: {str(e)}")

# 创建交易处理器实例
transaction_handler = TransactionHandler()
