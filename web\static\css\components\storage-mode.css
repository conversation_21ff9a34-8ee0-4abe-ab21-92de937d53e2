/*******************************************
 *          存储模式样式
 *******************************************/
.storage-info {
  background-color: var(--color-card);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

.storage-mode {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.storage-mode span {
  font-size: var(--font-size-base);
  color: var(--color-text);
}

.storage-mode strong {
  color: var(--color-primary);
  margin-left: var(--spacing-xs);
}

.storage-mode-btn {
  padding: var(--spacing-xs) var(--spacing-md);
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius-xs);
  cursor: pointer;
  font-size: var(--font-size-sm);
  transition: background-color 0.3s;
}

.storage-mode-btn:hover {
  background-color: var(--color-primary-dark);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .storage-mode {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .storage-mode-btn {
    width: 100%;
  }
}
