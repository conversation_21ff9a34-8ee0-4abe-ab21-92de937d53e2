import{a as m,r as oe,l as B,m as le,c as V,e as s,v as E,f as v,w as l,d as o,h as ie,s as $,g as re,o as y,i as T,p as ce,t as p,q as ue,n as U}from"./index--MNqwREY.js";import{u as de}from"./subscriptions-4RI1wB9c.js";import{c as Y,S as z,g as ve}from"./ServiceLogo-DKV-znac.js";import{_ as me}from"./_plugin-vue_export-helper-DlAUqK2U.js";const pe={class:"edit-subscription-page"},_e={key:0,class:"loading-container"},ye={key:1,class:"page-container"},fe={key:1,class:"service-icon"},ge={class:"currency-symbol"},be={class:"field-icon"},we={class:"field-icon"},Ve={class:"field-icon"},ke={class:"field-icon"},Ce={class:"switch-container"},he={class:"switch-label"},xe={class:"field-icon"},Se={key:0,class:"preview-section"},De={class:"preview-card"},$e={class:"preview-header"},Ue={class:"preview-service"},Ye={class:"service-details"},Me={class:"service-name"},Ne={class:"service-type"},Pe={class:"preview-price"},Re={class:"amount"},qe={class:"cycle"},Be={class:"preview-footer"},Ee={class:"start-date"},Te={class:"submit-section"},ze={__name:"Edit",setup(Fe){const M=ie(),N=re(),P=de(),d=m(null),x=m(!0),S=m(!1),f=m(!1),g=m(!1),b=m(!1),w=m(!1),t=oe({name:"",service_type:"",amount:"",currency:"CNY",billing_cycle:"",start_date:"",description:"",is_active:!0}),F=B(()=>t.service_type&&t.amount&&t.currency&&t.billing_cycle&&t.start_date&&/^\d+(\.\d{1,2})?$/.test(t.amount)),k=m([]),L=new Date(2020,0,1),j=new Date(2030,11,31),_=m([]),G=B(()=>{var r;if(_.value.length===0)return"";const[n,e]=_.value,i=Y.find(u=>u.value===n);if(!i)return"";const c=(r=i.children)==null?void 0:r.find(u=>u.value===e);return c?`${i.text} ${c.text}`:i.text}),I=[{text:"月付",value:"monthly"},{text:"季付",value:"quarterly"},{text:"年付",value:"yearly"},{text:"周付",value:"weekly"}],J=[{text:"人民币 (CNY)",value:"CNY"},{text:"美元 (USD)",value:"USD"},{text:"马来西亚令吉 (MYR)",value:"MYR"},{text:"欧元 (EUR)",value:"EUR"},{text:"英镑 (GBP)",value:"GBP"},{text:"日元 (JPY)",value:"JPY"}],C=n=>n?ve(n).name:"未选择",O=n=>({weekly:"/周",monthly:"/月",quarterly:"/季",yearly:"/年"})[n]||"/月",R=n=>({CNY:"¥",USD:"$",MYR:"RM",EUR:"€",GBP:"£",JPY:"¥"})[n]||"¥",A=n=>{if(!n)return"未选择";try{const e=new Date(n);return`${e.getFullYear()}年${e.getMonth()+1}月${e.getDate()}日`}catch{return"未知"}},H=({selectedOptions:n})=>{const e=n.map(i=>i.value);if(_.value=e,t.service_type=e[e.length-1],n.length>0){const i=n[0].text.split(" ").slice(1).join(" "),c=n.length>1?n[1].text:"";t.name=c?`${i} ${c}`:i}f.value=!1},K=({selectedOptions:n})=>{t.billing_cycle=n[0].value,g.value=!1},Q=({selectedOptions:n})=>{t.currency=n[0].value,b.value=!1},W=()=>{const[n,e,i]=k.value,c=new Date(n,e-1,i);t.start_date=c.toISOString().split("T")[0],w.value=!1},X=async()=>{S.value=!0;try{const n={...t,amount:parseFloat(t.amount),start_date:new Date(t.start_date).toISOString()};await P.updateSubscription(M.params.id,n),$({message:"修改成功",type:"success"}),N.back()}catch(n){$({message:n.message||"修改失败",type:"fail"})}finally{S.value=!1}},Z=async()=>{var n;try{x.value=!0,d.value=await P.getSubscription(M.params.id),Object.assign(t,{name:d.value.name,service_type:d.value.service_type,amount:d.value.amount.toString(),currency:d.value.currency,billing_cycle:d.value.billing_cycle,start_date:d.value.start_date.split("T")[0],description:d.value.description||"",is_active:d.value.is_active});const e=new Date(d.value.start_date);k.value=[e.getFullYear(),e.getMonth()+1,e.getDate()];const i=d.value.service_type;for(const c of Y){if(i===c.value){_.value=[i];break}if((n=c.children)==null?void 0:n.find(u=>u.value===i)){_.value=[c.value,i];break}}}catch{$({message:"获取订阅详情失败",type:"fail"}),N.back()}finally{x.value=!1}};return le(()=>{Z()}),(n,e)=>{const i=v("van-nav-bar"),c=v("van-loading"),r=v("van-icon"),u=v("van-field"),D=v("van-cell-group"),ee=v("van-switch"),te=v("van-button"),ae=v("van-form"),se=v("van-cascader"),h=v("van-popup"),q=v("van-picker"),ne=v("van-date-picker");return y(),V("div",pe,[s(i,{title:"编辑订阅","left-arrow":"",fixed:"",placeholder:"","safe-area-inset-top":"",onClickLeft:e[0]||(e[0]=a=>n.$router.back())}),x.value?(y(),V("div",_e,[s(c,{size:"24px",vertical:""},{default:l(()=>e[22]||(e[22]=[T("加载中...")])),_:1,__:[22]})])):d.value?(y(),V("div",ye,[e[25]||(e[25]=o("div",{class:"page-header"},[o("h1",{class:"page-title"},"编辑订阅"),o("p",{class:"page-description"},"修改您的订阅信息")],-1)),s(ae,{onSubmit:X},{default:l(()=>[s(D,{inset:"",title:"🎯 选择服务"},{default:l(()=>[s(u,{"model-value":G.value,name:"service_type",label:"服务套餐",placeholder:"请选择服务和套餐",readonly:"","is-link":"",onClick:e[1]||(e[1]=a=>f.value=!0),rules:[{required:!0,message:"请选择服务套餐"}]},{"left-icon":l(()=>[t.service_type?(y(),ce(z,{key:0,"service-name":C(t.service_type),"service-type":t.service_type,size:"small"},null,8,["service-name","service-type"])):(y(),V("div",fe," 📱 "))]),"right-icon":l(()=>[s(r,{name:"arrow",class:"arrow-icon"})]),_:1},8,["model-value"])]),_:1}),s(D,{inset:"",title:"📝 基本信息"},{default:l(()=>[s(u,{modelValue:t.amount,"onUpdate:modelValue":e[2]||(e[2]=a=>t.amount=a),name:"amount",label:"费用",placeholder:"0.00",type:"number",rules:[{required:!0,message:"请输入费用"},{pattern:/^\d+(\.\d{1,2})?$/,message:"请输入正确的金额格式"}]},{"left-icon":l(()=>[o("span",ge,p(R(t.currency)),1)]),_:1},8,["modelValue"]),s(u,{modelValue:t.currency,"onUpdate:modelValue":e[3]||(e[3]=a=>t.currency=a),name:"currency",label:"货币",placeholder:"请选择货币",readonly:"","is-link":"",onClick:e[4]||(e[4]=a=>b.value=!0),rules:[{required:!0,message:"请选择货币"}]},{"left-icon":l(()=>[o("div",be,[s(r,{name:"gold-coin-o"})])]),"right-icon":l(()=>[s(r,{name:"arrow",class:"arrow-icon"})]),_:1},8,["modelValue"]),s(u,{modelValue:t.billing_cycle,"onUpdate:modelValue":e[5]||(e[5]=a=>t.billing_cycle=a),name:"billing_cycle",label:"计费周期",placeholder:"请选择计费周期",readonly:"","is-link":"",onClick:e[6]||(e[6]=a=>g.value=!0),rules:[{required:!0,message:"请选择计费周期"}]},{"left-icon":l(()=>[o("div",we,[s(r,{name:"clock-o"})])]),"right-icon":l(()=>[s(r,{name:"arrow",class:"arrow-icon"})]),_:1},8,["modelValue"]),s(u,{modelValue:t.start_date,"onUpdate:modelValue":e[7]||(e[7]=a=>t.start_date=a),name:"start_date",label:"开始日期",placeholder:"请选择开始日期",readonly:"","is-link":"",onClick:e[8]||(e[8]=a=>w.value=!0),rules:[{required:!0,message:"请选择开始日期"}]},{"left-icon":l(()=>[o("div",Ve,[s(r,{name:"calendar-o"})])]),"right-icon":l(()=>[s(r,{name:"arrow",class:"arrow-icon"})]),_:1},8,["modelValue"]),s(u,{modelValue:t.is_active,"onUpdate:modelValue":e[10]||(e[10]=a=>t.is_active=a),name:"is_active",label:"订阅状态"},{"left-icon":l(()=>[o("div",ke,[s(r,{name:"setting-o"})])]),input:l(()=>[o("div",Ce,[o("span",he,p(t.is_active?"启用":"暂停"),1),s(ee,{modelValue:t.is_active,"onUpdate:modelValue":e[9]||(e[9]=a=>t.is_active=a)},null,8,["modelValue"])])]),_:1},8,["modelValue"])]),_:1}),s(D,{inset:"",title:"💭 可选信息"},{default:l(()=>[s(u,{modelValue:t.description,"onUpdate:modelValue":e[11]||(e[11]=a=>t.description=a),name:"description",label:"备注",placeholder:"添加一些备注信息（可选）",type:"textarea",rows:"3",autosize:""},{"left-icon":l(()=>[o("div",xe,[s(r,{name:"notes-o"})])]),_:1},8,["modelValue"])]),_:1}),t.service_type&&t.amount&&t.billing_cycle?(y(),V("div",Se,[e[23]||(e[23]=o("h3",{class:"preview-title"},"📋 预览",-1)),o("div",De,[o("div",$e,[o("div",Ue,[s(z,{"service-name":t.name||C(t.service_type),"service-type":t.service_type,size:"small"},null,8,["service-name","service-type"]),o("div",Ye,[o("h4",Me,p(t.name||C(t.service_type)),1),o("p",Ne,p(C(t.service_type)),1)])]),o("div",Pe,[o("span",Re,p(R(t.currency))+p(t.amount),1),o("span",qe,p(O(t.billing_cycle)),1)])]),o("div",Be,[o("span",Ee,"开始日期: "+p(A(t.start_date)),1),o("span",{class:ue(["status-badge",{active:t.is_active}])},p(t.is_active?"启用":"暂停"),3)])])])):E("",!0),o("div",Te,[s(te,{type:"primary",block:"","native-type":"submit",loading:S.value,disabled:!F.value},{default:l(()=>[s(r,{name:"success"}),e[24]||(e[24]=T(" 保存修改 "))]),_:1,__:[24]},8,["loading","disabled"])])]),_:1})])):E("",!0),s(h,{show:f.value,"onUpdate:show":e[14]||(e[14]=a=>f.value=a),position:"bottom",round:""},{default:l(()=>[s(se,{modelValue:_.value,"onUpdate:modelValue":e[12]||(e[12]=a=>_.value=a),title:"选择服务套餐",options:U(Y),onClose:e[13]||(e[13]=a=>f.value=!1),onFinish:H},null,8,["modelValue","options"])]),_:1},8,["show"]),s(h,{show:g.value,"onUpdate:show":e[16]||(e[16]=a=>g.value=a),position:"bottom",round:""},{default:l(()=>[s(q,{columns:I,onConfirm:K,onCancel:e[15]||(e[15]=a=>g.value=!1)})]),_:1},8,["show"]),s(h,{show:b.value,"onUpdate:show":e[18]||(e[18]=a=>b.value=a),position:"bottom",round:""},{default:l(()=>[s(q,{columns:J,onConfirm:Q,onCancel:e[17]||(e[17]=a=>b.value=!1)})]),_:1},8,["show"]),s(h,{show:w.value,"onUpdate:show":e[21]||(e[21]=a=>w.value=a),position:"bottom",round:""},{default:l(()=>[s(ne,{modelValue:k.value,"onUpdate:modelValue":e[19]||(e[19]=a=>k.value=a),onConfirm:W,onCancel:e[20]||(e[20]=a=>w.value=!1),"min-date":U(L),"max-date":U(j)},null,8,["modelValue","min-date","max-date"])]),_:1},8,["show"])])}}},Je=me(ze,[["__scopeId","data-v-719bb533"]]);export{Je as default};
