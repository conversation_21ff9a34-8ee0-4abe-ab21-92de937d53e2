import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'

// Vant UI组件库
import Vant from 'vant'
import 'vant/lib/index.css'

// Vant触摸模拟器（用于桌面端调试）
import '@vant/touch-emulator'

// 全局样式
import './styles/index.css'

// 设备检测和自适应
import { getDeviceInfo, getAdaptiveClasses } from './utils/device'

// 输出设备信息（用于调试）
console.log('当前设备信息:', getDeviceInfo())
console.log('移动版应用已加载')

const app = createApp(App)

// 使用插件
app.use(createPinia())
app.use(router)
app.use(Vant)

// 添加自适应样式类到body
document.body.className += ' ' + getAdaptiveClasses()

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err, info)
}

app.mount('#app')
