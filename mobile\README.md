# 钱管家 - 手机版

个人财务管理系统的移动端应用，基于 Vue 3 + Vant + ECharts + Pinia 构建。

## 🚀 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
cd finance-mobile
npm install
```

### 启动开发服务器

```bash
npm run dev
```

应用将在 `http://localhost:3000` 启动。

### 构建生产版本

```bash
npm run build
```

## 🛠️ 技术栈

- **框架**: Vue 3 (Composition API)
- **构建工具**: Vite
- **UI组件库**: Vant 4
- **图表库**: ECharts + vue-echarts
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios
- **工具库**: @vueuse/core

## 📱 功能特性

### 已实现功能

- ✅ 用户认证（登录/注册）
- ✅ 仪表盘概览
- ✅ 响应式布局
- ✅ 底部导航栏
- ✅ API接口封装
- ✅ 状态管理
- ✅ 图表组件
- ✅ 工具函数库
- ✅ **智能设备检测和自适应**
- ✅ **自动端口切换**
- ✅ **手动版本切换**
- ✅ **设备信息调试**

### 已完成功能 (核心功能)

- ✅ **交易记录管理** - 完整的CRUD操作、搜索筛选、批量操作
- ✅ **账户管理** - 账户列表、实时余额计算、资产统计
- ✅ **预算管理** - 预算创建、使用情况监控、智能状态提醒
- ✅ **数据统计** - ECharts图表、收支分析、分类统计
- ✅ **搜索功能** - 交易搜索、实时筛选

### 待完善功能

- 🔄 **个人资料编辑** - 用户信息修改功能
- 🔄 **应用设置** - 主题切换、偏好配置
- 🔄 **财务目标** - 目标设置和进度跟踪
- 🔄 **通知推送** - 预算提醒和重要通知
- 🔄 **离线支持** - PWA功能和离线数据访问

## 📁 项目结构

```
src/
├── api/                 # API接口封装
│   ├── index.js        # Axios配置和拦截器
│   ├── auth.js         # 认证API
│   ├── transactions.js # 交易API
│   ├── accounts.js     # 账户API
│   └── budgets.js      # 预算API
├── components/         # 通用组件
│   ├── charts/         # 图表组件
│   │   └── BaseChart.vue
│   └── DeviceInfo.vue  # 设备信息组件
├── stores/             # Pinia状态管理
│   ├── auth.js         # 认证状态
│   ├── transactions.js # 交易状态
│   ├── accounts.js     # 账户状态
│   └── budgets.js      # 预算状态
├── utils/              # 工具函数
│   ├── device.js       # 设备检测和自适应
│   └── format.js       # 格式化工具
├── views/              # 页面组件
│   ├── Auth/           # 认证页面 (登录/注册)
│   ├── Dashboard/      # 仪表盘概览
│   ├── Transactions/   # 交易记录管理
│   ├── Accounts/       # 账户管理
│   ├── Budgets/        # 预算管理
│   ├── Statistics/     # 数据统计
│   ├── Profile/        # 个人中心
│   ├── Layout/         # 布局组件
│   └── Error/          # 错误页面
├── router/             # 路由配置
├── styles/             # 全局样式
└── main.js             # 应用入口
```

## 🔧 配置说明

### API代理配置

开发环境下，所有 `/api/*` 请求会被代理到 `http://localhost:8000`（Flask后端）。

```javascript
// vite.config.js
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:8000',
      changeOrigin: true
    }
  }
}
```

### 环境变量

创建 `.env.local` 文件来配置环境变量：

```env
# API基础URL（生产环境）
VITE_API_BASE_URL=http://your-api-domain.com

# 应用标题
VITE_APP_TITLE=钱管家

# 是否启用调试模式
VITE_DEBUG=true
```

## 🎨 UI设计

### 主题色彩

- 主色调: `#1989fa` (蓝色)
- 成功色: `#07c160` (绿色)
- 警告色: `#ff976a` (橙色)
- 危险色: `#ee0a24` (红色)

### 响应式断点

- 小屏幕: `< 375px`
- 中等屏幕: `375px - 414px`
- 大屏幕: `> 414px`

## 📊 状态管理

使用 Pinia 进行状态管理，主要包括：

- `authStore`: 用户认证状态
- `transactionsStore`: 交易数据状态
- `accountsStore`: 账户数据状态（待实现）
- `budgetsStore`: 预算数据状态（待实现）

## 🔄 智能自适应功能

### 设备检测和自动切换

系统会自动检测用户设备类型并跳转到最适合的版本：

- **移动设备** → 自动跳转到手机版 (3000端口)
- **桌面设备** → 自动跳转到网页版 (8000端口)
- **平板设备** → 根据屏幕尺寸智能选择

### 检测规则

```javascript
// 移动设备检测
const isMobile = () => {
  const userAgent = navigator.userAgent.toLowerCase()
  const mobileKeywords = ['android', 'iphone', 'ipad', 'mobile']
  return mobileKeywords.some(keyword => userAgent.includes(keyword))
}

// 屏幕尺寸检测
const isSmallScreen = () => window.innerWidth < 768

// 触摸设备检测
const isTouchDevice = () => 'ontouchstart' in window
```

### 手动切换

用户可以在个人中心手动切换版本：
- 手机版 → 网页版：个人中心 > 切换到网页版
- 网页版 → 手机版：自动检测提示或手动访问3000端口

### 调试功能

在手机版个人中心可以查看详细的设备信息：
- 设备类型和屏幕信息
- User Agent 信息
- 重定向逻辑测试
- 实时设备状态

## 🔗 与后端集成

### 认证机制

使用 Session-based 认证，通过 cookies 维持登录状态：

```javascript
// axios配置
withCredentials: true  // 携带cookies
```

### API接口

所有API接口都复用现有的Flask后端：

- `POST /api/auth/login` - 用户登录
- `GET /api/auth/check` - 检查登录状态
- `GET /api/transactions` - 获取交易列表
- `POST /api/transactions` - 创建交易
- ...

## 🚀 部署指南

### 开发环境

1. 确保Flask后端在 `localhost:8000` 运行
2. 启动Vue开发服务器: `npm run dev`
3. 访问 `http://localhost:3000`

### 生产环境

1. 构建应用: `npm run build`
2. 将 `dist` 目录部署到Web服务器
3. 配置反向代理将 `/api/*` 请求转发到Flask后端

### Nginx配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 静态文件
    location / {
        root /path/to/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🐛 调试指南

### 常见问题

1. **API请求失败**
   - 检查Flask后端是否运行在8000端口
   - 查看浏览器控制台的网络请求
   - 确认CORS配置正确

2. **登录状态丢失**
   - 检查cookies是否正确设置
   - 确认 `withCredentials: true` 配置
   - 查看Session配置

3. **图表不显示**
   - 检查ECharts配置是否正确
   - 确认数据格式符合要求
   - 查看控制台错误信息

### 开发工具

- Vue DevTools: 调试Vue组件和状态
- Network面板: 查看API请求
- Console面板: 查看错误日志

## 📞 技术支持

如有问题，请检查：

1. 控制台错误信息
2. 网络请求状态
3. Vue DevTools状态
4. 后端日志文件

## 🎯 下一步计划

### 🔥 高优先级
- [ ] **个人资料编辑** - 用户信息修改和头像上传
- [ ] **应用设置页面** - 主题切换、货币设置、通知配置
- [ ] **财务目标管理** - 目标设置、进度跟踪、达成提醒

### ⚡ 中优先级
- [ ] **PWA支持** - Service Worker、离线缓存、桌面安装
- [ ] **数据导出** - Excel/CSV导出、数据备份
- [ ] **智能分析** - 支出预测、财务建议、趋势分析

### 🔮 长期规划
- [ ] **语音记账** - 语音识别输入交易信息
- [ ] **手势操作** - 滑动删除、下拉刷新等交互
- [ ] **多语言支持** - 国际化和本地化
- [ ] **云端同步** - 跨设备数据同步

---

**享受您的移动端财务管理体验！** 📱💰
