<template>
  <div ref="chartRef" :style="{ width: '100%', height: height }"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  options: {
    type: Object,
    required: true
  },
  height: {
    type: String,
    default: '300px'
  },
  theme: {
    type: String,
    default: 'light'
  }
})

const chartRef = ref()
let chart = null

onMounted(async () => {
  await nextTick()
  initChart()
})

onUnmounted(() => {
  if (chart) {
    chart.dispose()
    chart = null
  }
})

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chart = echarts.init(chartRef.value, props.theme)
  updateChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 更新图表
const updateChart = () => {
  if (chart && props.options) {
    // 设置默认配置
    const defaultOptions = {
      backgroundColor: 'transparent',
      textStyle: {
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif'
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: 'transparent',
        textStyle: {
          color: '#fff',
          fontSize: 12
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '10%',
        containLabel: true
      }
    }
    
    // 合并配置
    const mergedOptions = {
      ...defaultOptions,
      ...props.options
    }
    
    chart.setOption(mergedOptions, true)
  }
}

// 处理窗口大小变化
const handleResize = () => {
  if (chart) {
    chart.resize()
  }
}

// 监听配置变化
watch(() => props.options, updateChart, { deep: true })

// 监听主题变化
watch(() => props.theme, () => {
  if (chart) {
    chart.dispose()
    initChart()
  }
})

// 暴露图表实例
defineExpose({
  chart: () => chart,
  resize: handleResize
})
</script>

<style scoped>
/* 图表容器样式 */
</style>
