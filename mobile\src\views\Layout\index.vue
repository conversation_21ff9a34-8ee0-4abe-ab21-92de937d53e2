<template>
  <div class="layout">
    <!-- 主内容区域 -->
    <div class="main-content">
      <router-view />
    </div>
    
    <!-- 底部导航栏 -->
    <van-tabbar 
      v-model="activeTab" 
      @change="onTabChange"
      fixed
      placeholder
      safe-area-inset-bottom
    >
      <van-tabbar-item 
        v-for="tab in tabs" 
        :key="tab.name"
        :name="tab.name"
        :icon="tab.icon"
        :to="tab.path"
      >
        {{ tab.title }}
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 底部导航栏配置
const tabs = [
  {
    name: 'dashboard',
    title: '仪表盘',
    icon: 'chart-trending-o',
    path: '/'
  },
  {
    name: 'transactions',
    title: '交易',
    icon: 'bill-o',
    path: '/transactions'
  },
  {
    name: 'accounts',
    title: '账户',
    icon: 'credit-pay',
    path: '/accounts'
  },
  {
    name: 'budgets',
    title: '预算',
    icon: 'bag-o',
    path: '/budgets'
  },
  {
    name: 'profile',
    title: '我的',
    icon: 'user-o',
    path: '/profile'
  }
]

// 当前激活的标签
const activeTab = ref('dashboard')

// 根据当前路由设置激活标签
const updateActiveTab = () => {
  const currentPath = route.path
  const tab = tabs.find(t => t.path === currentPath)
  if (tab) {
    activeTab.value = tab.name
  }
}

// 监听路由变化
watch(() => route.path, updateActiveTab, { immediate: true })

// 标签切换处理
const onTabChange = (name) => {
  const tab = tabs.find(t => t.name === name)
  if (tab && route.path !== tab.path) {
    router.push(tab.path)
  }
}
</script>

<style scoped>
.layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 50px; /* 为底部导航栏留出空间 */
}

/* 自定义底部导航栏样式 */
:deep(.van-tabbar) {
  background-color: #fff;
  border-top: 1px solid #ebedf0;
}

:deep(.van-tabbar-item) {
  color: #969799;
}

:deep(.van-tabbar-item--active) {
  color: #1989fa;
}

:deep(.van-tabbar-item__text) {
  font-size: 12px;
  margin-top: 4px;
}

:deep(.van-tabbar-item__icon) {
  font-size: 20px;
  margin-bottom: 2px;
}
</style>
