<template>
  <div class="subscription-detail-page">
    <van-nav-bar 
      title="订阅详情" 
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="$router.back()"
    >
      <template #right>
        <van-icon 
          name="edit" 
          size="18" 
          @click="editSubscription"
        />
      </template>
    </van-nav-bar>

    <div v-if="loading" class="loading-container">
      <van-loading size="24px" vertical>加载中...</van-loading>
    </div>

    <div v-else-if="subscription" class="page-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <h1 class="page-title">订阅详情</h1>
        <p class="page-description">查看和管理您的订阅信息</p>
      </div>

      <!-- 订阅信息卡片 -->
      <div class="subscription-card">
        <div class="subscription-header">
          <div class="service-info">
            <ServiceLogo
              :service-name="subscription.name"
              :service-type="subscription.service_type"
              size="medium"
            />
            <div class="subscription-details">
              <h2 class="subscription-name">{{ subscription.name }}</h2>
              <p class="service-type">{{ getServiceTypeLabel(subscription.service_type) }}</p>
            </div>
          </div>
          <div class="subscription-status">
            <van-tag
              v-if="subscription.is_due_soon"
              type="warning"
              class="status-tag"
            >
              {{ subscription.days_until_billing }}天后到期
            </van-tag>
            <van-tag
              v-else-if="!subscription.is_active"
              type="default"
              class="status-tag"
            >
              已暂停
            </van-tag>
            <van-tag
              v-else
              type="success"
              class="status-tag"
            >
              正常
            </van-tag>
          </div>
        </div>

        <div class="subscription-amount">
          <div class="amount-display">
            <span class="currency">{{ getCurrencySymbol(subscription.currency) }}</span>
            <span class="amount">{{ subscription.amount }}</span>
            <span class="cycle">{{ getCycleText(subscription.billing_cycle) }}</span>
          </div>
          <div class="billing-info">
            <span class="next-billing">
              下次扣费: {{ formatDate(subscription.next_billing_date) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 详细信息 -->
      <van-cell-group inset title="📋 详细信息">
        <van-cell title="服务类型" :value="getServiceTypeLabel(subscription.service_type)">
          <template #icon>
            <div class="cell-icon">
              <van-icon name="apps-o" />
            </div>
          </template>
        </van-cell>
        <van-cell title="计费周期" :value="getCycleText(subscription.billing_cycle)">
          <template #icon>
            <div class="cell-icon">
              <van-icon name="clock-o" />
            </div>
          </template>
        </van-cell>
        <van-cell title="开始日期" :value="formatDate(subscription.start_date)">
          <template #icon>
            <div class="cell-icon">
              <van-icon name="calendar-o" />
            </div>
          </template>
        </van-cell>
        <van-cell title="下次扣费" :value="formatDate(subscription.next_billing_date)">
          <template #icon>
            <div class="cell-icon">
              <van-icon name="credit-pay" />
            </div>
          </template>
        </van-cell>
        <van-cell title="货币类型" :value="subscription.currency">
          <template #icon>
            <div class="cell-icon">
              <van-icon name="gold-coin-o" />
            </div>
          </template>
        </van-cell>
        <van-cell
          v-if="subscription.description"
          title="备注"
          :value="subscription.description"
        >
          <template #icon>
            <div class="cell-icon">
              <van-icon name="notes-o" />
            </div>
          </template>
        </van-cell>
      </van-cell-group>

      <!-- 操作按钮 -->
      <div class="action-section">
        <h3 class="action-title">🛠️ 操作</h3>
        <div class="action-buttons">
          <van-button
            v-if="subscription.is_due_soon"
            type="primary"
            block
            @click="renewSubscription"
            :loading="renewLoading"
            class="action-btn primary-btn"
          >
            <van-icon name="credit-pay" />
            立即续费
          </van-button>

          <van-button
            type="warning"
            block
            plain
            @click="toggleSubscription"
            :loading="toggleLoading"
            class="action-btn warning-btn"
          >
            <van-icon :name="subscription.is_active ? 'pause-circle-o' : 'play-circle-o'" />
            {{ subscription.is_active ? '暂停订阅' : '恢复订阅' }}
          </van-button>

          <van-button
            type="danger"
            block
            plain
            @click="deleteSubscription"
            :loading="deleteLoading"
            class="action-btn danger-btn"
          >
            <van-icon name="delete-o" />
            删除订阅
          </van-button>
        </div>
      </div>
    </div>

    <div v-else class="error-container">
      <van-empty description="订阅不存在或已被删除" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useSubscriptionsStore } from '@/stores/subscriptions'
import { showToast, showConfirmDialog } from 'vant'
import { getServiceConfig } from '@/config/serviceIcons'
import { getCurrencySymbol } from '@/config/currencies'
import ServiceLogo from '@/components/ServiceLogo.vue'

const route = useRoute()
const router = useRouter()
const subscriptionsStore = useSubscriptionsStore()

const subscription = ref(null)
const loading = ref(true)
const renewLoading = ref(false)
const toggleLoading = ref(false)
const deleteLoading = ref(false)

// 获取服务类型标签
const getServiceTypeLabel = (serviceType) => {
  if (!serviceType) return '未知服务'

  const config = getServiceConfig(serviceType)
  return config.name
}

// 获取周期文本
const getCycleText = (cycle) => {
  const cycleMap = {
    'monthly': '月',
    'quarterly': '季',
    'yearly': '年',
    'weekly': '周'
  }
  return cycleMap[cycle] || cycle
}

// 获取货币符号 - 使用统一配置
// getCurrencySymbol 已从配置文件导入

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 编辑订阅
const editSubscription = () => {
  router.push(`/subscription/edit/${route.params.id}`)
}

// 续费订阅
const renewSubscription = async () => {
  try {
    await showConfirmDialog({
      title: '确认续费',
      message: '确定要续费这个订阅吗？'
    })

    renewLoading.value = true
    await subscriptionsStore.renewSubscription(subscription.value.id)
    
    showToast({
      message: '续费成功',
      type: 'success'
    })
    
    // 重新获取订阅详情
    await fetchSubscriptionDetail()
    
  } catch (error) {
    if (error !== 'cancel') {
      showToast({
        message: error.message || '续费失败',
        type: 'fail'
      })
    }
  } finally {
    renewLoading.value = false
  }
}

// 切换订阅状态
const toggleSubscription = async () => {
  try {
    const action = subscription.value.is_active ? '暂停' : '恢复'
    await showConfirmDialog({
      title: `确认${action}`,
      message: `确定要${action}这个订阅吗？`
    })

    toggleLoading.value = true
    await subscriptionsStore.updateSubscription(subscription.value.id, {
      is_active: !subscription.value.is_active
    })
    
    showToast({
      message: `${action}成功`,
      type: 'success'
    })
    
    // 重新获取订阅详情
    await fetchSubscriptionDetail()
    
  } catch (error) {
    if (error !== 'cancel') {
      showToast({
        message: error.message || '操作失败',
        type: 'fail'
      })
    }
  } finally {
    toggleLoading.value = false
  }
}

// 删除订阅
const deleteSubscription = async () => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: '删除后无法恢复，确定要删除这个订阅吗？',
      confirmButtonText: '确认删除',
      confirmButtonColor: '#ee0a24'
    })

    deleteLoading.value = true
    await subscriptionsStore.deleteSubscription(subscription.value.id)
    
    showToast({
      message: '删除成功',
      type: 'success'
    })
    
    router.back()
    
  } catch (error) {
    if (error !== 'cancel') {
      showToast({
        message: error.message || '删除失败',
        type: 'fail'
      })
    }
  } finally {
    deleteLoading.value = false
  }
}

// 获取订阅详情
const fetchSubscriptionDetail = async () => {
  try {
    loading.value = true
    subscription.value = await subscriptionsStore.getSubscription(route.params.id)
  } catch (error) {
    showToast({
      message: '获取订阅详情失败',
      type: 'fail'
    })
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchSubscriptionDetail()
})
</script>

<style scoped>
.subscription-detail-page {
  background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
  min-height: 100vh;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
}

.page-container {
  padding: 16px;
}

/* 页面头部样式 */
.page-header {
  text-align: center;
  padding: 24px 0 32px;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  margin: -16px -16px 24px -16px;
  border-radius: 0 0 24px 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

/* 订阅卡片样式 */
.subscription-card {
  background: #ffffff;
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 24px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.subscription-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.service-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.service-icon {
  font-size: 32px;
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 16px;
  color: #ffffff;
}

.subscription-details {
  flex: 1;
}

.subscription-name {
  margin: 0 0 4px 0;
  color: #1f2937;
  font-size: 20px;
  font-weight: 700;
}

.service-type {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.subscription-status {
  margin-left: 16px;
}

.status-tag {
  border-radius: 12px;
  font-weight: 500;
}

.subscription-amount {
  text-align: center;
  padding: 20px 0 0;
  border-top: 1px solid #e2e8f0;
}

.amount-display {
  margin-bottom: 8px;
}

.currency {
  font-size: 18px;
  color: #3b82f6;
  margin-right: 4px;
  font-weight: 600;
}

.amount {
  font-size: 36px;
  font-weight: 800;
  color: #1f2937;
}

.cycle {
  font-size: 16px;
  color: #6b7280;
  margin-left: 4px;
  font-weight: 500;
}

.billing-info {
  margin-top: 8px;
}

.next-billing {
  font-size: 12px;
  color: #6b7280;
  background: #f1f5f9;
  padding: 4px 12px;
  border-radius: 12px;
  display: inline-block;
}

/* 单元格图标样式 */
.cell-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
  border-radius: 6px;
  color: #3b82f6;
  font-size: 12px;
  margin-right: 12px;
}

/* 操作部分样式 */
.action-section {
  margin-top: 24px;
}

.action-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
  padding: 0 4px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-btn {
  height: 48px;
  border-radius: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.98);
}

.primary-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  color: #ffffff;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.warning-btn {
  border-color: #f59e0b;
  color: #f59e0b;
}

.warning-btn:hover {
  background: #fef3c7;
}

.danger-btn {
  border-color: #ef4444;
  color: #ef4444;
}

.danger-btn:hover {
  background: #fef2f2;
}

/* 自定义导航栏样式 */
:deep(.van-nav-bar) {
  background: #ffffff;
  border-bottom: 1px solid #e2e8f0;
}

:deep(.van-nav-bar__title) {
  color: #1f2937;
  font-weight: 600;
}

:deep(.van-nav-bar .van-icon) {
  color: #3b82f6;
}

/* 自定义单元格样式 */
:deep(.van-cell-group) {
  background: #ffffff;
  border-radius: 16px;
  margin-bottom: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

:deep(.van-cell-group__title) {
  color: #1f2937;
  font-weight: 600;
  padding: 16px 16px 8px;
  font-size: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  margin: 0;
  border-bottom: 1px solid #e2e8f0;
}

:deep(.van-cell) {
  background: #ffffff;
  color: #1f2937;
  border-bottom: 1px solid #f1f5f9;
  padding: 16px;
}

:deep(.van-cell:last-child) {
  border-bottom: none;
}

:deep(.van-cell__title) {
  color: #374151;
  font-weight: 500;
  font-size: 14px;
}

:deep(.van-cell__value) {
  color: #1f2937;
  font-size: 14px;
}

/* 加载状态优化 */
:deep(.van-loading__spinner) {
  color: #3b82f6;
}

/* 空状态优化 */
:deep(.van-empty__description) {
  color: #6b7280;
}
</style>
