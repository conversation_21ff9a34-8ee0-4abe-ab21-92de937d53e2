# 模板文件结构说明

## 📁 文件夹结构

```
templates/
├── README.md                    # 本说明文件
├── index.html                   # 主应用页面
├── auth/                        # 用户认证相关页面
│   └── login.html              # 登录/注册页面
├── demo/                        # 演示和展示页面
│   └── progress_bar_demo.html  # 进度条设计演示
└── test/                        # 测试和开发页面
    ├── test_progress.html       # 进度测试页面
    └── test_user_functions.html # 用户功能测试页面
```

## 🎯 模块分类说明

### 📱 主应用 (Root Level)
- **index.html** - 主要的财务管理应用界面
  - 包含仪表盘、交易记录、账户管理等核心功能
  - 用户登录后的主要工作界面

### 🔐 认证模块 (auth/)
- **login.html** - 用户登录和注册页面
  - 支持用户登录、注册功能
  - 密码强度检查、表单验证
  - 美观的认证界面设计

### 🎨 演示模块 (demo/)
- **progress_bar_demo.html** - 进度条设计演示
  - 展示不同状态的预算进度条
  - 包含正常、警告、超支等状态
  - 响应式设计演示

### 🧪 测试模块 (test/)
- **test_progress.html** - 进度功能测试页面
  - 用于测试进度条相关功能
  - 开发和调试使用

- **test_user_functions.html** - 用户功能测试页面
  - 测试登录、注册、用户管理等API
  - 开发和调试使用

## 🔗 路由配置

### 主要访问路径：
- **主页**: `http://localhost:8000/` → `templates/index.html`
- **登录页**: `http://localhost:8000/login.html` → `templates/auth/login.html`
- **登录页**: `http://localhost:8000/templates/auth/login.html` → `templates/auth/login.html`

### 演示和测试页面：
- **进度条演示**: `http://localhost:8000/templates/demo/progress_bar_demo.html`
- **用户功能测试**: `http://localhost:8000/templates/test/test_user_functions.html`
- **进度测试**: `http://localhost:8000/templates/test/test_progress.html`

## 📝 文件路径更新

### CSS 资源路径
由于文件移动到子文件夹，相对路径已更新：

```html
<!-- 原路径 -->
<link rel="stylesheet" href="assets/css/components/auth.css">

<!-- 新路径 (auth/ 文件夹中) -->
<link rel="stylesheet" href="../../assets/css/components/auth.css">

<!-- 新路径 (demo/ 文件夹中) -->
<link rel="stylesheet" href="../../assets/css/main.css">
```

### JavaScript 跳转路径
应用中的页面跳转路径已更新：

```javascript
// 原路径
window.location.href = '/login.html';

// 新路径
window.location.href = '/templates/auth/login.html';
```

## 🚀 使用指南

### 开发者
1. **添加新的认证页面**: 放入 `templates/auth/` 文件夹
2. **添加新的演示页面**: 放入 `templates/demo/` 文件夹
3. **添加新的测试页面**: 放入 `templates/test/` 文件夹
4. **记住更新相对路径**: 根据文件夹层级调整 CSS/JS 路径

### 用户
- 正常使用不受影响，所有功能保持原有访问方式
- 登录页面路径保持兼容性

## 🔧 技术细节

### Flask 路由配置
```python
@app.route('/')
def index():
    return send_from_directory('templates', 'index.html')

@app.route('/login.html')
def login_redirect():
    return send_from_directory('templates/auth', 'login.html')

@app.route('/templates/auth/login.html')
def login():
    return send_from_directory('templates/auth', 'login.html')
```

### 静态文件服务
```python
@app.route('/templates/<path:path>')
def serve_templates(path):
    return send_from_directory('templates', path)
```

## 📋 维护说明

1. **添加新页面时**：
   - 选择合适的分类文件夹
   - 更新相对路径引用
   - 必要时添加新的路由

2. **移动现有页面时**：
   - 更新所有相关的路径引用
   - 测试页面功能是否正常
   - 更新文档说明

3. **删除页面时**：
   - 检查是否有其他页面引用
   - 移除相关路由配置
   - 更新导航链接

## ✅ 优势

1. **模块化管理**: 按功能分类，便于维护
2. **清晰结构**: 开发者容易找到相关文件
3. **扩展性好**: 新增功能时有明确的放置位置
4. **向后兼容**: 保持原有访问路径的兼容性

这种模块化结构使项目更加有序，便于团队协作和长期维护。
