<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

onMounted(async () => {
  // 应用启动时检查登录状态
  await authStore.checkAuth()
})
</script>

<style>
#app {
  height: 100vh;
  background-color: #f7f8fa;
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 隐藏滚动条但保持滚动功能 */
::-webkit-scrollbar {
  display: none;
}

/* 移动端适配 */
html {
  font-size: 16px;
}

@media screen and (max-width: 375px) {
  html {
    font-size: 14px;
  }
}

@media screen and (min-width: 414px) {
  html {
    font-size: 18px;
  }
}
</style>
