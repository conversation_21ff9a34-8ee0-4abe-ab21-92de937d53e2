# 📊 钱管家 - 个人财务管理系统

一个现代化的个人财务管理系统，支持**Web版本**、**移动版本**和**Telegram机器人**三个平台，让财务管理变得简单高效。

## 📚 文档索引

### 📖 核心文档
- **[系统概述](README.md)** - 本文档，系统功能和使用说明
- **[用户登录指南](LOGIN_GUIDE.md)** - 多用户登录功能详细说明
- **[机器人用户绑定](BOT_USER_BINDING.md)** - Telegram机器人用户绑定功能

### 🔧 开发文档
- **[全平台功能总结](PLATFORM_FEATURES_SUMMARY.md)** - 三个平台功能状态总结
- **[用户功能状态](USER_FUNCTIONS_STATUS.md)** - 功能开发和测试状态

## ✨ 主要功能

### 💰 财务管理
- **收支记录**: 支持多种收入和支出类别
- **账户管理**: 银行账户、现金、信用卡等多账户支持
- **多货币支持**: MYR、CNY、USD、EUR等8种货币
- **附件支持**: 上传收据、发票等凭证

### 📊 预算管理
- **预算设置**: 按类别设置月度预算
- **实时监控**: 预算使用情况实时跟踪
- **智能提醒**: 80%预警，100%危险提醒
- **可视化图表**: 直观的预算使用图表

### 🎯 财务目标
- **目标设置**: 设定储蓄和投资目标
- **进度跟踪**: 目标完成进度可视化
- **资产配置**: 净资产分配管理

### 🤖 Telegram机器人
- **交互式记账**: 内联键盘操作，简单直观
- **账户选择**: 支持多账户记账
- **查询功能**: 余额查询、预算查询
- **用户绑定**: 与网站账户绑定同步

## 🛠️ 技术栈

### 🌐 Web版本
- **后端**: Python Flask + SQLite
- **前端**: HTML5/CSS3/JavaScript (原生)
- **图表**: Chart.js

### 📱 移动版本
- **框架**: Vue 3 + Vant + Pinia
- **构建**: Vite
- **图表**: ECharts

### 🤖 Telegram机器人
- **语言**: Python
- **API**: Telegram Bot API (原生HTTP)

## 🚀 快速开始

### 🎯 一键启动
```bash
# 启动所有服务（推荐）
start_all.bat

# 或单独启动
start_web.bat      # Web版本 (端口 8000)
start_mobile.bat   # 移动版本 (端口 3000)
start_bot.bat      # Telegram机器人
```

### 📋 环境要求
- **Python 3.8+** (Web版本 + 机器人)
- **Node.js 16+** (移动版本)
- **SQLite** (数据库)

### ⚙️ 配置步骤
1. 安装Python依赖：`pip install -r requirements.txt`
2. 安装Node.js依赖：`cd mobile && npm install`
3. 配置环境变量：编辑 `.env` 文件
4. 配置Telegram Bot Token（如需使用机器人）

## 🎮 使用方式

### 🌐 Web版本
访问 `http://localhost:8000` 使用完整的桌面端功能

### 📱 移动版本
访问 `http://localhost:3000` 使用移动端优化界面

### 🤖 Telegram机器人
- 发送 `/start` 开始使用
- 发送 `记账` 开始交互式记账
- 发送 `余额` 查询账户余额
- 发送 `预算` 查询预算使用情况

## 📁 项目结构

```
Project_3/
├── web/                    # Web版本 (Flask + HTML/CSS/JS)
├── mobile/                 # 移动版本 (Vue3 + Vant)
├── bot/                    # Telegram机器人
├── data/                   # 数据文件目录
│   ├── finance.db         # 主数据库（三版本共享）
│   └── bot.db             # Bot专用数据库
├── docs/                   # 项目文档
├── logs/                   # 日志文件
├── venv/                   # 共享Python虚拟环境
├── .env                    # 环境配置文件
├── requirements.txt        # 统一Python依赖
└── 启动脚本
```

## 🔧 配置示例

```env
# 基础配置
BOT_DEBUG=True
WEB_API_BASE_URL=http://localhost:8000/api

# Telegram机器人配置
TELEGRAM_ENABLED=True
TELEGRAM_BOT_TOKEN=你的Bot Token

# 预算检查配置
BUDGET_WARNING_THRESHOLD=0.8
BUDGET_DANGER_THRESHOLD=1.0
```

## 📊 功能特性

### 收入类别
- 工资、奖金、投资收益、礼金、退款、其他收入

### 支出类别
- 储蓄、固定、流动、债务

### 账户类型
- 银行账户🏦、现金💵、信用卡💳

### 支持货币
- MYR、CNY、USD、EUR、GBP、JPY、KRW、SGD

## 🎉 版本特性

### 🌟 当前版本亮点
- ✅ **三平台统一** - Web、移动、Bot数据同步
- ✅ **现代化技术栈** - Vue3、Flask、Telegram Bot
- ✅ **完整财务管理** - 记账、预算、目标、统计
- ✅ **智能自适应** - 设备检测和端口自动切换
- ✅ **用户友好** - 多种交互方式适应不同场景

## 📚 完整文档列表

### 📖 核心使用文档
- **[系统概述](README.md)** - 本文档，系统功能和使用说明
- **[用户登录指南](LOGIN_GUIDE.md)** - 多用户登录功能详细说明
- **[机器人用户绑定](BOT_USER_BINDING.md)** - Telegram机器人用户绑定功能

### 🔧 开发维护文档
- **[全平台功能总结](PLATFORM_FEATURES_SUMMARY.md)** - 三个平台功能状态总结
- **[用户功能状态](USER_FUNCTIONS_STATUS.md)** - 功能开发和测试状态

## 📞 支持

如有问题或建议，请查看 [文档](.) 或创建 Issue。

---

**钱管家** - 让财务管理变得简单高效！ 💰✨
