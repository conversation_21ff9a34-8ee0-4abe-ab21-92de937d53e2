import{a as g,l as b,m as q,j as P,c as x,e as c,d as t,f as d,w as _,t as v,n as B,p as T,g as R,o as u,i as U,F as X,k as G}from"./index--MNqwREY.js";import{u as H}from"./transactions-F5TqZ8Qm.js";import{B as O}from"./BaseChart-BEnmQZd1.js";import{f as h}from"./format-wz8GKlWC.js";import{_ as J}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./transactions-CrdnwW_k.js";const K={class:"category-detail-page"},Q={class:"page-container"},Z={class:"type-selector"},ee={class:"overview-card"},te={class:"overview-item"},ae={class:"overview-label"},oe={class:"overview-value"},ne={class:"overview-item"},se={class:"overview-value"},le={class:"overview-item"},re={class:"overview-value"},ie={class:"chart-container"},ce={class:"chart-header"},ve={class:"chart-content"},de={key:1,class:"chart-loading"},ue={class:"category-list"},me={class:"list-header"},pe={class:"category-rank"},ge={class:"category-percentage"},_e={class:"trend-container"},he={class:"trend-content"},fe={key:1,class:"chart-loading"},ye={__name:"CategoryDetail",setup(be){const z=R(),w=H(),F=g(!1),i=g("expense"),f=g("desc"),C=g(null),k=g(null),m=b(()=>{const o=w.transactions.filter(n=>n.type===i.value),e={};return o.forEach(n=>{e[n.category]||(e[n.category]={name:n.category,amount:0,count:0}),e[n.category].amount+=n.amount,e[n.category].count+=1}),Object.values(e)}),M=b(()=>[...m.value].sort((o,e)=>f.value==="desc"?e.amount-o.amount:o.amount-e.amount)),S=b(()=>m.value.reduce((o,e)=>o+e.amount,0)),j=b(()=>m.value.length>0?S.value/m.value.length:0);q(async()=>{await A()}),P(i,()=>{$()});const A=async()=>{F.value=!0;try{await w.fetchTransactions(),$()}catch(o){console.error("加载数据失败:",o)}finally{F.value=!1}},$=()=>{E(),N()},E=()=>{const o=m.value.map(e=>({name:e.name,value:e.amount})).sort((e,n)=>n.value-e.value);C.value={title:{text:`${i.value==="expense"?"支出":"收入"}分布`,left:"center",textStyle:{fontSize:16,fontWeight:"bold"}},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left",data:o.map(e=>e.name)},series:[{name:`${i.value==="expense"?"支出":"收入"}分类`,type:"pie",radius:["40%","70%"],center:["60%","50%"],data:o,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},label:{formatter:"{b}: {d}%"}}]}},N=()=>{const o=w.transactions.filter(a=>a.type===i.value),e={},n=new Date;for(let a=5;a>=0;a--){const s=new Date(n.getFullYear(),n.getMonth()-a,1),l=`${s.getFullYear()}-${String(s.getMonth()+1).padStart(2,"0")}`,r=`${s.getMonth()+1}月`;e[l]={label:r,categories:{}}}o.forEach(a=>{const s=new Date(a.date),l=`${s.getFullYear()}-${String(s.getMonth()+1).padStart(2,"0")}`;e[l]&&(e[l].categories[a.category]||(e[l].categories[a.category]=0),e[l].categories[a.category]+=a.amount)});const p=[...new Set(o.map(a=>a.category))],D=Object.values(e).map(a=>a.label),y=p.map((a,s)=>({name:a,type:"line",data:Object.values(e).map(l=>l.categories[a]||0),smooth:!0,itemStyle:{color:V(s)}}));k.value={title:{text:"月度趋势",left:"center",textStyle:{fontSize:16,fontWeight:"bold"}},tooltip:{trigger:"axis",formatter:a=>{let s=`${a[0].axisValue}<br/>`;return a.forEach(l=>{s+=`${l.seriesName}: ${h(l.value)}<br/>`}),s}},legend:{data:p,bottom:10},xAxis:{type:"category",data:D},yAxis:{type:"value",axisLabel:{formatter:a=>h(a,"",0)}},series:y}},V=o=>{const e=["#1989fa","#07c160","#ee0a24","#ff976a","#91d5ff","#b37feb","#ffc53d","#36cfc9","#73d13d","#ff85c0"];return e[o%e.length]},L=()=>{$()},Y=()=>{f.value=f.value==="desc"?"asc":"desc"},I=o=>{z.push({path:"/transactions",query:{category:o.name,type:i.value}})};return(o,e)=>{const n=d("van-nav-bar"),p=d("van-tab"),D=d("van-tabs"),y=d("van-loading"),a=d("van-button"),s=d("van-cell"),l=d("van-cell-group");return u(),x("div",K,[c(n,{title:"分类统计","left-arrow":"",onClickLeft:e[0]||(e[0]=r=>o.$router.back()),fixed:"",placeholder:""}),t("div",Q,[t("div",Z,[c(D,{active:i.value,"onUpdate:active":e[1]||(e[1]=r=>i.value=r),onChange:L},{default:_(()=>[c(p,{title:"支出分类",name:"expense"}),c(p,{title:"收入分类",name:"income"})]),_:1},8,["active"])]),t("div",ee,[t("div",te,[t("div",ae,"总"+v(i.value==="expense"?"支出":"收入"),1),t("div",oe,v(B(h)(S.value)),1)]),t("div",ne,[e[2]||(e[2]=t("div",{class:"overview-label"},"分类数量",-1)),t("div",se,v(m.value.length),1)]),t("div",le,[e[3]||(e[3]=t("div",{class:"overview-label"},"平均金额",-1)),t("div",re,v(B(h)(j.value)),1)])]),t("div",ie,[t("div",ce,[t("h3",null,v(i.value==="expense"?"支出":"收入")+"分布",1)]),t("div",ve,[C.value?(u(),T(O,{key:0,options:C.value,height:"300px"},null,8,["options"])):(u(),x("div",de,[c(y,{size:"24px"}),e[4]||(e[4]=t("span",null,"加载中...",-1))]))])]),t("div",ue,[t("div",me,[e[5]||(e[5]=t("h3",null,"分类详情",-1)),c(a,{size:"mini",type:"primary",plain:"",onClick:Y},{default:_(()=>[U(v(f.value==="desc"?"金额↓":"金额↑"),1)]),_:1})]),c(l,{inset:""},{default:_(()=>[(u(!0),x(X,null,G(M.value,(r,W)=>(u(),T(s,{key:r.name,title:r.name,label:`${r.count} 笔交易`,value:B(h)(r.amount),"is-link":"",onClick:xe=>I(r)},{icon:_(()=>[t("div",pe,v(W+1),1)]),"right-icon":_(()=>[t("div",ge,v((r.amount/S.value*100).toFixed(1))+"% ",1)]),_:2},1032,["title","label","value","onClick"]))),128))]),_:1})]),t("div",_e,[e[7]||(e[7]=t("div",{class:"trend-header"},[t("h3",null,"月度趋势")],-1)),t("div",he,[k.value?(u(),T(O,{key:0,options:k.value,height:"240px"},null,8,["options"])):(u(),x("div",fe,[c(y,{size:"24px"}),e[6]||(e[6]=t("span",null,"加载中...",-1))]))])])])])}}},Be=J(ye,[["__scopeId","data-v-a4334e2c"]]);export{Be as default};
