#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
个人财务管理系统后端
提供API接口，连接SQLite数据库

此应用程序可以通过 start_app.bat 启动，它会自动设置虚拟环境、安装依赖并启动服务器。
"""

from flask import Flask, request, jsonify, send_from_directory, g, session
from flask_cors import CORS
from flask_session import Session
import sqlite3
import os
import sys
import datetime
import uuid
import logging
import re
from functools import wraps

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.database_config import MAIN_DB_PATH

# 确保logs目录存在
os.makedirs('logs', exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/finance_app.log')
    ]
)
logger = logging.getLogger(__name__)

# 设备检测函数
def is_mobile_device(user_agent):
    """检测是否为移动设备"""
    if not user_agent:
        return False

    mobile_patterns = [
        r'Mobile', r'Android', r'iPhone', r'iPad', r'iPod',
        r'BlackBerry', r'IEMobile', r'Opera Mini', r'webOS',
        r'Windows Phone'
    ]

    for pattern in mobile_patterns:
        if re.search(pattern, user_agent, re.IGNORECASE):
            return True
    return False

# 创建Flask应用
app = Flask(__name__, static_folder='static', static_url_path='/static')

# 配置CORS以支持手机版和域名访问
CORS(app,
     origins=[
         'http://localhost:3000', 'http://localhost:8000',
         'http://127.0.0.1:3000', 'http://127.0.0.1:8000',
         'https://mrtan.pro', 'http://mrtan.pro',
         'https://www.mrtan.pro', 'http://www.mrtan.pro'
     ],
     supports_credentials=True,
     allow_headers=['Content-Type', 'Authorization', 'X-Requested-With'],
     methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])

# 配置应用
app.config.update(
    SECRET_KEY=os.environ.get('SECRET_KEY', 'dev_key_please_change_in_production'),
    API_TOKEN=os.environ.get('API_TOKEN', 'finance_app_token'),
    DEBUG=os.environ.get('DEBUG', 'True').lower() in ('true', '1', 't'),
    # Session配置
    SESSION_TYPE='filesystem',
    SESSION_PERMANENT=False,
    SESSION_USE_SIGNER=True,
    SESSION_KEY_PREFIX='finance_app:',
    SESSION_FILE_DIR='./sessions',
    PERMANENT_SESSION_LIFETIME=datetime.timedelta(days=7)
)

# 确保sessions目录存在
os.makedirs('./sessions', exist_ok=True)

# 初始化Session
Session(app)

def clear_all_sessions():
    """清除所有会话文件"""
    try:
        sessions_dir = './sessions'
        if os.path.exists(sessions_dir):
            for filename in os.listdir(sessions_dir):
                file_path = os.path.join(sessions_dir, filename)
                if os.path.isfile(file_path):
                    os.remove(file_path)
            logger.info("已清除所有会话文件")
    except Exception as e:
        logger.error(f"清除会话文件失败: {e}")

# 使用统一的数据库路径配置
DB_PATH = MAIN_DB_PATH

# 如果数据库不存在，清除所有会话
if not os.path.exists(DB_PATH):
    clear_all_sessions()

# 添加缓存控制，防止浏览器缓存API响应
@app.after_request
def add_cache_control(response):
    """添加缓存控制头部，防止浏览器缓存API响应"""
    if request.path.startswith('/api/'):
        response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
    return response

# 身份验证装饰器
def login_required(f):
    """用户登录验证装饰器"""
    @wraps(f)
    def decorated(*args, **kwargs):
        # 检查session中是否有登录信息
        if not session.get('logged_in') or not session.get('user_id'):
            logger.warning(f"未登录用户访问API: {request.path}")
            return jsonify({'success': False, 'error': '请先登录'}), 401

        # 检查数据库文件是否存在
        if not os.path.exists(DB_PATH):
            logger.warning("数据库文件不存在，清除会话")
            session.clear()
            return jsonify({'success': False, 'error': '请先登录'}), 401

        # 验证数据库中用户是否仍然存在且有效
        try:
            user_id = session.get('user_id')
            conn = sqlite3.connect(DB_PATH)
            conn.row_factory = sqlite3.Row

            user = conn.execute('''
                SELECT id FROM users
                WHERE id = ? AND is_active = 1
            ''', (user_id,)).fetchone()

            conn.close()

            if not user:
                # 用户不存在或已被禁用，清除会话
                logger.warning(f"用户 {user_id} 不存在或已被禁用，清除会话")
                session.clear()
                return jsonify({'success': False, 'error': '请先登录'}), 401

        except sqlite3.Error as db_error:
            logger.error(f"数据库查询失败: {db_error}")
            session.clear()
            return jsonify({'success': False, 'error': '请先登录'}), 401

        return f(*args, **kwargs)
    return decorated

def auth_required(f):
    """API身份验证装饰器（保留原有功能）"""
    @wraps(f)
    def decorated(*args, **kwargs):
        # 检查是否启用了身份验证
        if not app.config.get('API_AUTH_ENABLED', False):
            return f(*args, **kwargs)

        auth_token = request.headers.get('X-Auth-Token')
        # 简单的令牌验证
        if not auth_token or auth_token != app.config.get('API_TOKEN'):
            logger.warning(f"未授权的API访问尝试: {request.path}")
            return jsonify({'error': '未授权访问'}), 401
        return f(*args, **kwargs)
    return decorated

def get_current_user_id():
    """获取当前登录用户ID"""
    return session.get('user_id')



def get_db_connection():
    """获取数据库连接（使用连接池模式）"""
    if 'db' not in g:
        logger.debug("创建新的数据库连接")
        g.db = sqlite3.connect(DB_PATH)
        g.db.row_factory = sqlite3.Row  # 返回字典形式的结果
        # 启用外键约束
        g.db.execute('PRAGMA foreign_keys = ON')
        # 优化查询性能
        g.db.execute('PRAGMA journal_mode = WAL')
        g.db.execute('PRAGMA synchronous = NORMAL')
    return g.db

@app.teardown_appcontext
def close_db(e=None):
    """关闭数据库连接"""
    db = g.pop('db', None)
    if db is not None:
        db.close()
        logger.debug("关闭数据库连接")

def init_db():
    """初始化数据库"""
    # 直接使用sqlite3连接，不使用g对象
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # 创建用户表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        username TEXT UNIQUE NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        display_name TEXT NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        last_login_at TEXT
    )
    ''')

    # 创建交易表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS transactions (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        date TEXT NOT NULL,
        type TEXT NOT NULL,
        category TEXT NOT NULL,
        description TEXT,
        account TEXT NOT NULL,
        amount REAL NOT NULL,
        currency TEXT NOT NULL,
        attachment TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )
    ''')

    # 创建账户表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS accounts (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        currency TEXT NOT NULL,
        initial_balance REAL NOT NULL,
        description TEXT,
        icon TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )
    ''')

    # 创建预算表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS budgets (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        project_name TEXT NOT NULL,
        category TEXT NOT NULL,
        amount REAL NOT NULL,
        period TEXT NOT NULL,
        start_date TEXT NOT NULL,
        end_date TEXT NOT NULL,
        currency TEXT NOT NULL,
        description TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )
    ''')

    # 创建目标表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS goals (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        name TEXT NOT NULL,
        target_amount REAL NOT NULL,
        current_amount REAL NOT NULL,
        target_date TEXT NOT NULL,
        currency TEXT NOT NULL,
        description TEXT,
        icon TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )
    ''')

    # 创建订阅表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS subscriptions (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        name TEXT NOT NULL,
        service_type TEXT NOT NULL,
        amount REAL NOT NULL,
        currency TEXT NOT NULL,
        billing_cycle TEXT NOT NULL,
        start_date TEXT NOT NULL,
        next_billing_date TEXT NOT NULL,
        description TEXT,
        icon TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )
    ''')

    # 创建订阅提醒表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS subscription_reminders (
        id TEXT PRIMARY KEY,
        subscription_id TEXT NOT NULL,
        user_id TEXT NOT NULL,
        reminder_date TEXT NOT NULL,
        reminder_type TEXT NOT NULL,
        is_sent BOOLEAN DEFAULT FALSE,
        created_at TEXT NOT NULL,
        FOREIGN KEY (subscription_id) REFERENCES subscriptions (id),
        FOREIGN KEY (user_id) REFERENCES users (id)
    )
    ''')

    # 创建通知历史表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS notifications (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        data TEXT,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TEXT NOT NULL
    )
    ''')

    # 创建索引以提高查询性能
    # 用户表索引
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active)')

    # 交易表索引
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(date)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_category ON transactions(category)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_account ON transactions(account)')

    # 账户表索引
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_accounts_user_id ON accounts(user_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_accounts_name ON accounts(name)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_accounts_type ON accounts(type)')

    # 预算表索引
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_budgets_user_id ON budgets(user_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_budgets_category ON budgets(category)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_budgets_period ON budgets(period)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_budgets_dates ON budgets(start_date, end_date)')

    # 目标表索引
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_goals_user_id ON goals(user_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_goals_name ON goals(name)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_goals_target_date ON goals(target_date)')

    # 通知表索引
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at)')

    # 订阅表索引
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_subscriptions_service_type ON subscriptions(service_type)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_subscriptions_next_billing_date ON subscriptions(next_billing_date)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_subscriptions_is_active ON subscriptions(is_active)')

    # 订阅提醒表索引
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_subscription_reminders_user_id ON subscription_reminders(user_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_subscription_reminders_subscription_id ON subscription_reminders(subscription_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_subscription_reminders_reminder_date ON subscription_reminders(reminder_date)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_subscription_reminders_is_sent ON subscription_reminders(is_sent)')

    # 创建默认账户（如果不存在）
    # 注意：这里不创建默认账户，因为账户现在需要关联到具体用户
    # 默认账户将在用户注册时或通过迁移脚本创建
    pass

    conn.commit()
    conn.close()
    logger.info("数据库初始化完成")

# 在应用上下文中初始化数据库
with app.app_context():
    init_db()

# 注册蓝图
from blueprints.auth import bp as auth_bp
from blueprints.transactions import bp as transactions_bp
from blueprints.accounts import bp as accounts_bp
from blueprints.budgets import bp as budgets_bp
from blueprints.goals import bp as goals_bp
from blueprints.migrate import bp as migrate_bp
from blueprints.budget_check import bp as budget_check_bp
from blueprints.notifications import bp as notifications_bp
from blueprints.subscriptions import bp as subscriptions_bp

# 注册API蓝图
app.register_blueprint(auth_bp, url_prefix='/api/auth')
app.register_blueprint(transactions_bp, url_prefix='/api/transactions')
app.register_blueprint(accounts_bp, url_prefix='/api/accounts')
app.register_blueprint(budgets_bp, url_prefix='/api/budgets')
app.register_blueprint(goals_bp, url_prefix='/api/goals')
app.register_blueprint(migrate_bp, url_prefix='/api/migrate')
app.register_blueprint(budget_check_bp)
app.register_blueprint(notifications_bp)
app.register_blueprint(subscriptions_bp, url_prefix='/api/subscriptions')

# API测试路由
@app.route('/api/test', methods=['GET'])
def test_api():
    """测试API"""
    logger.info("测试API")
    return jsonify({
        'success': True,
        'message': '服务器正常运行',
        'time': datetime.datetime.now().isoformat(),
        'version': '1.0.0'
    })

# 静态文件路由
@app.route('/')
def index():
    """根据设备类型返回不同页面"""
    user_agent = request.headers.get('User-Agent', '')
    logger.info(f"访问首页 - User-Agent: {user_agent}")

    if is_mobile_device(user_agent):
        logger.info("检测到移动设备，返回移动版页面")
        # 检查移动版构建文件是否存在
        mobile_index = os.path.join('..', 'mobile', 'dist', 'index.html')
        if os.path.exists(mobile_index):
            return send_from_directory('../mobile/dist', 'index.html')
        else:
            logger.warning("移动版构建文件不存在，返回网页版")
            return send_from_directory('templates', 'index.html')
    else:
        logger.info("检测到桌面设备，返回网页版页面")
        return send_from_directory('templates', 'index.html')

@app.route('/login.html')
def login_redirect():
    """重定向到新的登录页面路径"""
    logger.info("重定向到登录页面")
    return send_from_directory('templates/auth', 'login.html')

@app.route('/templates/auth/login.html')
def login():
    """提供登录页面"""
    logger.info("访问登录页面")
    return send_from_directory('templates/auth', 'login.html')

@app.route('/assets/<path:path>')
def serve_assets(path):
    """为移动版和网页版提供静态资源"""
    user_agent = request.headers.get('User-Agent', '')

    if is_mobile_device(user_agent):
        # 先尝试移动版assets
        mobile_assets = os.path.join('..', 'mobile', 'dist', 'assets')
        mobile_file_path = os.path.join(mobile_assets, path)
        if os.path.exists(mobile_file_path):
            logger.info(f"返回移动版静态文件: {path}")
            return send_from_directory(mobile_assets, path)

    # 如果不是移动设备或移动版文件不存在，返回网页版静态文件
    logger.info(f"返回网页版静态文件: {path}")
    return send_from_directory('static', path)

@app.route('/templates/<path:path>')
def serve_templates(path):
    """提供templates目录下的文件"""
    return send_from_directory('templates', path)

@app.route('/<path:path>')
def serve_static(path):
    """提供根目录下的静态文件"""
    return send_from_directory('.', path)

# 启动应用时的配置
if __name__ == '__main__':
    logger.info("=" * 60)
    logger.info("个人财务管理系统后端服务器")
    logger.info("=" * 60)
    logger.info("服务器已启动，访问 http://localhost:8000")
    logger.info("按Ctrl+C可以停止服务器")
    logger.info("=" * 60)

    # 在新线程中打开浏览器（仅在非调试模式或首次启动时）
    import threading
    import webbrowser
    import time
    import os

    # 使用环境变量来防止重复打开浏览器
    if os.environ.get('WERKZEUG_RUN_MAIN') != 'true':
        def open_browser():
            time.sleep(1)
            webbrowser.open('http://localhost:8000')

        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()

    # 启动Flask应用
    app.run(host='0.0.0.0', port=8000, debug=app.config['DEBUG'])
