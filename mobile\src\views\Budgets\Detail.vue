<template>
  <div class="budget-detail-page">
    <van-nav-bar
      :title="budget?.project_name || '预算详情'"
      left-arrow
      @click-left="$router.back()"
      fixed
      placeholder
    >
      <template #right>
        <van-icon name="edit" size="18" @click="editBudget" />
      </template>
    </van-nav-bar>

    <div class="page-container">
      <div v-if="loading" class="loading-container">
        <van-loading size="24px" />
        <span>加载中...</span>
      </div>

      <div v-else-if="budget">
        <!-- 预算信息卡片 -->
        <div class="budget-info-card">
          <div class="budget-header">
            <div class="budget-icon" :class="budget.status">
              {{ getCategoryIcon(budget.category) }}
            </div>
            <div class="budget-details">
              <h3>{{ budget.project_name || budget.category }}</h3>
              <p>{{ budget.category }} • {{ formatBudgetPeriod(budget.start_date, budget.end_date) }} • {{ budget.currency }}</p>
            </div>
            <div class="status-badge" :class="budget.status">
              {{ getStatusText(budget.status) }}
            </div>
          </div>

          <!-- 预算使用情况 -->
          <div class="usage-info">
            <div class="usage-header">
              <span>预算使用情况</span>
              <span class="percentage" :class="budget.status">
                {{ (budget.usage_percentage || 0).toFixed(1) }}%
              </span>
            </div>
            <van-progress
              :percentage="Math.min(budget.usage_percentage || 0, 100)"
              :color="getProgressColor(budget.usage_percentage || 0)"
              stroke-width="12"
            />
            <div class="usage-details">
              <div class="usage-item">
                <span class="label">已使用</span>
                <span class="amount used">{{ formatCurrency(budget.used_amount || 0) }}</span>
              </div>
              <div class="usage-item">
                <span class="label">剩余</span>
                <span class="amount remaining">{{ formatCurrency(budget.remaining_amount || 0) }}</span>
              </div>
              <div class="usage-item">
                <span class="label">总预算</span>
                <span class="amount total">{{ formatCurrency(budget.amount) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 预算描述 -->
        <van-cell-group v-if="budget.description" inset title="预算描述">
          <van-cell :value="budget.description" />
        </van-cell-group>

        <!-- 相关交易 -->
        <van-cell-group inset title="相关交易">
          <div v-if="transactionsLoading" class="loading-container small">
            <van-loading size="16px" />
            <span>加载交易记录...</span>
          </div>
          
          <div v-else-if="relatedTransactions.length === 0" class="empty-transactions">
            <p>暂无相关交易记录</p>
          </div>
          
          <van-cell
            v-else
            v-for="transaction in relatedTransactions"
            :key="transaction.id"
            :title="transaction.description || transaction.category"
            :label="formatDate(transaction.date)"
            :value="formatCurrency(transaction.amount, transaction.currency)"
            is-link
            @click="viewTransaction(transaction)"
          >
            <template #icon>
              <div class="transaction-icon">💸</div>
            </template>
          </van-cell>
          
          <van-cell
            v-if="relatedTransactions.length > 0"
            title="查看全部交易"
            is-link
            @click="viewAllTransactions"
          />
        </van-cell-group>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <van-button type="primary" block round @click="editBudget">
            编辑预算
          </van-button>
          <van-button type="danger" block round @click="showDeleteConfirm = true">
            删除预算
          </van-button>
        </div>
      </div>

      <div v-else class="error-container">
        <div class="error-icon">❌</div>
        <p>预算不存在</p>
        <van-button type="primary" size="small" @click="$router.back()">
          返回
        </van-button>
      </div>
    </div>

    <!-- 删除确认对话框 -->
    <van-dialog
      v-model:show="showDeleteConfirm"
      title="删除预算"
      message="确定要删除这个预算吗？删除后无法恢复。"
      show-cancel-button
      @confirm="deleteBudget"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useBudgetsStore } from '@/stores/budgets'
import { useTransactionsStore } from '@/stores/transactions'
import { formatCurrency } from '@/utils/format'
import { showToast, showSuccessToast } from 'vant'

const router = useRouter()
const route = useRoute()
const budgetsStore = useBudgetsStore()
const transactionsStore = useTransactionsStore()

// 响应式数据
const loading = ref(false)
const transactionsLoading = ref(false)
const showDeleteConfirm = ref(false)
const relatedTransactions = ref([])

// 计算属性
const budgetId = computed(() => route.params.id)
const budget = computed(() => budgetsStore.currentBudget)

// 初始化
onMounted(async () => {
  await loadBudgetDetail()
  await loadRelatedTransactions()
})

// 加载预算详情
const loadBudgetDetail = async () => {
  loading.value = true
  try {
    await budgetsStore.fetchBudget(budgetId.value)
    
    if (!budget.value) {
      showToast({
        message: '预算不存在',
        type: 'fail'
      })
    }
  } catch (error) {
    console.error('加载预算详情失败:', error)
    showToast({
      message: '加载预算详情失败',
      type: 'fail'
    })
  } finally {
    loading.value = false
  }
}

// 加载相关交易 - 精确匹配交易描述和预算项目名称
const loadRelatedTransactions = async () => {
  if (!budget.value) return

  transactionsLoading.value = true
  try {
    const transactions = await transactionsStore.fetchTransactions({
      type: 'expense',
      description: budget.value.project_name, // 精确匹配项目名称
      start_date: budget.value.start_date,
      end_date: budget.value.end_date,
      limit: 10
    })
    relatedTransactions.value = transactions || []
  } catch (error) {
    console.error('加载相关交易失败:', error)
    relatedTransactions.value = []
  } finally {
    transactionsLoading.value = false
  }
}

// 格式化周期
const formatPeriod = (period) => {
  const periodMap = {
    monthly: '每月',
    weekly: '每周',
    custom: '自定义'
  }
  return periodMap[period] || period
}

// 格式化日期
const formatDate = (dateStr) => {
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

// 格式化预算周期
const formatBudgetPeriod = (startDate, endDate) => {
  if (!startDate || !endDate) return ''

  const start = new Date(startDate)
  const end = new Date(endDate)

  // 如果是同一个月
  if (start.getFullYear() === end.getFullYear() && start.getMonth() === end.getMonth()) {
    return `${start.getFullYear()}年${String(start.getMonth() + 1).padStart(2, '0')}月`
  }

  // 如果跨月，显示完整的年月范围
  const startMonth = String(start.getMonth() + 1).padStart(2, '0')
  const endMonth = String(end.getMonth() + 1).padStart(2, '0')

  if (start.getFullYear() === end.getFullYear()) {
    return `${start.getFullYear()}年${startMonth}月-${endMonth}月`
  } else {
    return `${start.getFullYear()}年${startMonth}月-${end.getFullYear()}年${endMonth}月`
  }
}

// 获取剩余天数
const getRemainingDays = () => {
  if (!budget.value) return '0天'
  
  const now = new Date()
  const endDate = new Date(budget.value.end_date)
  const diffTime = endDate - now
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) return '已过期'
  if (diffDays === 0) return '今天结束'
  return `${diffDays}天`
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    normal: '正常',
    warning: '警告',
    danger: '危险',
    exceeded: '超支'
  }
  return statusMap[status] || '正常'
}

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage >= 100) return '#ee0a24'
  if (percentage >= 90) return '#ff976a'
  if (percentage >= 80) return '#ffd21e'
  return '#07c160'
}

// 获取类别图标
const getCategoryIcon = (category) => {
  const icons = {
    '储蓄': '💰',
    '固定': '🏠',
    '流动': '🛒',
    '债务': '💳'
  }
  return icons[category] || '💰'
}

// 编辑预算
const editBudget = () => {
  if (budget.value) {
    router.push(`/budget/edit/${budget.value.id}`)
  }
}

// 删除预算
const deleteBudget = async () => {
  try {
    await budgetsStore.deleteBudget(budgetId.value)
    showSuccessToast('预算删除成功')
    router.back()
  } catch (error) {
    console.error('删除预算失败:', error)
    showToast({
      message: '删除预算失败',
      type: 'fail'
    })
  }
}

// 查看交易详情
const viewTransaction = (transaction) => {
  router.push(`/transaction/detail/${transaction.id}`)
}

// 查看全部交易
const viewAllTransactions = () => {
  router.push({
    path: '/transactions',
    query: { 
      category: budget.value.category,
      type: 'expense'
    }
  })
}
</script>

<style scoped>
.budget-detail-page {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.page-container {
  padding-bottom: 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #969799;
  gap: 12px;
}

.loading-container.small {
  padding: 20px;
  font-size: 14px;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #969799;
  gap: 16px;
}

.error-icon {
  font-size: 48px;
}

/* 预算信息卡片 */
.budget-info-card {
  background: white;
  margin: 16px;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.budget-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.budget-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  background: #f0f9ff;
}

.budget-icon.warning {
  background: #fff7cc;
}

.budget-icon.danger {
  background: #ffece8;
}

.budget-icon.exceeded {
  background: #fef0f0;
}

.budget-details {
  flex: 1;
}

.budget-details h3 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 600;
  color: #323233;
}

.budget-details p {
  margin: 0;
  font-size: 14px;
  color: #969799;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  background: #f0f9ff;
  color: #1989fa;
}

.status-badge.warning {
  background: #fff7cc;
  color: #ff976a;
}

.status-badge.danger {
  background: #ffece8;
  color: #ee0a24;
}

.status-badge.exceeded {
  background: #fef0f0;
  color: #ee0a24;
}

/* 使用情况 */
.usage-info {
  margin-top: 24px;
}

.usage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.usage-header span {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

.percentage {
  font-size: 18px;
  color: #07c160;
}

.percentage.warning {
  color: #ff976a;
}

.percentage.danger,
.percentage.exceeded {
  color: #ee0a24;
}

.usage-details {
  margin-top: 16px;
  display: flex;
  justify-content: space-between;
}

.usage-item {
  text-align: center;
  flex: 1;
}

.usage-item .label {
  display: block;
  font-size: 12px;
  color: #969799;
  margin-bottom: 4px;
}

.usage-item .amount {
  font-size: 16px;
  font-weight: 600;
}

.amount.used {
  color: #ff976a;
}

.amount.remaining {
  color: #07c160;
}

.amount.total {
  color: #1989fa;
}

/* 空状态 */
.empty-transactions {
  text-align: center;
  padding: 40px 20px;
  color: #969799;
}

/* 交易图标 */
.transaction-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 16px;
  background: #f0f9ff;
}

/* 操作按钮 */
.action-buttons {
  margin: 24px 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

:deep(.van-cell-group__title) {
  padding: 16px 16px 8px;
  font-size: 14px;
  font-weight: 600;
  color: #323233;
}
</style>
