import api from './index'

export const transactionsAPI = {
  // 获取交易列表
  getTransactions(params = {}) {
    return api.get('/transactions/', { params })
  },

  // 获取单个交易
  getTransaction(id) {
    return api.get(`/transactions/${id}`)
  },

  // 创建交易
  createTransaction(data) {
    return api.post('/transactions/', data)
  },

  // 更新交易
  updateTransaction(id, data) {
    return api.put(`/transactions/${id}`, data)
  },

  // 删除交易
  deleteTransaction(id) {
    return api.delete(`/transactions/${id}`)
  },

  // 获取交易统计
  getStats(params = {}) {
    return api.get('/transactions/stats', { params })
  },

  // 导出交易数据
  exportTransactions(params = {}) {
    return api.get('/transactions/export', { params })
  }
}
