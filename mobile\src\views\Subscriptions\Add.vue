<template>
  <div class="add-subscription-page">
    <van-nav-bar
      title="添加订阅"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="$router.back()"
    />

    <div class="page-container">
      <!-- 页面标题和描述 -->
      <div class="page-header">
        <h1 class="page-title">创建新订阅</h1>
        <p class="page-description">添加您的订阅服务，让我们帮您管理和提醒</p>
      </div>

      <van-form @submit="handleSubmit">
        <!-- 服务选择 -->
        <van-cell-group inset title="🎯 选择服务">
          <van-field
            :model-value="selectedServiceText"
            name="service_type"
            label="服务套餐"
            placeholder="请选择服务和套餐"
            readonly
            is-link
            @click="showServicePicker = true"
            :rules="[{ required: true, message: '请选择服务套餐' }]"
          >
            <template #left-icon>
              <ServiceLogo
                v-if="form.service_type"
                :service-name="getServiceTypeLabel(form.service_type)"
                :service-type="form.service_type"
                size="small"
              />
              <div v-else class="service-icon">
                📱
              </div>
            </template>
            <template #right-icon>
              <van-icon name="arrow" class="arrow-icon" />
            </template>
          </van-field>
        </van-cell-group>

        <!-- 基本信息 -->
        <van-cell-group inset title="📝 基本信息">
          <van-field
            v-model="form.amount"
            name="amount"
            label="费用"
            placeholder="0.00"
            type="number"
            :rules="[
              { required: true, message: '请输入费用' },
              { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式' }
            ]"
          >
            <template #left-icon>
              <span class="currency-symbol">{{ getCurrencySymbol(form.currency) }}</span>
            </template>
          </van-field>

          <van-field
            v-model="form.currency"
            name="currency"
            label="货币"
            placeholder="请选择货币"
            readonly
            is-link
            @click="showCurrencyPicker = true"
            :rules="[{ required: true, message: '请选择货币' }]"
          >
            <template #left-icon>
              <div class="field-icon">
                <van-icon name="gold-coin-o" />
              </div>
            </template>
            <template #right-icon>
              <van-icon name="arrow" class="arrow-icon" />
            </template>
          </van-field>

          <van-field
            v-model="form.billing_cycle"
            name="billing_cycle"
            label="计费周期"
            placeholder="请选择计费周期"
            readonly
            is-link
            @click="showCyclePicker = true"
            :rules="[{ required: true, message: '请选择计费周期' }]"
          >
            <template #left-icon>
              <div class="field-icon">
                <van-icon name="clock-o" />
              </div>
            </template>
            <template #right-icon>
              <van-icon name="arrow" class="arrow-icon" />
            </template>
          </van-field>

          <van-field
            v-model="form.start_date"
            name="start_date"
            label="开始日期"
            placeholder="请选择开始日期"
            readonly
            is-link
            @click="showDatePicker = true"
            :rules="[{ required: true, message: '请选择开始日期' }]"
          >
            <template #left-icon>
              <div class="field-icon">
                <van-icon name="calendar-o" />
              </div>
            </template>
            <template #right-icon>
              <van-icon name="arrow" class="arrow-icon" />
            </template>
          </van-field>
        </van-cell-group>

        <!-- 可选信息 -->
        <van-cell-group inset title="💭 可选信息">
          <van-field
            v-model="form.description"
            name="description"
            label="备注"
            placeholder="添加一些备注信息（可选）"
            type="textarea"
            rows="3"
            autosize
          >
            <template #left-icon>
              <div class="field-icon">
                <van-icon name="notes-o" />
              </div>
            </template>
          </van-field>
        </van-cell-group>

        <!-- 预览卡片 -->
        <div v-if="form.service_type && form.amount && form.billing_cycle" class="preview-section">
          <h3 class="preview-title">📋 预览</h3>
          <div class="preview-card">
            <div class="preview-header">
              <div class="preview-service">
                <ServiceLogo
                  :service-name="form.name || getServiceTypeLabel(form.service_type)"
                  :service-type="form.service_type"
                  size="small"
                />
                <div class="service-details">
                  <h4 class="service-name">{{ form.name || getServiceTypeLabel(form.service_type) }}</h4>
                  <p class="service-type">{{ getServiceTypeLabel(form.service_type) }}</p>
                </div>
              </div>
              <div class="preview-price">
                <span class="amount">{{ getCurrencySymbol(form.currency) }}{{ form.amount }}</span>
                <span class="cycle">{{ getCycleLabel(form.billing_cycle) }}</span>
              </div>
            </div>
            <div class="preview-footer">
              <span class="start-date">开始日期: {{ formatDate(form.start_date) }}</span>
            </div>
          </div>
        </div>

        <!-- 提交按钮 -->
        <div class="submit-section">
          <van-button
            type="primary"
            block
            native-type="submit"
            :loading="loading"
            :disabled="!isFormValid"
          >
            <van-icon name="plus" />
            添加订阅
          </van-button>
        </div>
      </van-form>
    </div>

    <!-- 服务类型级联选择器 -->
    <van-popup v-model:show="showServicePicker" position="bottom" round>
      <van-cascader
        v-model="selectedCascaderValue"
        title="选择服务套餐"
        :options="cascaderOptions"
        @close="showServicePicker = false"
        @finish="onServiceConfirm"
      />
    </van-popup>

    <!-- 计费周期选择器 -->
    <van-popup v-model:show="showCyclePicker" position="bottom" round>
      <van-picker
        :columns="cycleOptions"
        @confirm="onCycleConfirm"
        @cancel="showCyclePicker = false"
      />
    </van-popup>

    <!-- 货币选择器 -->
    <van-popup v-model:show="showCurrencyPicker" position="bottom" round>
      <van-picker
        :columns="currencyOptions"
        @confirm="onCurrencyConfirm"
        @cancel="showCurrencyPicker = false"
      />
    </van-popup>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom" round>
      <van-date-picker
        v-model="currentDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
        :min-date="minDate"
        :max-date="maxDate"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useSubscriptionsStore } from '@/stores/subscriptions'
import { showToast } from 'vant'
import { cascaderOptions, getServiceConfig } from '@/config/serviceIcons'
import { getCurrencyOptionsForSubscriptions, getCurrencySymbol } from '@/config/currencies'
import ServiceLogo from '@/components/ServiceLogo.vue'

const router = useRouter()
const subscriptionsStore = useSubscriptionsStore()

const loading = ref(false)
const showServicePicker = ref(false)
const showCyclePicker = ref(false)
const showCurrencyPicker = ref(false)
const showDatePicker = ref(false)

// 表单数据
const form = reactive({
  name: '',
  service_type: '',
  amount: '',
  currency: 'MYR',
  billing_cycle: '',
  start_date: '',
  description: ''
})

// 表单验证
const isFormValid = computed(() => {
  return form.service_type &&
         form.amount &&
         form.currency &&
         form.billing_cycle &&
         form.start_date &&
         /^\d+(\.\d{1,2})?$/.test(form.amount)
})

// 当前选择的日期 - Vant 4.x 使用数组格式 [year, month, day]
const currentDate = ref([])
const minDate = new Date(2020, 0, 1)
const maxDate = new Date(2030, 11, 31)



// 当前选择的级联值
const selectedCascaderValue = ref([])

// 显示的服务文本
const selectedServiceText = computed(() => {
  if (selectedCascaderValue.value.length === 0) return ''

  const [serviceValue, planValue] = selectedCascaderValue.value
  const service = cascaderOptions.find(opt => opt.value === serviceValue)
  if (!service) return ''

  const plan = service.children?.find(child => child.value === planValue)
  return plan ? `${service.text} ${plan.text}` : service.text
})

// 计费周期选项
const cycleOptions = [
  { text: '月付', value: 'monthly' },
  { text: '季付', value: 'quarterly' },
  { text: '年付', value: 'yearly' },
  { text: '周付', value: 'weekly' }
]

// 货币选项 - 使用统一配置
const currencyOptions = getCurrencyOptionsForSubscriptions()

// 获取服务类型标签
const getServiceTypeLabel = (serviceType) => {
  if (!serviceType) return '未选择'

  const config = getServiceConfig(serviceType)
  return config.name
}

// 获取周期标签
const getCycleLabel = (cycle) => {
  const cycleMap = {
    'weekly': '/周',
    'monthly': '/月',
    'quarterly': '/季',
    'yearly': '/年'
  }
  return cycleMap[cycle] || '/月'
}

// 获取货币符号 - 使用统一配置
// getCurrencySymbol 已从配置文件导入

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未选择'
  try {
    const date = new Date(dateString)
    return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
  } catch {
    return '未知'
  }
}

// 级联选择器确认
const onServiceConfirm = ({ selectedOptions }) => {
  // selectedOptions 是一个数组，包含每一级的选择
  const values = selectedOptions.map(option => option.value)
  selectedCascaderValue.value = values

  // 设置 service_type 为最后一级的值（具体套餐）
  form.service_type = values[values.length - 1]

  // 自动生成订阅名称
  if (selectedOptions.length > 0) {
    const serviceName = selectedOptions[0].text.split(' ').slice(1).join(' ')
    const planName = selectedOptions.length > 1 ? selectedOptions[1].text : ''
    form.name = planName ? `${serviceName} ${planName}` : serviceName
  }

  showServicePicker.value = false
}

// 计费周期确认
const onCycleConfirm = ({ selectedOptions }) => {
  form.billing_cycle = selectedOptions[0].value
  showCyclePicker.value = false
}

// 货币选择确认
const onCurrencyConfirm = ({ selectedOptions }) => {
  form.currency = selectedOptions[0].value
  showCurrencyPicker.value = false
}

// 日期确认
const onDateConfirm = () => {
  // 将数组格式 [year, month, day] 转换为日期字符串
  const [year, month, day] = currentDate.value
  const dateObj = new Date(year, month - 1, day) // month 需要减1，因为 Date 的月份从0开始
  form.start_date = dateObj.toISOString().split('T')[0]
  showDatePicker.value = false
}

// 提交表单
const handleSubmit = async () => {
  loading.value = true
  
  try {
    // 构建提交数据
    const submitData = {
      ...form,
      amount: parseFloat(form.amount),
      start_date: new Date(form.start_date).toISOString()
    }
    
    await subscriptionsStore.createSubscription(submitData)
    
    showToast({
      message: '订阅添加成功',
      type: 'success'
    })
    
    router.back()
    
  } catch (error) {
    showToast({
      message: error.message || '添加失败',
      type: 'fail'
    })
  } finally {
    loading.value = false
  }
}

// 初始化默认值
const today = new Date()
form.start_date = today.toISOString().split('T')[0]
// 初始化日期选择器的值为今天
currentDate.value = [today.getFullYear(), today.getMonth() + 1, today.getDate()]
</script>

<style scoped>
.add-subscription-page {
  background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
  min-height: 100vh;
}

.page-container {
  padding: 16px;
}

/* 页面头部样式 */
.page-header {
  text-align: center;
  padding: 24px 0 32px;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  margin: -16px -16px 24px -16px;
  border-radius: 0 0 24px 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

/* 字段图标样式 */
.field-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
  border-radius: 6px;
  color: #3b82f6;
  font-size: 12px;
  margin-right: 8px;
}

.arrow-icon {
  color: #9ca3af;
  font-size: 14px;
}

/* 预览部分样式 */
.preview-section {
  margin: 24px 0;
}

.preview-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
  padding: 0 4px;
}

.preview-card {
  background: #ffffff;
  border-radius: 16px;
  padding: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.preview-service {
  display: flex;
  align-items: center;
  flex: 1;
}

.service-details {
  margin-left: 8px;
}

.service-name {
  margin: 0 0 2px 0;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.service-type {
  margin: 0;
  font-size: 11px;
  color: #6b7280;
}

.preview-price {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.amount {
  font-size: 16px;
  font-weight: 700;
  color: #3b82f6;
}

.cycle {
  font-size: 10px;
  color: #6b7280;
}

.preview-footer {
  padding-top: 12px;
  border-top: 1px solid #f1f5f9;
}

.start-date {
  font-size: 12px;
  color: #6b7280;
}

.service-icon {
  font-size: 20px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 8px;
  color: #ffffff;
}

.currency-symbol {
  color: #3b82f6;
  font-weight: 600;
  margin-right: 8px;
  font-size: 16px;
}

.submit-section {
  padding: 24px 16px;
  background: #ffffff;
  border-top: 1px solid #e2e8f0;
  margin: 0 -16px -16px -16px;
}

.submit-section .van-button {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  color: #ffffff;
  font-weight: 600;
  height: 48px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.submit-section .van-button:active {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: scale(0.98);
}

.submit-section .van-button:disabled {
  background: #e2e8f0;
  color: #9ca3af;
  box-shadow: none;
}

/* 自定义导航栏样式 */
:deep(.van-nav-bar) {
  background: #ffffff;
  border-bottom: 1px solid #e2e8f0;
}

:deep(.van-nav-bar__title) {
  color: #1f2937;
  font-weight: 600;
}

:deep(.van-nav-bar .van-icon) {
  color: #3b82f6;
}

/* 自定义表单样式 */
:deep(.van-cell-group) {
  background: #ffffff;
  border-radius: 16px;
  margin-bottom: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

:deep(.van-cell-group__title) {
  color: #1f2937;
  font-weight: 600;
  padding: 16px 16px 8px;
  font-size: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  margin: 0;
  border-bottom: 1px solid #e2e8f0;
}

:deep(.van-cell) {
  background: #ffffff;
  color: #1f2937;
  border-bottom: 1px solid #f1f5f9;
  padding: 16px;
  transition: background-color 0.3s ease;
}

:deep(.van-cell:last-child) {
  border-bottom: none;
}

:deep(.van-cell:active) {
  background: #f8fafc;
}

:deep(.van-field__label) {
  color: #374151;
  font-weight: 500;
  font-size: 14px;
}

:deep(.van-field__control) {
  color: #1f2937;
  font-size: 14px;
}

:deep(.van-field__control::placeholder) {
  color: #9ca3af;
}

:deep(.van-field__right-icon) {
  color: #6b7280;
}

/* 表单验证样式 */
:deep(.van-field--error .van-field__control) {
  color: #ef4444;
}

:deep(.van-field--error .van-field__label) {
  color: #ef4444;
}

/* 选择器样式优化 */
:deep(.van-picker) {
  background: #ffffff;
}

:deep(.van-picker__toolbar) {
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

:deep(.van-picker__confirm) {
  color: #3b82f6;
  font-weight: 600;
}

:deep(.van-picker__cancel) {
  color: #6b7280;
}

/* 弹出层样式 */
:deep(.van-popup) {
  border-radius: 16px 16px 0 0;
}

/* 加载状态优化 */
:deep(.van-loading__spinner) {
  color: #3b82f6;
}

/* 级联选择器样式 */
:deep(.van-cascader) {
  background: #ffffff;
}

:deep(.van-cascader__header) {
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

:deep(.van-cascader__title) {
  color: #1f2937;
  font-weight: 600;
}

:deep(.van-cascader__close-icon) {
  color: #6b7280;
}

:deep(.van-cascader__tabs) {
  background: #ffffff;
}

:deep(.van-cascader__tab) {
  color: #6b7280;
}

:deep(.van-cascader__tab--selected) {
  color: #3b82f6;
  font-weight: 600;
}

:deep(.van-cascader__option) {
  color: #1f2937;
  padding: 12px 16px;
}

:deep(.van-cascader__option--selected) {
  color: #3b82f6;
  background: #f0f9ff;
  font-weight: 500;
}
</style>
