import{u as V,r as x,a as h,c as k,b as p,d as e,e as a,w as n,f as d,g as q,h as N,o as S,i as r,s as m}from"./index--MNqwREY.js";import{_ as z}from"./_plugin-vue_export-helper-DlAUqK2U.js";const B={class:"login-page"},C={class:"main-container"},L={class:"login-panel"},M={class:"panel-container"},U={class:"form-group"},R={class:"form-label"},T={class:"form-group"},$={class:"form-label"},A={class:"form-controls"},E={class:"control-left"},I={class:"control-right"},j={class:"submit-text"},D={class:"quick-access"},F={class:"access-grid"},G={class:"access-item"},H={class:"access-icon wechat-pro"},J={class:"access-item"},K={class:"access-icon sms-pro"},O={class:"access-item"},P={class:"access-icon scan-pro"},Q={class:"register-area"},W={class:"register-content"},X={class:"footer-section"},Y={class:"footer-content"},Z={class:"security-badge"},ss={__name:"Login",setup(es){const _=q(),f=N(),b=V(),o=x({username:"",password:"",rememberMe:!1}),c=h(!1),g=async()=>{if(!o.username||!o.password){m("请填写完整的登录信息");return}c.value=!0;try{await b.login({username:o.username,password:o.password}),m({message:"登录成功",type:"success"});const l=f.query.redirect||"/";_.replace(l)}catch(l){console.error("登录失败:",l),m({message:l.message||"登录失败，请检查用户名和密码",type:"fail"})}finally{c.value=!1}};return(l,s)=>{const t=d("van-icon"),u=d("van-field"),w=d("van-checkbox"),v=d("van-button"),y=d("van-form");return S(),k("div",B,[s[19]||(s[19]=p('<div class="professional-bg" data-v-c97e2173><div class="grid-pattern" data-v-c97e2173></div><div class="accent-line line-1" data-v-c97e2173></div><div class="accent-line line-2" data-v-c97e2173></div><div class="accent-line line-3" data-v-c97e2173></div></div>',1)),e("div",C,[s[18]||(s[18]=p('<div class="brand-section" data-v-c97e2173><div class="brand-container" data-v-c97e2173><div class="logo-professional" data-v-c97e2173><div class="logo-frame" data-v-c97e2173><div class="logo-inner" data-v-c97e2173>💰</div></div></div><div class="brand-info" data-v-c97e2173><h1 class="brand-title" data-v-c97e2173>钱管家</h1><p class="brand-subtitle" data-v-c97e2173>专业财务管理平台</p><div class="brand-line" data-v-c97e2173></div></div></div></div>',1)),e("div",L,[e("div",M,[s[14]||(s[14]=e("div",{class:"panel-header"},[e("h2",{class:"panel-title"},"账户登录"),e("p",{class:"panel-subtitle"},"请输入您的凭据以访问系统")],-1)),a(y,{onSubmit:g,class:"professional-form"},{default:n(()=>[e("div",U,[e("label",R,[a(t,{name:"user-o",class:"label-icon"}),s[4]||(s[4]=r(" 用户名 / 邮箱 "))]),a(u,{modelValue:o.username,"onUpdate:modelValue":s[0]||(s[0]=i=>o.username=i),name:"username",placeholder:"请输入用户名或邮箱地址",rules:[{required:!0,message:"请输入用户名或邮箱"}],clearable:"",class:"professional-field"},null,8,["modelValue"])]),e("div",T,[e("label",$,[a(t,{name:"lock",class:"label-icon"}),s[5]||(s[5]=r(" 密码 "))]),a(u,{modelValue:o.password,"onUpdate:modelValue":s[1]||(s[1]=i=>o.password=i),type:"password",name:"password",placeholder:"请输入您的密码",rules:[{required:!0,message:"请输入密码"}],clearable:"",class:"professional-field"},null,8,["modelValue"])]),e("div",A,[e("div",E,[a(w,{modelValue:o.rememberMe,"onUpdate:modelValue":s[2]||(s[2]=i=>o.rememberMe=i),class:"professional-checkbox"},{default:n(()=>s[6]||(s[6]=[e("span",{class:"checkbox-text"},"保持登录状态",-1)])),_:1,__:[6]},8,["modelValue"])]),e("div",I,[a(v,{type:"default",size:"mini",plain:"",class:"forgot-password"},{default:n(()=>s[7]||(s[7]=[r(" 忘记密码？ ")])),_:1,__:[7]})])]),a(v,{block:"",type:"primary","native-type":"submit",loading:c.value,"loading-text":"验证中...",size:"large",class:"login-submit"},{default:n(()=>[e("span",j,[a(t,{name:"arrow",class:"submit-icon"}),s[8]||(s[8]=r(" 登录系统 "))])]),_:1},8,["loading"])]),_:1}),s[15]||(s[15]=p('<div class="section-divider" data-v-c97e2173><div class="divider-content" data-v-c97e2173><div class="divider-line-left" data-v-c97e2173></div><span class="divider-text" data-v-c97e2173>或使用以下方式</span><div class="divider-line-right" data-v-c97e2173></div></div></div>',1)),e("div",D,[e("div",F,[e("div",G,[e("div",H,[a(t,{name:"wechat"})]),s[9]||(s[9]=e("span",{class:"access-label"},"微信登录",-1))]),e("div",J,[e("div",K,[a(t,{name:"phone-o"})]),s[10]||(s[10]=e("span",{class:"access-label"},"短信验证",-1))]),e("div",O,[e("div",P,[a(t,{name:"scan"})]),s[11]||(s[11]=e("span",{class:"access-label"},"扫码登录",-1))])])]),e("div",Q,[e("div",W,[s[13]||(s[13]=e("span",{class:"register-prompt"},"没有账户？",-1)),a(v,{type:"primary",size:"small",plain:"",onClick:s[3]||(s[3]=i=>l.$router.push("/register")),class:"register-action"},{default:n(()=>s[12]||(s[12]=[r(" 申请账户 ")])),_:1,__:[12]})])])])]),e("div",X,[e("div",Y,[e("div",Z,[a(t,{name:"shield-o"}),s[16]||(s[16]=e("span",null,"安全登录",-1))]),s[17]||(s[17]=e("div",{class:"version-display"},[e("span",null,"版本 1.0.0")],-1))])])])])}}},ts=z(ss,[["__scopeId","data-v-c97e2173"]]);export{ts as default};
