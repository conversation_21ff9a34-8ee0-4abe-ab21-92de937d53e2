import{z as E,a as D,r as j,l as _,A as R,m as H,p as P,w as l,d as s,e,f as u,i as S,B as U,s as p,o as M,C as O,u as F,c as G,t as h,g as J,v as K}from"./index--MNqwREY.js";import{u as Q}from"./subscriptions-4RI1wB9c.js";import{useAccountsStore as X}from"./accounts-bk1VVrk3.js";import{u as Y}from"./transactions-F5TqZ8Qm.js";import{_ as W}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{s as N}from"./function-call-w7FI8rRn.js";import"./transactions-CrdnwW_k.js";const Z={class:"device-info"},tt={class:"info-content"},et={class:"actions"},st={__name:"DeviceInfo",props:{show:{type:<PERSON><PERSON>an,default:!1},showModifiers:{}},emits:["update:show"],setup(z){const w=E(z,"show"),g=D(!1),n=j({}),b=D(localStorage.getItem("disableAutoRedirect")==="true"),k=_(()=>R()),m=()=>{Object.assign(n,U())},y=async()=>{g.value=!0,await new Promise(c=>setTimeout(c,500)),m(),g.value=!1,p("信息已刷新")},f=async()=>{try{await navigator.clipboard.writeText(n.userAgent),p("User Agent 已复制到剪贴板")}catch{p("复制失败")}},C=()=>{const c=O(),i=R();p({message:`
      检测结果: ${i?"应该使用手机版":"应该使用网页版"}
      ${c?`重定向到: ${c}`:"无需重定向"}
      屏幕宽度: ${window.innerWidth}px
    `,duration:4e3})},I=()=>{const i=`http://${U().currentHost}:8000${window.location.pathname}`;p({message:"正在跳转到网页版...",type:"loading",duration:1500}),setTimeout(()=>{window.location.href=i},1500)},T=c=>{localStorage.setItem("disableAutoRedirect",c.toString()),p({message:c?"已禁用自动重定向":"已启用自动重定向",type:"success"})};return H(()=>{m()}),window.addEventListener("resize",m),(c,i)=>{const x=u("van-icon"),A=u("van-nav-bar"),o=u("van-cell"),t=u("van-cell-group"),$=u("van-switch"),r=u("van-button"),a=u("van-popup");return M(),P(a,{show:w.value,"onUpdate:show":i[2]||(i[2]=v=>w.value=v),position:"bottom",style:{height:"60%"}},{default:l(()=>[s("div",Z,[e(A,{title:"设备信息",onClickLeft:i[0]||(i[0]=v=>w.value=!1)},{left:l(()=>[e(x,{name:"cross"})]),_:1}),s("div",tt,[e(t,{inset:"",title:"设备检测"},{default:l(()=>[e(o,{title:"设备类型",value:n.deviceType},null,8,["value"]),e(o,{title:"屏幕尺寸",value:n.screenSize},null,8,["value"]),e(o,{title:"是否移动设备",value:n.isMobile?"是":"否"},null,8,["value"]),e(o,{title:"是否平板设备",value:n.isTablet?"是":"否"},null,8,["value"]),e(o,{title:"是否桌面设备",value:n.isDesktop?"是":"否"},null,8,["value"]),e(o,{title:"是否触摸设备",value:n.isTouchDevice?"是":"否"},null,8,["value"]),e(o,{title:"应该使用手机版",value:k.value?"是":"否","value-class":k.value?"text-success":"text-danger"},null,8,["value","value-class"])]),_:1}),e(t,{inset:"",title:"屏幕信息"},{default:l(()=>[e(o,{title:"屏幕宽度",value:n.screenWidth+"px"},null,8,["value"]),e(o,{title:"屏幕高度",value:n.screenHeight+"px"},null,8,["value"]),e(o,{title:"当前端口",value:n.currentPort||"默认"},null,8,["value"]),e(o,{title:"当前主机",value:n.currentHost},null,8,["value"])]),_:1}),e(t,{inset:"",title:"用户代理"},{default:l(()=>[e(o,{title:"User Agent",value:n.userAgent,label:n.userAgent,"is-link":"",onClick:f},null,8,["value","label"])]),_:1}),e(t,{inset:"",title:"调试设置"},{default:l(()=>[e(o,{title:"禁用自动重定向"},{"right-icon":l(()=>[e($,{modelValue:b.value,"onUpdate:modelValue":i[1]||(i[1]=v=>b.value=v),onChange:T,size:"20px"},null,8,["modelValue"])]),_:1})]),_:1}),s("div",et,[e(r,{type:"primary",block:"",onClick:y,loading:g.value},{default:l(()=>i[3]||(i[3]=[S(" 刷新信息 ")])),_:1,__:[3]},8,["loading"]),e(r,{type:"default",block:"",plain:"",onClick:C,style:{"margin-top":"12px"}},{default:l(()=>i[4]||(i[4]=[S(" 测试重定向逻辑 ")])),_:1,__:[4]}),e(r,{type:"warning",block:"",plain:"",onClick:I,style:{"margin-top":"12px"}},{default:l(()=>i[5]||(i[5]=[S(" 强制跳转到网页版 ")])),_:1,__:[5]})])])])]),_:1},8,["show"])}}},ot=W(st,[["__scopeId","data-v-a23af40a"]]),nt={class:"profile-page"},it={class:"page-container"},lt={class:"compact-user-card"},at={class:"user-row"},rt={class:"user-avatar-compact"},ut={class:"user-details"},ct={class:"user-actions-compact"},dt={class:"stats-row"},vt={class:"stat-value"},pt={class:"stat-value"},mt={class:"stat-compact"},gt={class:"stat-value"},ft={class:"quick-grid"},_t={class:"logout-section"},wt={__name:"index",setup(z){const w=J(),g=F(),n=Q(),b=X(),k=Y(),m=D(!1),y=D(!1),f=_(()=>g.user),C=_(()=>n.dueSoonCount),I=_(()=>b.accounts.length),T=_(()=>k.transactions.length),c=_(()=>{var r;if(!((r=f.value)!=null&&r.created_at))return 0;const o=new Date(f.value.created_at),$=Math.abs(new Date-o);return Math.ceil($/(1e3*60*60*24))}),i=()=>{console.log("编辑资料")},x=async()=>{try{await N({title:"确认退出",message:"确定要退出登录吗？"}),m.value=!0,await g.logout(),p({message:"已退出登录",type:"success"}),w.replace("/login")}catch(o){o!=="cancel"&&(console.error("退出登录失败:",o),p({message:"退出失败",type:"fail"}))}finally{m.value=!1}},A=async()=>{try{await N({title:"切换到网页版",message:"网页版适合在电脑上使用，确定要切换吗？"});const t=`http://${U().currentHost}:8000${window.location.pathname}`;p({message:"正在跳转到网页版...",type:"loading",duration:1500}),setTimeout(()=>{window.location.href=t},1500)}catch(o){o!=="cancel"&&console.error("切换失败:",o)}};return H(async()=>{try{await Promise.all([n.fetchSubscriptions(),n.fetchDueSoon(),b.fetchAccounts(),k.fetchTransactions()])}catch(o){console.error("获取数据失败:",o)}}),(o,t)=>{var V,B;const $=u("van-nav-bar"),r=u("van-icon"),a=u("van-cell"),v=u("van-cell-group"),L=u("van-badge"),q=u("van-button");return M(),G("div",nt,[e($,{title:"个人中心",fixed:"",placeholder:"","safe-area-inset-top":""}),s("div",it,[s("div",lt,[s("div",at,[s("div",rt,[e(r,{name:"user-o",size:"28"}),t[10]||(t[10]=s("div",{class:"status-dot"},null,-1))]),s("div",ut,[s("h3",null,h(((V=f.value)==null?void 0:V.display_name)||"admin"),1),s("p",null,h(((B=f.value)==null?void 0:B.email)||"<EMAIL>"),1),t[11]||(t[11]=s("div",{class:"user-badges"},[s("span",{class:"badge verified"},"已认证"),s("span",{class:"badge member"},"会员")],-1))]),s("div",ct,[e(r,{name:"edit",size:"20",onClick:i})])]),s("div",dt,[s("div",{class:"stat-compact",onClick:t[0]||(t[0]=d=>o.$router.push("/accounts"))},[s("div",vt,h(I.value),1),t[12]||(t[12]=s("div",{class:"stat-name"},"账户",-1))]),t[15]||(t[15]=s("div",{class:"stat-divider"},null,-1)),s("div",{class:"stat-compact",onClick:t[1]||(t[1]=d=>o.$router.push("/transactions"))},[s("div",pt,h(T.value),1),t[13]||(t[13]=s("div",{class:"stat-name"},"交易",-1))]),t[16]||(t[16]=s("div",{class:"stat-divider"},null,-1)),s("div",mt,[s("div",gt,h(c.value),1),t[14]||(t[14]=s("div",{class:"stat-name"},"天数",-1))])])]),s("div",ft,[s("div",{class:"grid-item",onClick:t[2]||(t[2]=d=>o.$router.push("/accounts"))},[e(r,{name:"gold-coin-o",size:"24"}),t[17]||(t[17]=s("span",null,"我的账户",-1))]),s("div",{class:"grid-item",onClick:t[3]||(t[3]=d=>o.$router.push("/transactions"))},[e(r,{name:"bill-o",size:"24"}),t[18]||(t[18]=s("span",null,"交易记录",-1))]),s("div",{class:"grid-item",onClick:t[4]||(t[4]=d=>o.$router.push("/subscriptions"))},[e(r,{name:"credit-pay",size:"24"}),t[19]||(t[19]=s("span",null,"订阅管理",-1))]),s("div",{class:"grid-item",onClick:t[5]||(t[5]=d=>o.$router.push("/settings"))},[e(r,{name:"setting-o",size:"24"}),t[20]||(t[20]=s("span",null,"设置",-1))])]),e(v,{inset:"",title:"功能设置"},{default:l(()=>[e(a,{title:"数据统计","is-link":"",icon:"chart-trending-o"}),e(a,{title:"导入导出","is-link":"",icon:"exchange"}),e(a,{title:"备份恢复","is-link":"",icon:"replay"})]),_:1}),e(v,{inset:"",title:"订阅管理"},{default:l(()=>[e(a,{title:"我的订阅","is-link":"",icon:"credit-pay",onClick:t[6]||(t[6]=d=>o.$router.push("/subscriptions"))},{"right-icon":l(()=>[C.value>0?(M(),P(L,{key:0,content:C.value,max:99},null,8,["content"])):K("",!0)]),_:1}),e(a,{title:"添加订阅","is-link":"",icon:"plus",onClick:t[7]||(t[7]=d=>o.$router.push("/subscription/add"))})]),_:1}),e(v,{inset:"",title:"应用设置"},{default:l(()=>[e(a,{title:"主题设置","is-link":"",icon:"setting-o"}),e(a,{title:"语言设置","is-link":"",icon:"globe-o"}),e(a,{title:"通知设置","is-link":"",icon:"bell"}),e(a,{title:"切换到网页版","is-link":"",icon:"desktop-o",onClick:A})]),_:1}),e(v,{inset:"",title:"帮助与反馈"},{default:l(()=>[e(a,{title:"使用帮助","is-link":"",icon:"question-o"}),e(a,{title:"意见反馈","is-link":"",icon:"comment-o"}),e(a,{title:"关于我们","is-link":"",icon:"info-o"}),e(a,{title:"设备信息","is-link":"",icon:"phone-o",onClick:t[8]||(t[8]=d=>y.value=!0)})]),_:1}),s("div",_t,[e(q,{block:"",type:"danger",plain:"",onClick:x,loading:m.value},{default:l(()=>t[21]||(t[21]=[S(" 退出登录 ")])),_:1,__:[21]},8,["loading"])])]),e(ot,{show:y.value,"onUpdate:show":t[9]||(t[9]=d=>y.value=d)},null,8,["show"])])}}},Dt=W(wt,[["__scopeId","data-v-7e977fdc"]]);export{Dt as default};
