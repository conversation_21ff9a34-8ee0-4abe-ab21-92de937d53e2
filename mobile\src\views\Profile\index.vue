<template>
  <div class="profile-page">
    <van-nav-bar 
      title="个人中心" 
      fixed
      placeholder
      safe-area-inset-top
    />

    <div class="page-container">
      <!-- 紧凑型用户卡片 -->
      <div class="compact-user-card">
        <!-- 用户信息横向布局 -->
        <div class="user-row">
          <div class="user-avatar-compact">
            <van-icon name="user-o" size="28" />
            <div class="status-dot"></div>
          </div>
          <div class="user-details">
            <h3>{{ user?.display_name || 'admin' }}</h3>
            <p>{{ user?.email || '<EMAIL>' }}</p>
            <div class="user-badges">
              <span class="badge verified">已认证</span>
              <span class="badge member">会员</span>
            </div>
          </div>
          <div class="user-actions-compact">
            <van-icon name="edit" size="20" @click="editProfile" />
          </div>
        </div>

        <!-- 统计数据横向布局 -->
        <div class="stats-row">
          <div class="stat-compact" @click="$router.push('/accounts')">
            <div class="stat-value">{{ accountsCount }}</div>
            <div class="stat-name">账户</div>
          </div>
          <div class="stat-divider"></div>
          <div class="stat-compact" @click="$router.push('/transactions')">
            <div class="stat-value">{{ transactionsCount }}</div>
            <div class="stat-name">交易</div>
          </div>
          <div class="stat-divider"></div>
          <div class="stat-compact">
            <div class="stat-value">{{ usageDays }}</div>
            <div class="stat-name">天数</div>
          </div>
        </div>
      </div>

      <!-- 快捷功能网格 -->
      <div class="quick-grid">
        <div class="grid-item" @click="$router.push('/accounts')">
          <van-icon name="gold-coin-o" size="24" />
          <span>我的账户</span>
        </div>
        <div class="grid-item" @click="$router.push('/transactions')">
          <van-icon name="bill-o" size="24" />
          <span>交易记录</span>
        </div>
        <div class="grid-item" @click="$router.push('/subscriptions')">
          <van-icon name="credit-pay" size="24" />
          <span>订阅管理</span>
        </div>
        <div class="grid-item" @click="$router.push('/settings')">
          <van-icon name="setting-o" size="24" />
          <span>设置</span>
        </div>
      </div>

      <!-- 功能菜单 -->
      <van-cell-group inset title="功能设置">
        <van-cell title="数据统计" is-link icon="chart-trending-o" />
        <van-cell title="导入导出" is-link icon="exchange" />
        <van-cell title="备份恢复" is-link icon="replay" />
      </van-cell-group>

      <!-- 订阅管理 -->
      <van-cell-group inset title="订阅管理">
        <van-cell
          title="我的订阅"
          is-link
          icon="credit-pay"
          @click="$router.push('/subscriptions')"
        >
          <template #right-icon>
            <van-badge
              v-if="dueSoonCount > 0"
              :content="dueSoonCount"
              :max="99"
            />
          </template>
        </van-cell>
        <van-cell
          title="添加订阅"
          is-link
          icon="plus"
          @click="$router.push('/subscription/add')"
        />
      </van-cell-group>

      <van-cell-group inset title="应用设置">
        <van-cell title="主题设置" is-link icon="setting-o" />
        <van-cell title="语言设置" is-link icon="globe-o" />
        <van-cell title="通知设置" is-link icon="bell" />
        <van-cell
          title="切换到网页版"
          is-link
          icon="desktop-o"
          @click="switchToWebVersion"
        />
      </van-cell-group>

      <van-cell-group inset title="帮助与反馈">
        <van-cell title="使用帮助" is-link icon="question-o" />
        <van-cell title="意见反馈" is-link icon="comment-o" />
        <van-cell title="关于我们" is-link icon="info-o" />
        <van-cell
          title="设备信息"
          is-link
          icon="phone-o"
          @click="showDeviceInfo = true"
        />
      </van-cell-group>



      <!-- 退出登录 -->
      <div class="logout-section">
        <van-button 
          block 
          type="danger" 
          plain
          @click="handleLogout"
          :loading="loading"
        >
          退出登录
        </van-button>
      </div>
    </div>

    <!-- 设备信息弹窗 -->
    <DeviceInfo v-model:show="showDeviceInfo" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useSubscriptionsStore } from '@/stores/subscriptions'
import { useAccountsStore } from '@/stores/accounts'
import { useTransactionsStore } from '@/stores/transactions'
import { showConfirmDialog, showToast } from 'vant'
import { getDeviceInfo } from '@/utils/device'
import DeviceInfo from '@/components/DeviceInfo.vue'

const router = useRouter()
const authStore = useAuthStore()
const subscriptionsStore = useSubscriptionsStore()
const accountsStore = useAccountsStore()
const transactionsStore = useTransactionsStore()

const loading = ref(false)
const showDeviceInfo = ref(false)

const user = computed(() => authStore.user)
const dueSoonCount = computed(() => subscriptionsStore.dueSoonCount)

// 统计数据
const accountsCount = computed(() => accountsStore.accounts.length)
const transactionsCount = computed(() => transactionsStore.transactions.length)
const usageDays = computed(() => {
  if (!user.value?.created_at) return 0
  const createdDate = new Date(user.value.created_at)
  const now = new Date()
  const diffTime = Math.abs(now - createdDate)
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
})

// 编辑资料
const editProfile = () => {
  // TODO: 实现编辑资料功能
  console.log('编辑资料')
}

const handleLogout = async () => {
  try {
    await showConfirmDialog({
      title: '确认退出',
      message: '确定要退出登录吗？'
    })

    loading.value = true
    await authStore.logout()

    showToast({
      message: '已退出登录',
      type: 'success'
    })

    router.replace('/login')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('退出登录失败:', error)
      showToast({
        message: '退出失败',
        type: 'fail'
      })
    }
  } finally {
    loading.value = false
  }
}

const switchToWebVersion = async () => {
  try {
    await showConfirmDialog({
      title: '切换到网页版',
      message: '网页版适合在电脑上使用，确定要切换吗？'
    })

    const deviceInfo = getDeviceInfo()
    const webUrl = `http://${deviceInfo.currentHost}:8000${window.location.pathname}`

    showToast({
      message: '正在跳转到网页版...',
      type: 'loading',
      duration: 1500
    })

    setTimeout(() => {
      window.location.href = webUrl
    }, 1500)

  } catch (error) {
    if (error !== 'cancel') {
      console.error('切换失败:', error)
    }
  }
}

// 组件挂载时获取数据
onMounted(async () => {
  try {
    await Promise.all([
      subscriptionsStore.fetchSubscriptions(),
      subscriptionsStore.fetchDueSoon(),
      accountsStore.fetchAccounts(),
      transactionsStore.fetchTransactions()
    ])
  } catch (error) {
    console.error('获取数据失败:', error)
  }
})
</script>

<style scoped>
.profile-page {
  background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
  min-height: 100vh;
}

/* 紧凑型用户卡片 */
.compact-user-card {
  background: white;
  margin: 16px;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 24px rgba(30, 58, 138, 0.15);
}

/* 用户信息行 */
.user-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.user-avatar-compact {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #1e40af 0%, #3730a3 100%);
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-right: 16px;
}

.user-avatar-compact .van-icon {
  color: white;
}

.status-dot {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 14px;
  height: 14px;
  background: #10b981;
  border-radius: 50%;
  border: 2px solid white;
}

.user-details {
  flex: 1;
}

.user-details h3 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 700;
  color: #1e40af;
}

.user-details p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #64748b;
}

.user-badges {
  display: flex;
  gap: 8px;
}

.badge {
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.badge.verified {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.badge.member {
  background: rgba(30, 64, 175, 0.1);
  color: #1e40af;
}

.user-actions-compact {
  padding: 12px;
  background: rgba(30, 64, 175, 0.1);
  border-radius: 12px;
  cursor: pointer;
}

.user-actions-compact .van-icon {
  color: #1e40af;
}

/* 统计数据行 */
.stats-row {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 16px 0;
  background: rgba(30, 64, 175, 0.05);
  border-radius: 16px;
}

.stat-compact {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-compact:active {
  transform: scale(0.95);
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #1e40af;
  margin-bottom: 4px;
  font-family: 'SF Mono', Monaco, monospace;
}

.stat-name {
  font-size: 12px;
  color: #64748b;
  font-weight: 600;
}

.stat-divider {
  width: 1px;
  height: 40px;
  background: rgba(30, 64, 175, 0.1);
}

/* 快捷功能网格 */
.quick-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin: 16px;
}

.grid-item {
  background: white;
  padding: 20px 12px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(30, 58, 138, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.grid-item:active {
  transform: scale(0.95);
  box-shadow: 0 2px 8px rgba(30, 58, 138, 0.15);
}

.grid-item .van-icon {
  color: #1e40af;
  margin-bottom: 8px;
}

.grid-item span {
  display: block;
  font-size: 12px;
  color: #64748b;
  font-weight: 600;
}

.logout-section {
  padding: 24px 16px;
}
</style>
