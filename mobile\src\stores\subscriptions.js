import { defineStore } from 'pinia'
import { subscriptionsAPI } from '@/api/subscriptions'

export const useSubscriptionsStore = defineStore('subscriptions', {
  state: () => ({
    subscriptions: [],
    stats: {
      total_subscriptions: 0,
      monthly_cost: 0,
      yearly_cost: 0,
      due_soon_count: 0,
      service_types: {}
    },
    dueSoonSubscriptions: [],
    loading: false,
    error: null
  }),

  getters: {
    // 获取活跃订阅
    activeSubscriptions: (state) => {
      return state.subscriptions.filter(sub => sub.is_active)
    },

    // 获取即将到期的订阅
    dueSoonCount: (state) => {
      return state.subscriptions.filter(sub => sub.is_due_soon).length
    },

    // 按服务类型分组
    subscriptionsByType: (state) => {
      const grouped = {}
      state.subscriptions.forEach(sub => {
        if (!grouped[sub.service_type]) {
          grouped[sub.service_type] = []
        }
        grouped[sub.service_type].push(sub)
      })
      return grouped
    },

    // 月度总费用
    totalMonthlyCost: (state) => {
      return state.subscriptions.reduce((total, sub) => {
        if (!sub.is_active) return total
        
        let monthlyAmount = 0
        switch (sub.billing_cycle) {
          case 'monthly':
            monthlyAmount = sub.amount
            break
          case 'quarterly':
            monthlyAmount = sub.amount / 3
            break
          case 'yearly':
            monthlyAmount = sub.amount / 12
            break
          case 'weekly':
            monthlyAmount = sub.amount * 4.33
            break
          default:
            monthlyAmount = sub.amount
        }
        return total + monthlyAmount
      }, 0)
    }
  },

  actions: {
    // 获取所有订阅
    async fetchSubscriptions() {
      this.loading = true
      this.error = null
      
      try {
        const response = await subscriptionsAPI.getSubscriptions()
        if (response.success) {
          this.subscriptions = response.subscriptions || []
        } else {
          throw new Error(response.error || '获取订阅列表失败')
        }
      } catch (error) {
        this.error = error.message
        console.error('获取订阅列表失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 获取订阅统计
    async fetchStats() {
      try {
        const response = await subscriptionsAPI.getStats()
        if (response.success) {
          this.stats = response.stats
        }
      } catch (error) {
        console.error('获取订阅统计失败:', error)
      }
    },

    // 获取即将到期的订阅
    async fetchDueSoon(days = 7) {
      try {
        const response = await subscriptionsAPI.getDueSoon(days)
        if (response.success) {
          this.dueSoonSubscriptions = response.subscriptions || []
        }
      } catch (error) {
        console.error('获取即将到期订阅失败:', error)
      }
    },

    // 创建订阅
    async createSubscription(subscriptionData) {
      this.loading = true
      this.error = null
      
      try {
        const response = await subscriptionsAPI.createSubscription(subscriptionData)
        if (response.success) {
          // 重新获取订阅列表
          await this.fetchSubscriptions()
          await this.fetchStats()
          return response
        } else {
          throw new Error(response.error || '创建订阅失败')
        }
      } catch (error) {
        this.error = error.message
        console.error('创建订阅失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 更新订阅
    async updateSubscription(id, subscriptionData) {
      this.loading = true
      this.error = null
      
      try {
        const response = await subscriptionsAPI.updateSubscription(id, subscriptionData)
        if (response.success) {
          // 重新获取订阅列表
          await this.fetchSubscriptions()
          await this.fetchStats()
          return response
        } else {
          throw new Error(response.error || '更新订阅失败')
        }
      } catch (error) {
        this.error = error.message
        console.error('更新订阅失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 删除订阅
    async deleteSubscription(id) {
      this.loading = true
      this.error = null
      
      try {
        const response = await subscriptionsAPI.deleteSubscription(id)
        if (response.success) {
          // 重新获取订阅列表
          await this.fetchSubscriptions()
          await this.fetchStats()
          return response
        } else {
          throw new Error(response.error || '删除订阅失败')
        }
      } catch (error) {
        this.error = error.message
        console.error('删除订阅失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 续费订阅
    async renewSubscription(id) {
      try {
        const response = await subscriptionsAPI.renewSubscription(id)
        if (response.success) {
          // 重新获取订阅列表
          await this.fetchSubscriptions()
          await this.fetchStats()
          return response
        } else {
          throw new Error(response.error || '续费失败')
        }
      } catch (error) {
        console.error('续费订阅失败:', error)
        throw error
      }
    },

    // 获取单个订阅详情
    async getSubscription(id) {
      try {
        const response = await subscriptionsAPI.getSubscription(id)
        if (response.success) {
          return response.subscription
        } else {
          throw new Error(response.error || '获取订阅详情失败')
        }
      } catch (error) {
        console.error('获取订阅详情失败:', error)
        throw error
      }
    },

    // 清除错误状态
    clearError() {
      this.error = null
    },

    // 重置状态
    reset() {
      this.subscriptions = []
      this.stats = {
        total_subscriptions: 0,
        monthly_cost: 0,
        yearly_cost: 0,
        due_soon_count: 0,
        service_types: {}
      }
      this.dueSoonSubscriptions = []
      this.loading = false
      this.error = null
    }
  }
})
