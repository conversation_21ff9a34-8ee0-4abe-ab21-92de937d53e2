import{a as f,l as M,m as de,c as d,e as o,d as v,p as h,v as S,w as r,f as i,t as V,g as pe,o as s,i as k,F as N,k as O,n as me,x as fe,q as ye,s as x}from"./index--MNqwREY.js";import{u as _e}from"./transactions-F5TqZ8Qm.js";import{a as ge,f as he}from"./format-wz8GKlWC.js";import{_ as ke}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{s as xe}from"./function-call-w7FI8rRn.js";import"./transactions-CrdnwW_k.js";const Ce={class:"transactions-page"},we={class:"nav-actions"},be={class:"page-container"},Se={key:0,class:"search-section"},Ve={class:"filter-section"},Te={key:1,class:"search-result-tip"},$e={key:2,class:"loading-container"},Ue={key:3,class:"empty-container"},ze={key:1,class:"batch-toolbar"},De={class:"batch-info"},Ae={class:"batch-actions"},Be={__name:"index",setup(Me){const L=pe(),_=_e(),T=f(!1),C=f(!1),p=f(""),w=f(!1),y=f(!1),t=f([]),g=f(!1),u=f({type:"all",category:"all",currency:"all"}),R=[{text:"全部类型",value:"all"},{text:"收入",value:"income"},{text:"支出",value:"expense"}],E=[{text:"全部分类",value:"all"},{text:"储蓄",value:"储蓄"},{text:"固定",value:"固定"},{text:"流动",value:"流动"},{text:"债务",value:"债务"}],F=[{text:"全部货币",value:"all"},{text:"RM",value:"RM"},{text:"USD",value:"USD"},{text:"CNY",value:"CNY"}],P=[{name:"批量管理",value:"batch"},{name:"导出数据",value:"export"},{name:"统计分析",value:"stats"}],m=M(()=>{let a=_.transactions;if(p.value.trim()){const e=p.value.trim().toLowerCase();a=a.filter(l=>l.description.toLowerCase().includes(e)||l.category.toLowerCase().includes(e)||l.attachment&&l.attachment.toLowerCase().includes(e))}return u.value.type!=="all"&&(a=a.filter(e=>e.type===u.value.type)),u.value.category!=="all"&&(a=a.filter(e=>e.category===u.value.category)),u.value.currency!=="all"&&(a=a.filter(e=>e.currency===u.value.currency)),a.sort((e,l)=>new Date(l.date)-new Date(e.date))}),Y=M(()=>{const a={};return m.value.forEach(e=>{const l=ge(e.date);a[l]||(a[l]={date:l,transactions:[]}),a[l].transactions.push(e)}),Object.values(a)});de(async()=>{await j()});const j=async()=>{T.value=!0;try{console.log("开始加载交易数据..."),await _.fetchTransactions(),console.log("交易数据加载完成:",_.transactions),console.log("交易数量:",_.transactions.length)}catch(a){console.error("加载交易失败:",a)}finally{T.value=!1}},q=a=>{L.push(`/transaction/detail/${a.id}`)},K=a=>{console.log("搜索:",a)},U=()=>{C.value=!1,p.value=""},G=()=>{p.value=""},H=()=>{p.value="",C.value=!1},J=a=>{y.value?Q(a.id):q(a)},Q=a=>{const e=t.value.indexOf(a);e>-1?t.value.splice(e,1):t.value.push(a),z()},z=()=>{const a=t.value.length===m.value.length&&m.value.length>0;g.value!==a&&(g.value=a)},W=(a,e)=>{if(e)t.value.includes(a)||t.value.push(a);else{const l=t.value.indexOf(a);l>-1&&t.value.splice(l,1)}z()},X=a=>{a?t.value=m.value.map(e=>e.id):t.value=[]},Z=()=>{y.value=!0,t.value=[],g.value=!1},D=()=>{y.value=!1,t.value=[],g.value=!1},I=async()=>{if(t.value.length===0){x("请选择要删除的交易");return}try{await xe({title:"确认删除",message:`确定要删除选中的 ${t.value.length} 条交易记录吗？此操作不可撤销。`});const a=t.value.map(e=>_.deleteTransaction(e));await Promise.all(a),x({message:`成功删除 ${t.value.length} 条交易记录`,type:"success"}),D()}catch(a){a!=="cancel"&&(console.error("批量删除失败:",a),x({message:"删除失败，请重试",type:"fail"}))}},ee=a=>{switch(w.value=!1,a.value){case"batch":Z();break;case"export":ae();break;case"stats":te();break}},ae=()=>{x("导出功能开发中...")},te=()=>{x("统计功能开发中...")};return(a,e)=>{const l=i("van-icon"),le=i("van-nav-bar"),ne=i("van-search"),$=i("van-dropdown-item"),oe=i("van-dropdown-menu"),b=i("van-button"),se=i("van-loading"),A=i("van-checkbox"),ie=i("van-cell"),ue=i("van-cell-group"),ce=i("van-list"),re=i("van-floating-bubble"),ve=i("van-action-sheet");return s(),d("div",Ce,[o(le,{title:"交易记录",fixed:"",placeholder:"","safe-area-inset-top":""},{right:r(()=>[v("div",we,[o(l,{name:"search",size:"18",onClick:e[0]||(e[0]=n=>C.value=!0),class:"nav-icon"}),o(l,{name:"ellipsis",size:"18",onClick:e[1]||(e[1]=n=>w.value=!0),class:"nav-icon"}),o(l,{name:"plus",size:"18",onClick:e[2]||(e[2]=n=>a.$router.push("/transaction/add")),class:"nav-icon"})])]),_:1}),v("div",be,[C.value?(s(),d("div",Se,[o(ne,{modelValue:p.value,"onUpdate:modelValue":e[3]||(e[3]=n=>p.value=n),placeholder:"搜索交易描述、分类...","show-action":"",onSearch:K,onCancel:U,onClear:G},{action:r(()=>[v("div",{onClick:U},"取消")]),_:1},8,["modelValue"])])):S("",!0),v("div",Ve,[o(oe,null,{default:r(()=>[o($,{modelValue:u.value.type,"onUpdate:modelValue":e[4]||(e[4]=n=>u.value.type=n),options:R},null,8,["modelValue"]),o($,{modelValue:u.value.category,"onUpdate:modelValue":e[5]||(e[5]=n=>u.value.category=n),options:E},null,8,["modelValue"]),o($,{modelValue:u.value.currency,"onUpdate:modelValue":e[6]||(e[6]=n=>u.value.currency=n),options:F},null,8,["modelValue"])]),_:1})]),p.value&&m.value.length>0?(s(),d("div",Te,[v("span",null,"找到 "+V(m.value.length)+" 条相关记录",1),o(b,{type:"primary",size:"mini",plain:"",onClick:H},{default:r(()=>e[11]||(e[11]=[k(" 清除搜索 ")])),_:1,__:[11]})])):S("",!0),T.value?(s(),d("div",$e,[o(se,{size:"24px"}),e[12]||(e[12]=v("span",null,"加载中...",-1))])):m.value.length===0?(s(),d("div",Ue,[e[14]||(e[14]=v("div",{class:"empty-icon"},"📝",-1)),e[15]||(e[15]=v("p",null,"暂无交易记录",-1)),o(b,{type:"primary",size:"small",onClick:e[7]||(e[7]=n=>a.$router.push("/transaction/add"))},{default:r(()=>e[13]||(e[13]=[k(" 立即记账 ")])),_:1,__:[13]})])):(s(),h(ce,{key:4},{default:r(()=>[(s(!0),d(N,null,O(Y.value,n=>(s(),h(ue,{key:n.date,title:n.date,inset:""},{default:r(()=>[(s(!0),d(N,null,O(n.transactions,c=>(s(),h(ie,{key:c.id,title:c.description,label:c.category,value:me(he)(c.amount,c.currency),"value-class":c.type==="income"?"amount income":"amount expense","is-link":!y.value,onClick:B=>J(c)},{icon:r(()=>[y.value?(s(),h(A,{key:0,"model-value":t.value.includes(c.id),"onUpdate:modelValue":B=>W(c.id,B),onClick:e[8]||(e[8]=fe(()=>{},["stop"]))},null,8,["model-value","onUpdate:modelValue"])):(s(),d("div",{key:1,class:ye(["transaction-icon",c.type])},V(c.type==="income"?"💰":"💸"),3))]),_:2},1032,["title","label","value","value-class","is-link","onClick"]))),128))]),_:2},1032,["title"]))),128))]),_:1}))]),y.value?S("",!0):(s(),h(re,{key:0,axis:"xy",icon:"plus",gap:{x:24,y:80},onClick:e[9]||(e[9]=n=>a.$router.push("/transaction/add"))})),y.value?(s(),d("div",ze,[v("div",De,[o(A,{"model-value":g.value,"onUpdate:modelValue":X},{default:r(()=>[k(" 全选 ("+V(t.value.length)+"/"+V(m.value.length)+") ",1)]),_:1},8,["model-value"])]),v("div",Ae,[o(b,{type:"danger",size:"small",onClick:I,disabled:t.value.length===0},{default:r(()=>e[16]||(e[16]=[k(" 删除 ")])),_:1,__:[16]},8,["disabled"]),o(b,{type:"default",size:"small",onClick:D},{default:r(()=>e[17]||(e[17]=[k(" 取消 ")])),_:1,__:[17]})])])):S("",!0),o(ve,{show:w.value,"onUpdate:show":e[10]||(e[10]=n=>w.value=n),actions:P,onSelect:ee,"cancel-text":"取消"},null,8,["show"])])}}},Pe=ke(Be,[["__scopeId","data-v-d935a56f"]]);export{Pe as default};
