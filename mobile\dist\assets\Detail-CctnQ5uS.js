import{a as f,l as N,h as O,m as Q,s as B,c,e as o,d as a,w as v,f as u,p as x,v as L,q as F,t as r,n as y,g as W,o as i,F as X,k as Z,i as M,H as ee}from"./index--MNqwREY.js";import{useBudgetsStore as te}from"./budgets-DJh-v5Y8.js";import{u as ae}from"./transactions-F5TqZ8Qm.js";import{f as k}from"./format-wz8GKlWC.js";import{_ as se}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./transactions-CrdnwW_k.js";const ne={class:"budget-detail-page"},oe={class:"page-container"},le={key:0,class:"loading-container"},re={key:1},ie={class:"budget-info-card"},ue={class:"budget-header"},de={class:"budget-details"},ce={class:"usage-info"},ve={class:"usage-header"},ge={class:"usage-details"},pe={class:"usage-item"},_e={class:"amount used"},me={class:"usage-item"},fe={class:"amount remaining"},ye={class:"usage-item"},ke={class:"amount total"},be={key:0,class:"loading-container small"},he={key:1,class:"empty-transactions"},we={class:"action-buttons"},Ce={key:2,class:"error-container"},$e={__name:"Detail",setup(Se){const _=W(),V=O(),b=te(),j=ae(),h=f(!1),w=f(!1),C=f(!1),g=f([]),T=N(()=>V.params.id),t=N(()=>b.currentBudget);Q(async()=>{await I(),await R()});const I=async()=>{h.value=!0;try{await b.fetchBudget(T.value),t.value||B({message:"预算不存在",type:"fail"})}catch(s){console.error("加载预算详情失败:",s),B({message:"加载预算详情失败",type:"fail"})}finally{h.value=!1}},R=async()=>{if(t.value){w.value=!0;try{const s=await j.fetchTransactions({type:"expense",description:t.value.project_name,start_date:t.value.start_date,end_date:t.value.end_date,limit:10});g.value=s||[]}catch(s){console.error("加载相关交易失败:",s),g.value=[]}finally{w.value=!1}}},q=s=>new Date(s).toLocaleDateString("zh-CN"),P=(s,e)=>{if(!s||!e)return"";const l=new Date(s),d=new Date(e);if(l.getFullYear()===d.getFullYear()&&l.getMonth()===d.getMonth())return`${l.getFullYear()}年${String(l.getMonth()+1).padStart(2,"0")}月`;const p=String(l.getMonth()+1).padStart(2,"0"),m=String(d.getMonth()+1).padStart(2,"0");return l.getFullYear()===d.getFullYear()?`${l.getFullYear()}年${p}月-${m}月`:`${l.getFullYear()}年${p}月-${d.getFullYear()}年${m}月`},A=s=>({normal:"正常",warning:"警告",danger:"危险",exceeded:"超支"})[s]||"正常",E=s=>s>=100?"#ee0a24":s>=90?"#ff976a":s>=80?"#ffd21e":"#07c160",H=s=>({储蓄:"💰",固定:"🏠",流动:"🛒",债务:"💳"})[s]||"💰",D=()=>{t.value&&_.push(`/budget/edit/${t.value.id}`)},U=async()=>{try{await b.deleteBudget(T.value),ee("预算删除成功"),_.back()}catch(s){console.error("删除预算失败:",s),B({message:"删除预算失败",type:"fail"})}},G=s=>{_.push(`/transaction/detail/${s.id}`)},J=()=>{_.push({path:"/transactions",query:{category:t.value.category,type:"expense"}})};return(s,e)=>{var z;const l=u("van-icon"),d=u("van-nav-bar"),p=u("van-loading"),m=u("van-progress"),$=u("van-cell"),Y=u("van-cell-group"),S=u("van-button"),K=u("van-dialog");return i(),c("div",ne,[o(d,{title:((z=t.value)==null?void 0:z.project_name)||"预算详情","left-arrow":"",onClickLeft:e[0]||(e[0]=n=>s.$router.back()),fixed:"",placeholder:""},{right:v(()=>[o(l,{name:"edit",size:"18",onClick:D})]),_:1},8,["title"]),a("div",oe,[h.value?(i(),c("div",le,[o(p,{size:"24px"}),e[4]||(e[4]=a("span",null,"加载中...",-1))])):t.value?(i(),c("div",re,[a("div",ie,[a("div",ue,[a("div",{class:F(["budget-icon",t.value.status])},r(H(t.value.category)),3),a("div",de,[a("h3",null,r(t.value.project_name||t.value.category),1),a("p",null,r(t.value.category)+" • "+r(P(t.value.start_date,t.value.end_date))+" • "+r(t.value.currency),1)]),a("div",{class:F(["status-badge",t.value.status])},r(A(t.value.status)),3)]),a("div",ce,[a("div",ve,[e[5]||(e[5]=a("span",null,"预算使用情况",-1)),a("span",{class:F(["percentage",t.value.status])},r((t.value.usage_percentage||0).toFixed(1))+"% ",3)]),o(m,{percentage:Math.min(t.value.usage_percentage||0,100),color:E(t.value.usage_percentage||0),"stroke-width":"12"},null,8,["percentage","color"]),a("div",ge,[a("div",pe,[e[6]||(e[6]=a("span",{class:"label"},"已使用",-1)),a("span",_e,r(y(k)(t.value.used_amount||0)),1)]),a("div",me,[e[7]||(e[7]=a("span",{class:"label"},"剩余",-1)),a("span",fe,r(y(k)(t.value.remaining_amount||0)),1)]),a("div",ye,[e[8]||(e[8]=a("span",{class:"label"},"总预算",-1)),a("span",ke,r(y(k)(t.value.amount)),1)])])])]),t.value.description?(i(),x(Y,{key:0,inset:"",title:"预算描述"},{default:v(()=>[o($,{value:t.value.description},null,8,["value"])]),_:1})):L("",!0),o(Y,{inset:"",title:"相关交易"},{default:v(()=>[w.value?(i(),c("div",be,[o(p,{size:"16px"}),e[9]||(e[9]=a("span",null,"加载交易记录...",-1))])):g.value.length===0?(i(),c("div",he,e[10]||(e[10]=[a("p",null,"暂无相关交易记录",-1)]))):(i(!0),c(X,{key:2},Z(g.value,n=>(i(),x($,{key:n.id,title:n.description||n.category,label:q(n.date),value:y(k)(n.amount,n.currency),"is-link":"",onClick:Be=>G(n)},{icon:v(()=>e[11]||(e[11]=[a("div",{class:"transaction-icon"},"💸",-1)])),_:2},1032,["title","label","value","onClick"]))),128)),g.value.length>0?(i(),x($,{key:3,title:"查看全部交易","is-link":"",onClick:J})):L("",!0)]),_:1}),a("div",we,[o(S,{type:"primary",block:"",round:"",onClick:D},{default:v(()=>e[12]||(e[12]=[M(" 编辑预算 ")])),_:1,__:[12]}),o(S,{type:"danger",block:"",round:"",onClick:e[1]||(e[1]=n=>C.value=!0)},{default:v(()=>e[13]||(e[13]=[M(" 删除预算 ")])),_:1,__:[13]})])])):(i(),c("div",Ce,[e[15]||(e[15]=a("div",{class:"error-icon"},"❌",-1)),e[16]||(e[16]=a("p",null,"预算不存在",-1)),o(S,{type:"primary",size:"small",onClick:e[2]||(e[2]=n=>s.$router.back())},{default:v(()=>e[14]||(e[14]=[M(" 返回 ")])),_:1,__:[14]})]))]),o(K,{show:C.value,"onUpdate:show":e[3]||(e[3]=n=>C.value=n),title:"删除预算",message:"确定要删除这个预算吗？删除后无法恢复。","show-cancel-button":"",onConfirm:U},null,8,["show"])])}}},ze=se($e,[["__scopeId","data-v-6872dd67"]]);export{ze as default};
