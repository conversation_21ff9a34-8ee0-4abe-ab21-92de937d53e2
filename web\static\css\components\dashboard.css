/*******************************************
 *          Dashboard 仪表盘样式
 *******************************************/

/* 货币筛选器样式 */
.currency-filter {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md);
  background-color: var(--color-card);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
}

.currency-filter label {
  margin-right: var(--spacing-md);
  font-weight: 500;
}

.currency-filter select {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--color-border);
  background-color: var(--color-bg);
  min-width: 150px;
}
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.stat-card {
  background-color: var(--color-card);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  position: relative;
}

.stat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.stat-card-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-income-bg);
  color: var(--color-primary);
}

.stat-card-title {
  font-size: var(--font-size-base);
  color: var(--color-text-light);
}

.stat-card-value {
  font-size: var(--font-size-xxl);
  font-weight: 700;
  margin-bottom: var(--spacing-xs);
}

.stat-card-comparison {
  font-size: var(--font-size-xs);
  color: var(--color-text-light);
}

.stat-card-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 20px;
  font-size: var(--font-size-xs);
  margin-left: var(--spacing-xs);
}

.stat-card-badge.positive {
  background-color: var(--color-income-bg);
  color: var(--color-income);
}

.stat-card-badge.negative {
  background-color: var(--color-expense-bg);
  color: var(--color-expense);
}

.chart-row {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.chart-card {
  background-color: var(--color-card);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.chart-title {
  font-size: var(--font-size-md);
  font-weight: 600;
}

.chart-period {
  display: flex;
  align-items: center;
  background-color: var(--color-hover);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-sm);
  cursor: pointer;
}

.chart-period-icon {
  margin-left: var(--spacing-xs);
}

.chart-container {
  height: 300px;
  position: relative;
}

.customer-growth-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.customer-growth-title {
  font-size: var(--font-size-md);
  font-weight: 600;
}

.customer-growth-chart {
  height: 250px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .chart-row {
    grid-template-columns: 1fr;
  }
}
