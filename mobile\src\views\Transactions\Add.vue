<template>
  <div class="add-transaction-page">
    <van-nav-bar
      title="添加交易"
      left-arrow
      @click-left="handleBack"
      fixed
      placeholder
    >
      <template #right>
        <van-button
          type="primary"
          size="small"
          :loading="loading"
          @click="handleSubmit"
        >
          保存
        </van-button>
      </template>
    </van-nav-bar>

    <div class="page-container">
      <!-- 交易类型选择 -->
      <div class="type-selector">
        <van-tabs v-model:active="form.type" @change="onTypeChange">
          <van-tab title="支出" name="expense">
            <div class="tab-content">
              <van-icon name="minus" class="tab-icon expense" />
              <span>记录支出</span>
            </div>
          </van-tab>
          <van-tab title="收入" name="income">
            <div class="tab-content">
              <van-icon name="plus" class="tab-icon income" />
              <span>记录收入</span>
            </div>
          </van-tab>
          <van-tab title="转账" name="transfer">
            <div class="tab-content">
              <van-icon name="exchange" class="tab-icon transfer" />
              <span>账户转移</span>
            </div>
          </van-tab>
        </van-tabs>
      </div>

      <!-- 表单内容 -->
      <van-form @submit="handleSubmit" class="transaction-form">
        <!-- 金额输入 -->
        <div class="amount-section">
          <div class="amount-label">
            {{ form.type === 'expense' ? '支出金额' : '收入金额' }}
          </div>
          <div class="amount-input-container">
            <span class="currency-symbol">{{ currencySymbol }}</span>
            <van-field
              v-model="form.amount"
              type="number"
              placeholder="0.00"
              class="amount-input"
              :rules="[{ required: true, message: '请输入金额' }]"
              @input="onAmountInput"
            />
          </div>
        </div>

        <!-- 基本信息 -->
        <van-cell-group inset title="基本信息">
          <!-- 交易描述 -->
          <van-field
            v-model="form.description"
            label="描述"
            placeholder="请输入交易描述"
            :rules="[{ required: true, message: '请输入交易描述' }]"
            clearable
          />

          <!-- 交易日期 -->
          <van-field
            v-model="form.date"
            label="日期"
            placeholder="选择交易日期"
            readonly
            is-link
            @click="handleDateClick"
            :rules="[{ required: true, message: '请选择交易日期' }]"
          />

          <!-- 分类选择 -->
          <van-field
            v-model="form.category"
            label="分类"
            placeholder="选择交易分类"
            readonly
            is-link
            @click="showCategoryPicker = true"
            :rules="[{ required: true, message: '请选择交易分类' }]"
          />

          <!-- 账户选择 -->
          <van-field
            v-model="selectedAccountName"
            label="账户"
            placeholder="选择账户"
            readonly
            is-link
            @click="showAccountPicker = true"
            :rules="[{ required: true, message: '请选择账户' }]"
          />

          <!-- 货币选择 -->
          <van-field
            v-model="form.currency"
            label="货币"
            placeholder="选择货币"
            readonly
            is-link
            @click="showCurrencyPicker = true"
            :rules="[{ required: true, message: '请选择货币' }]"
          />

          <!-- KDI出金账户选择 -->
          <van-field
            v-if="showWithdrawAccountField"
            v-model="selectedWithdrawAccountName"
            label="出金账户"
            placeholder="选择出金账户"
            readonly
            is-link
            @click="showWithdrawAccountPicker = true"
            :rules="[{ required: true, message: '请选择出金账户' }]"
          />
        </van-cell-group>

        <!-- 附加信息 -->
        <van-cell-group inset title="附加信息">
          <van-field
            v-model="form.attachment"
            label="备注"
            type="textarea"
            placeholder="添加备注信息（可选）"
            rows="3"
            autosize
            maxlength="200"
            show-word-limit
          />
        </van-cell-group>
      </van-form>
    </div>

    <!-- 日期选择器 -->
    <van-popup
      v-model:show="showDatePicker"
      position="bottom"
      round
    >
      <van-date-picker
        v-model="selectedDate"
        title="选择日期"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>

    <!-- 分类选择器 -->
    <van-popup v-model:show="showCategoryPicker" position="bottom">
      <van-picker
        :columns="categoryColumns"
        title="选择分类"
        @confirm="onCategoryConfirm"
        @cancel="showCategoryPicker = false"
      />
    </van-popup>

    <!-- 账户选择器 -->
    <van-popup v-model:show="showAccountPicker" position="bottom">
      <van-picker
        :columns="accountColumns"
        title="选择账户"
        @confirm="onAccountConfirm"
        @cancel="showAccountPicker = false"
      />
    </van-popup>

    <!-- 货币选择器 -->
    <van-popup v-model:show="showCurrencyPicker" position="bottom">
      <van-picker
        :columns="currencyColumns"
        title="选择货币"
        @confirm="onCurrencyConfirm"
        @cancel="showCurrencyPicker = false"
      />
    </van-popup>

    <!-- KDI出金账户选择器 -->
    <van-popup v-model:show="showWithdrawAccountPicker" position="bottom">
      <van-picker
        :columns="withdrawAccountColumns"
        title="选择出金账户"
        @confirm="onWithdrawAccountConfirm"
        @cancel="showWithdrawAccountPicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useTransactionsStore } from '@/stores/transactions'
import { useAccountsStore } from '@/stores/accounts'
import { showToast, showConfirmDialog } from 'vant'
import { formatCurrency } from '@/utils/format'
import { expenseCategories, incomeCategories, getCategoryOptions } from '@/config/categories'
import { getCurrencyOptions, getCurrencySymbol, DEFAULT_CURRENCY } from '@/config/currencies'

const router = useRouter()
const transactionsStore = useTransactionsStore()
const accountsStore = useAccountsStore()

// 响应式数据
const loading = ref(false)
const showDatePicker = ref(false)
const showCategoryPicker = ref(false)
const showAccountPicker = ref(false)
const showCurrencyPicker = ref(false)
const showWithdrawAccountPicker = ref(false) // KDI出金账户选择器

// 表单数据
const form = reactive({
  type: 'expense', // 默认为支出
  amount: '',
  description: '',
  date: '',
  category: '',
  account: '',
  currency: 'MYR',
  attachment: '',
  withdrawAccount: '' // KDI出金目标账户
})

// 日期相关 - Vant 4.x 使用数组格式 [year, month, day]
const selectedDate = ref([])
const minDate = new Date(2020, 0, 1)
const maxDate = new Date(2030, 11, 31)

// 分类数据从配置文件导入

// 货币数据 - 使用统一配置
const currencies = getCurrencyOptions()

// 计算属性
const categoryColumns = computed(() => {
  // 获取当前选择的账户信息
  const selectedAccount = accountsStore.accounts.find(acc => acc.id === form.account)
  const accountName = selectedAccount?.name || ''

  // 使用新的getCategoryOptions函数，支持KDI账户特殊分类
  const categoryOptions = getCategoryOptions(form.type, accountName)
  return categoryOptions
})

const accountColumns = computed(() => {
  // 根据选择的货币过滤账户
  return accountsStore.accounts
    .filter(account => account.currency === form.currency)
    .map(account => ({
      text: `${account.name} (${account.type})`,
      value: account.id
    }))
})

const currencyColumns = computed(() => currencies)

// KDI出金账户选择（只显示马币的银行账户和电子钱包）
const withdrawAccountColumns = computed(() => {
  return accountsStore.accounts
    .filter(account =>
      account.currency === 'MYR' &&
      (account.type === 'bank' || account.type === 'ewallet') &&
      account.id !== form.account // 排除KDI账户本身
    )
    .map(account => ({
      text: `${account.name} (${account.type})`,
      value: account.id
    }))
})

const selectedAccountName = computed(() => {
  const account = accountsStore.accounts.find(acc => acc.id === form.account)
  return account ? `${account.name} (${account.type})` : ''
})

// 判断是否为KDI账户
const isKDIAccount = computed(() => {
  const account = accountsStore.accounts.find(acc => acc.id === form.account)
  return account?.name === 'KDI'
})

// 判断是否显示出金账户选择器
const showWithdrawAccountField = computed(() => {
  return isKDIAccount.value && form.type === 'expense' && form.category === '出金'
})

// 获取出金账户名称
const selectedWithdrawAccountName = computed(() => {
  const account = accountsStore.accounts.find(acc => acc.id === form.withdrawAccount)
  return account ? `${account.name} (${account.type})` : ''
})

const currencySymbol = computed(() => {
  return getCurrencySymbol(form.currency)
})

// 初始化
onMounted(async () => {
  // 设置默认日期为今天
  const today = new Date()
  selectedDate.value = [today.getFullYear(), today.getMonth() + 1, today.getDate()]
  form.date = formatDate(today)

  // 加载账户数据
  try {
    await accountsStore.fetchAccounts()

    // 如果有账户，设置与默认货币匹配的第一个账户为默认账户
    if (accountsStore.accounts.length > 0) {
      // 优先选择与默认货币（MYR）匹配的账户
      const matchingAccount = accountsStore.accounts.find(account => account.currency === form.currency)
      if (matchingAccount) {
        form.account = matchingAccount.id
      } else {
        // 如果没有匹配的账户，选择第一个账户并更新货币
        const firstAccount = accountsStore.accounts[0]
        form.account = firstAccount.id
        form.currency = firstAccount.currency
      }
    } else {
      // 如果没有账户，提示用户创建
      showToast({
        message: '请先创建一个账户',
        type: 'fail',
        duration: 3000
      })
    }
  } catch (error) {
    console.error('加载账户失败:', error)
    showToast('加载账户失败')
  }
})

// 方法
const formatDate = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

const onTypeChange = (type) => {
  // 如果选择转账，跳转到转账页面
  if (type === 'transfer') {
    // 重置选项卡到之前的状态
    form.type = form.type === 'transfer' ? 'expense' : form.type
    // 跳转到转账页面
    router.push('/transaction/transfer')
    return
  }

  // 切换类型时清空分类和出金账户
  form.category = ''
  form.withdrawAccount = ''
}

const onAmountInput = (value) => {
  // 确保金额为正数
  if (value && parseFloat(value) < 0) {
    form.amount = Math.abs(parseFloat(value)).toString()
  }
}

const handleDateClick = () => {
  console.log('日期字段被点击')
  console.log('当前selectedDate:', selectedDate.value)
  console.log('当前form.date:', form.date)
  showDatePicker.value = true
}

const onDateConfirm = () => {
  console.log('日期确认:', selectedDate.value)

  // 将数组格式 [year, month, day] 转换为 Date 对象
  const [year, month, day] = selectedDate.value
  const dateObj = new Date(year, month - 1, day) // month 需要减1，因为 Date 的月份从0开始

  const formattedDate = formatDate(dateObj)
  console.log('格式化后的日期:', formattedDate)
  form.date = formattedDate
  showDatePicker.value = false
}

const onCategoryConfirm = ({ selectedOptions }) => {
  form.category = selectedOptions[0]?.value || ''
  // 如果不是出金，清空出金账户
  if (form.category !== '出金') {
    form.withdrawAccount = ''
  }
  showCategoryPicker.value = false
}

const onAccountConfirm = ({ selectedOptions }) => {
  const oldAccount = form.account
  form.account = selectedOptions[0]?.value || ''

  // 如果账户发生变化，清空分类和出金账户
  if (oldAccount !== form.account) {
    form.category = ''
    form.withdrawAccount = ''
  }

  showAccountPicker.value = false
}

const onCurrencyConfirm = ({ selectedOptions }) => {
  const oldCurrency = form.currency
  const newCurrency = selectedOptions[0]?.value || DEFAULT_CURRENCY

  // 如果货币发生变化，清空账户选择和相关字段
  if (oldCurrency !== newCurrency) {
    form.currency = newCurrency
    form.account = ''
    form.category = ''
    form.withdrawAccount = ''
  }

  showCurrencyPicker.value = false
}

const onWithdrawAccountConfirm = ({ selectedOptions }) => {
  form.withdrawAccount = selectedOptions[0]?.value || ''
  showWithdrawAccountPicker.value = false
}

const validateForm = () => {
  if (!form.amount || parseFloat(form.amount) <= 0) {
    showToast('请输入有效金额')
    return false
  }

  if (!form.description.trim()) {
    showToast('请输入交易描述')
    return false
  }

  if (!form.date) {
    showToast('请选择交易日期')
    return false
  }

  if (!form.category) {
    showToast('请选择交易分类')
    return false
  }

  if (!form.account) {
    showToast('请选择账户')
    return false
  }

  if (!form.currency) {
    showToast('请选择货币')
    return false
  }

  // 如果是KDI出金，需要选择出金账户
  if (showWithdrawAccountField.value && !form.withdrawAccount) {
    showToast('请选择出金账户')
    return false
  }

  return true
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  loading.value = true

  try {
    const transactionData = {
      type: form.type,
      amount: parseFloat(form.amount),
      description: form.description.trim(),
      date: form.date,
      category: form.category,
      account: form.account,
      currency: form.currency,
      attachment: form.attachment.trim()
    }

    // 如果是KDI出金，需要创建双向交易
    if (showWithdrawAccountField.value && form.withdrawAccount) {
      await transactionsStore.createKDIWithdrawTransaction(transactionData, form.withdrawAccount)
    } else {
      await transactionsStore.createTransaction(transactionData)
    }

    showToast({
      message: '交易添加成功',
      type: 'success'
    })

    // 返回上一页
    router.back()

  } catch (error) {
    console.error('添加交易失败:', error)
    showToast({
      message: error.message || '添加交易失败',
      type: 'fail'
    })
  } finally {
    loading.value = false
  }
}

const handleBack = async () => {
  // 检查是否有未保存的数据
  const hasData = form.amount || form.description || form.category

  if (hasData) {
    const result = await showConfirmDialog({
      title: '确认离开',
      message: '您有未保存的数据，确定要离开吗？'
    })

    if (result) {
      router.back()
    }
  } else {
    router.back()
  }
}
</script>

<style scoped>
.add-transaction-page {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.page-container {
  padding-bottom: 20px;
}

.type-selector {
  background: white;
  margin-bottom: 12px;
}

:deep(.van-tabs__nav) {
  background: white;
}

:deep(.van-tab) {
  flex: 1;
}

.tab-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 0;
}

.tab-icon {
  font-size: 18px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.tab-icon.expense {
  background: linear-gradient(135deg, #ee0a24, #ff4757);
}

.tab-icon.income {
  background: linear-gradient(135deg, #07c160, #2ed573);
}

.tab-icon.transfer {
  background: linear-gradient(135deg, #1989fa, #4fc3f7);
}

.amount-section {
  background: white;
  padding: 24px 16px;
  margin-bottom: 12px;
  text-align: center;
}

.amount-label {
  font-size: 14px;
  color: #646566;
  margin-bottom: 12px;
}

.amount-input-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.currency-symbol {
  font-size: 24px;
  font-weight: 600;
  color: #323233;
}

:deep(.amount-input .van-field__control) {
  font-size: 32px;
  font-weight: 600;
  text-align: center;
  color: #323233;
}

:deep(.amount-input .van-field__body) {
  border: none;
  background: transparent;
}

.transaction-form {
  margin-bottom: 20px;
}

:deep(.van-cell-group__title) {
  padding: 16px 16px 8px;
  font-size: 14px;
  font-weight: 600;
  color: #323233;
}

:deep(.van-field__label) {
  font-weight: 500;
  color: #323233;
}

:deep(.van-field__control) {
  color: #323233;
}

:deep(.van-field__control::placeholder) {
  color: #c8c9cc;
}

/* 选择器样式 */
:deep(.van-popup) {
  border-radius: 16px 16px 0 0;
}

:deep(.van-picker__toolbar) {
  padding: 16px;
}

:deep(.van-picker__title) {
  font-size: 16px;
  font-weight: 600;
}

:deep(.van-picker__confirm) {
  color: #1989fa;
  font-weight: 600;
}

:deep(.van-picker__cancel) {
  color: #646566;
}

/* 日期选择器样式 */
:deep(.van-date-picker) {
  background: white;
}

:deep(.van-date-picker .van-picker__toolbar) {
  border-bottom: 1px solid #ebedf0;
  background: white;
}

:deep(.van-date-picker .van-picker__columns) {
  background: white;
}

:deep(.van-date-picker .van-picker__column) {
  background: white;
}

:deep(.van-date-picker .van-picker__column-item) {
  color: #323233;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .amount-input-container {
    flex-direction: column;
    gap: 4px;
  }

  .currency-symbol {
    font-size: 20px;
  }

  :deep(.amount-input .van-field__control) {
    font-size: 28px;
  }
}
</style>
