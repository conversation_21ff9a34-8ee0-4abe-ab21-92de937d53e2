import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Auth/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/Auth/Register.vue'),
    meta: {
      title: '注册',
      requiresAuth: false
    }
  },

  {
    path: '/',
    name: 'Layout',
    component: () => import('@/views/Layout/index.vue'),
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard/index.vue'),
        meta: {
          title: '仪表盘',
          icon: 'chart-trending-o',
          showInTabbar: true
        }
      },
      {
        path: '/transactions',
        name: 'Transactions',
        component: () => import('@/views/Transactions/index.vue'),
        meta: {
          title: '交易记录',
          icon: 'bill-o',
          showInTabbar: true
        }
      },
      {
        path: '/accounts',
        name: 'Accounts',
        component: () => import('@/views/Accounts/index.vue'),
        meta: {
          title: '账户管理',
          icon: 'credit-pay',
          showInTabbar: true
        }
      },
      {
        path: '/budgets',
        name: 'Budgets',
        component: () => import('@/views/Budgets/index.vue'),
        meta: {
          title: '预算管理',
          icon: 'bag-o',
          showInTabbar: true
        }
      },
      {
        path: '/profile',
        name: 'Profile',
        component: () => import('@/views/Profile/index.vue'),
        meta: {
          title: '个人中心',
          icon: 'user-o',
          showInTabbar: true
        }
      }
    ]
  },
  {
    path: '/transaction/add',
    name: 'AddTransaction',
    component: () => import('@/views/Transactions/Add.vue'),
    meta: {
      title: '添加交易',
      requiresAuth: true
    }
  },
  {
    path: '/transaction/transfer',
    name: 'TransferTransaction',
    component: () => import('@/views/Transactions/Transfer.vue'),
    meta: {
      title: '账户转移',
      requiresAuth: true
    }
  },
  {
    path: '/transaction/detail/:id',
    name: 'TransactionDetail',
    component: () => import('@/views/Transactions/Detail.vue'),
    meta: {
      title: '交易详情',
      requiresAuth: true
    }
  },
  {
    path: '/transaction/edit/:id',
    name: 'EditTransaction',
    component: () => import('@/views/Transactions/Edit.vue'),
    meta: {
      title: '编辑交易',
      requiresAuth: true
    }
  },
  {
    path: '/account/add',
    name: 'AddAccount',
    component: () => import('@/views/Accounts/Add.vue'),
    meta: {
      title: '添加账户',
      requiresAuth: true
    }
  },
  {
    path: '/account/detail/:id',
    name: 'AccountDetail',
    component: () => import('@/views/Accounts/Detail.vue'),
    meta: {
      title: '账户详情',
      requiresAuth: true
    }
  },
  {
    path: '/account/edit/:id',
    name: 'EditAccount',
    component: () => import('@/views/Accounts/Edit.vue'),
    meta: {
      title: '编辑账户',
      requiresAuth: true
    }
  },

  // 投资账号路由
  {
    path: '/investments',
    name: 'Investments',
    component: () => import('@/views/Investments/index.vue'),
    meta: {
      title: '投资账号',
      requiresAuth: true
    }
  },

  // 预算管理路由
  {
    path: '/budgets/annual',
    name: 'AnnualBudget',
    component: () => import('@/views/Budgets/Annual.vue'),
    meta: {
      title: '年度预算详情',
      requiresAuth: true
    }
  },
  {
    path: '/budgets/list',
    name: 'BudgetsList',
    component: () => import('@/views/Budgets/List.vue'),
    meta: {
      title: '预算列表',
      requiresAuth: true
    }
  },
  {
    path: '/budget/add',
    name: 'AddBudget',
    component: () => import('@/views/Budgets/Add.vue'),
    meta: {
      title: '添加预算',
      requiresAuth: true
    }
  },
  {
    path: '/budget/detail/:id',
    name: 'BudgetDetail',
    component: () => import('@/views/Budgets/Detail.vue'),
    meta: {
      title: '预算详情',
      requiresAuth: true
    }
  },
  {
    path: '/budget/edit/:id',
    name: 'EditBudget',
    component: () => import('@/views/Budgets/Edit.vue'),
    meta: {
      title: '编辑预算',
      requiresAuth: true
    }
  },
  {
    path: '/statistics',
    name: 'Statistics',
    component: () => import('@/views/Statistics/index.vue'),
    meta: {
      title: '数据统计',
      requiresAuth: true
    }
  },
  {
    path: '/statistics/category',
    name: 'CategoryDetail',
    component: () => import('@/views/Statistics/CategoryDetail.vue'),
    meta: {
      title: '分类统计',
      requiresAuth: true
    }
  },
  {
    path: '/statistics/report',
    name: 'StatisticsReport',
    component: () => import('@/views/Statistics/Report.vue'),
    meta: {
      title: '财务报告',
      requiresAuth: true
    }
  },
  {
    path: '/subscriptions',
    name: 'Subscriptions',
    component: () => import('@/views/Subscriptions/index.vue'),
    meta: {
      title: '我的订阅',
      requiresAuth: true
    }
  },
  {
    path: '/subscription/add',
    name: 'AddSubscription',
    component: () => import('@/views/Subscriptions/Add.vue'),
    meta: {
      title: '添加订阅',
      requiresAuth: true
    }
  },
  {
    path: '/subscription/detail/:id',
    name: 'SubscriptionDetail',
    component: () => import('@/views/Subscriptions/Detail.vue'),
    meta: {
      title: '订阅详情',
      requiresAuth: true
    }
  },
  {
    path: '/subscription/edit/:id',
    name: 'EditSubscription',
    component: () => import('@/views/Subscriptions/Edit.vue'),
    meta: {
      title: '编辑订阅',
      requiresAuth: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/Error/404.vue'),
    meta: {
      title: '页面不存在'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 钱管家`
  }
  
  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    // 如果还没有检查过登录状态，先检查
    if (!authStore.isLoggedIn) {
      const isAuthenticated = await authStore.checkAuth()
      if (!isAuthenticated) {
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
        return
      }
    }
  }
  
  // 如果已登录用户访问登录/注册页，重定向到首页
  if ((to.name === 'Login' || to.name === 'Register') && authStore.isLoggedIn) {
    next('/')
    return
  }
  
  next()
})

export default router
