import{a as g,l as $,h as G,m as H,s as x,c,e as i,d as t,w as d,f as r,p as b,v as A,q as D,t as l,n as m,g as J,o,F as K,k as O,i as P}from"./index--MNqwREY.js";import{useAccountsStore as Q}from"./accounts-bk1VVrk3.js";import{u as U}from"./transactions-F5TqZ8Qm.js";import{f as v}from"./format-wz8GKlWC.js";import{_ as X}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./transactions-CrdnwW_k.js";const Y={class:"account-detail-page"},Z={class:"page-container"},ee={key:0,class:"loading-container"},te={key:1},ae={class:"account-info-card"},ne={class:"account-header"},se={class:"account-details"},oe={class:"balance-info"},le={class:"balance-item"},ce={class:"amount current"},ie={class:"balance-item"},re={class:"amount"},ue={class:"stats-row"},de={class:"stat-item"},ve={class:"value income"},pe={class:"stat-item"},me={class:"value expense"},_e={key:0,class:"loading-container small"},ye={key:1,class:"empty-transactions"},fe={key:2,class:"error-container"},ke={__name:"Detail",setup(he){const _=J(),z=G(),y=Q(),S=U(),f=g(!1),k=g(!1),u=g([]),p=$(()=>z.params.id),n=$(()=>y.accounts.find(a=>a.id===p.value));H(async()=>{await B(),await N()});const B=async()=>{f.value=!0;try{y.accounts.length===0&&await y.fetchAccountsWithBalances(),n.value||x({message:"账户不存在",type:"fail"})}catch(a){console.error("加载账户详情失败:",a),x({message:"加载账户详情失败",type:"fail"})}finally{f.value=!1}},N=async()=>{if(p.value){k.value=!0;try{const a=await S.fetchTransactions({account:p.value,limit:5});u.value=a||[]}catch(a){console.error("加载交易记录失败:",a),u.value=[]}finally{k.value=!1}}},L=a=>({bank:"银行账户",cash:"现金",credit:"信用卡",investment:"投资账户",ewallet:"电子钱包",other:"其他"})[a]||a,V=a=>({bank:"🏦",cash:"💵",credit:"💳",investment:"📈",ewallet:"🏪",other:"💰"})[a]||"💰",I=a=>new Date(a).toLocaleDateString("zh-CN",{month:"short",day:"numeric"}),M=a=>{const e=v(a.amount,a.currency);return a.type==="income"?`+${e}`:`-${e}`},R=()=>{n.value&&_.push(`/account/edit/${n.value.id}`)},q=a=>{_.push(`/transaction/detail/${a.id}`)},F=()=>{_.push({path:"/transactions",query:{account:p.value}})};return(a,e)=>{var T;const E=r("van-icon"),W=r("van-nav-bar"),w=r("van-loading"),h=r("van-cell"),C=r("van-cell-group"),j=r("van-button");return o(),c("div",Y,[i(W,{title:((T=n.value)==null?void 0:T.name)||"账户详情","left-arrow":"",onClickLeft:e[0]||(e[0]=s=>a.$router.back()),fixed:"",placeholder:""},{right:d(()=>[i(E,{name:"edit",size:"18",onClick:R})]),_:1},8,["title"]),t("div",Z,[f.value?(o(),c("div",ee,[i(w,{size:"24px"}),e[2]||(e[2]=t("span",null,"加载中...",-1))])):n.value?(o(),c("div",te,[t("div",ae,[t("div",ne,[t("div",{class:D(["account-icon",n.value.type])},l(V(n.value.type)),3),t("div",se,[t("h3",null,l(n.value.name),1),t("p",null,l(L(n.value.type))+" • "+l(n.value.currency),1)])]),t("div",oe,[t("div",le,[e[3]||(e[3]=t("span",{class:"label"},"当前余额",-1)),t("span",ce,l(m(v)(n.value.current_balance,n.value.currency)),1)]),t("div",ie,[e[4]||(e[4]=t("span",{class:"label"},"初始余额",-1)),t("span",re,l(m(v)(n.value.initial_balance,n.value.currency)),1)])]),t("div",ue,[t("div",de,[t("span",ve,"+"+l(m(v)(n.value.income||0,n.value.currency)),1),e[5]||(e[5]=t("span",{class:"label"},"总收入",-1))]),t("div",pe,[t("span",me,"-"+l(m(v)(n.value.expense||0,n.value.currency)),1),e[6]||(e[6]=t("span",{class:"label"},"总支出",-1))])])]),n.value.description?(o(),b(C,{key:0,inset:"",title:"账户描述"},{default:d(()=>[i(h,{value:n.value.description},null,8,["value"])]),_:1})):A("",!0),i(C,{inset:"",title:"最近交易"},{default:d(()=>[k.value?(o(),c("div",_e,[i(w,{size:"16px"}),e[7]||(e[7]=t("span",null,"加载交易记录...",-1))])):u.value.length===0?(o(),c("div",ye,e[8]||(e[8]=[t("p",null,"暂无交易记录",-1)]))):(o(!0),c(K,{key:2},O(u.value,s=>(o(),b(h,{key:s.id,title:s.description||s.category,label:I(s.date),value:M(s),"is-link":"",onClick:ge=>q(s)},{icon:d(()=>[t("div",{class:D(["transaction-icon",s.type])},l(s.type==="income"?"💰":"💸"),3)]),_:2},1032,["title","label","value","onClick"]))),128)),u.value.length>0?(o(),b(h,{key:3,title:"查看全部交易","is-link":"",onClick:F})):A("",!0)]),_:1})])):(o(),c("div",fe,[e[10]||(e[10]=t("div",{class:"error-icon"},"❌",-1)),e[11]||(e[11]=t("p",null,"账户不存在",-1)),i(j,{type:"primary",size:"small",onClick:e[1]||(e[1]=s=>a.$router.back())},{default:d(()=>e[9]||(e[9]=[P(" 返回 ")])),_:1,__:[9]})]))])])}}},Ae=X(ke,[["__scopeId","data-v-49cae33a"]]);export{Ae as default};
