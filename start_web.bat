@echo off
chcp 65001 > nul 2>&1
echo ========================================
echo Starting Personal Finance System - Web Version
echo ========================================

cd /d "%~dp0"

:: Check if virtual environment exists
if not exist "venv\Scripts\activate.bat" (
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo Error: Cannot create virtual environment, please ensure Python is installed
        pause
        exit /b 1
    )
)

:: Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

:: Install dependencies
echo Installing dependencies...
pip install -r requirements.txt

:: Start Web application
echo Starting Web server...
cd web
python app.py

pause
