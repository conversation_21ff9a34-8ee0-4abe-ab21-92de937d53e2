const s=["储蓄","固定","流动","债务"],o=["工资","奖金","投资收益","兼职","礼金","退款","利息","租金收入","其他收入"],c=["入金","收益"],n=["出金"],a=["储蓄","固定","流动","债务"],e=t=>t.map(r=>({text:r,value:r})),i=(t="expense",r="")=>{if(r==="KDI")switch(t){case"income":return e(c);case"expense":return e(n);default:return e(n)}switch(t){case"income":return e(o);case"expense":return e(s);case"budget":return e(a);default:return e(s)}};export{i as g};
