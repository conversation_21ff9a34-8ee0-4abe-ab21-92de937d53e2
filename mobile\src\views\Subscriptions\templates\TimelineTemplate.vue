<template>
  <div class="timeline-template">
    <div class="timeline-container">
      <div
        v-for="(group, index) in groupedSubscriptions"
        :key="group.date"
        class="timeline-group"
      >
        <div class="timeline-date">
          <div class="date-indicator">
            <span class="date-text">{{ group.dateLabel }}</span>
            <span class="date-count">{{ group.subscriptions.length }}个</span>
          </div>
        </div>
        
        <div class="timeline-items">
          <div
            v-for="subscription in group.subscriptions"
            :key="subscription.id"
            class="timeline-item"
            @click="$emit('click-subscription', subscription.id)"
          >
            <div class="timeline-dot" :class="getStatusClass(subscription)"></div>
            <div class="timeline-content">
              <div class="item-header">
                <div class="service-info">
                  <ServiceLogo :service-name="subscription.name" size="small" />
                  <div class="service-details">
                    <h4 class="service-name">{{ subscription.name }}</h4>
                    <p class="service-type">{{ getServiceTypeLabel(subscription.service_type) }}</p>
                  </div>
                </div>
                <div class="price-info">
                  <span class="amount">${{ subscription.amount }}</span>
                  <span class="cycle">{{ getCycleLabel(subscription.billing_cycle) }}</span>
                </div>
              </div>
              
              <div class="item-footer">
                <span class="billing-info">
                  {{ subscription.is_due_soon ?
                    `还有${subscription.days_until_billing}天到期` :
                    `下次扣费: ${formatDate(subscription.next_billing_date)}`
                  }}
                </span>
                <span class="status-text" :class="getStatusClass(subscription)">
                  {{ getStatusText(subscription) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import ServiceLogo from '@/components/ServiceLogo.vue'

const props = defineProps({
  subscriptions: {
    type: Array,
    required: true
  }
})

defineEmits(['click-subscription'])

// 按到期时间分组订阅
const groupedSubscriptions = computed(() => {
  const groups = {}
  
  props.subscriptions.forEach(subscription => {
    let groupKey, groupLabel
    
    if (subscription.is_due_soon) {
      const days = subscription.days_until_billing
      if (days <= 3) {
        groupKey = 'urgent'
        groupLabel = '紧急到期'
      } else if (days <= 7) {
        groupKey = 'soon'
        groupLabel = '即将到期'
      } else {
        groupKey = 'upcoming'
        groupLabel = '近期到期'
      }
    } else {
      const date = new Date(subscription.next_billing_date)
      const month = date.getMonth() + 1
      groupKey = `month-${month}`
      groupLabel = `${month}月到期`
    }
    
    if (!groups[groupKey]) {
      groups[groupKey] = {
        date: groupKey,
        dateLabel: groupLabel,
        subscriptions: []
      }
    }
    
    groups[groupKey].subscriptions.push(subscription)
  })
  
  // 排序：紧急 -> 即将 -> 近期 -> 按月份
  const sortOrder = ['urgent', 'soon', 'upcoming']
  return Object.values(groups).sort((a, b) => {
    const aIndex = sortOrder.indexOf(a.date)
    const bIndex = sortOrder.indexOf(b.date)
    
    if (aIndex !== -1 && bIndex !== -1) {
      return aIndex - bIndex
    } else if (aIndex !== -1) {
      return -1
    } else if (bIndex !== -1) {
      return 1
    } else {
      return a.date.localeCompare(b.date)
    }
  })
})

// 获取服务类型标签
const getServiceTypeLabel = (serviceType) => {
  const typeLabels = {
    'streaming': '流媒体',
    'music': '音乐',
    'cloud': '云服务',
    'productivity': '生产力',
    'development': '开发工具',
    'design': '设计工具',
    'fitness': '健身',
    'education': '教育',
    'news': '新闻',
    'gaming': '游戏',
    'ai': 'AI工具',
    'other': '其他'
  }
  return typeLabels[serviceType] || typeLabels.other
}

// 获取状态样式类
const getStatusClass = (subscription) => {
  if (subscription.is_due_soon) {
    return 'status-warning'
  } else if (!subscription.is_active) {
    return 'status-inactive'
  } else {
    return 'status-active'
  }
}

// 获取状态文本
const getStatusText = (subscription) => {
  if (subscription.is_due_soon) {
    return '即将到期'
  } else if (!subscription.is_active) {
    return '已暂停'
  } else {
    return '正常'
  }
}

// 获取周期标签
const getCycleLabel = (cycle) => {
  const cycleMap = {
    'weekly': '周',
    'monthly': '月',
    'quarterly': '季',
    'yearly': '年'
  }
  return cycleMap[cycle] || '月'
}

// 格式化日期
const formatDate = (dateString) => {
  try {
    const date = new Date(dateString)
    return `${date.getMonth() + 1}月${date.getDate()}日`
  } catch {
    return '未知'
  }
}
</script>

<style scoped>
.timeline-template {
  width: 100%;
}

.timeline-container {
  position: relative;
}

.timeline-group {
  margin-bottom: 24px;
}

.timeline-date {
  margin-bottom: 16px;
}

.date-indicator {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 20px;
  border: 1px solid rgba(212, 175, 55, 0.3);
}

.date-text {
  font-size: 14px;
  font-weight: 600;
  color: #ffd700;
}

.date-count {
  font-size: 12px;
  color: #e6d7b7;
  opacity: 0.8;
}

.timeline-items {
  position: relative;
  padding-left: 20px;
}

.timeline-items::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, rgba(212, 175, 55, 0.3) 0%, rgba(212, 175, 55, 0.1) 100%);
}

.timeline-item {
  position: relative;
  margin-bottom: 16px;
  cursor: pointer;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: -12px;
  top: 12px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 2px solid #1a1a1a;
}

.status-active {
  background: #00d4aa;
}

.status-warning {
  background: #ffc107;
}

.status-inactive {
  background: #6c757d;
}

.timeline-content {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(212, 175, 55, 0.2);
  transition: all 0.3s ease;
}

.timeline-item:active .timeline-content {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.service-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.service-details {
  margin-left: 8px;
}

.service-name {
  margin: 0 0 2px 0;
  font-size: 14px;
  font-weight: 600;
  color: #ffd700;
}

.service-type {
  margin: 0;
  font-size: 11px;
  color: #e6d7b7;
  opacity: 0.7;
}

.price-info {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.amount {
  font-size: 16px;
  font-weight: 700;
  color: #ffd700;
}

.cycle {
  font-size: 10px;
  color: #e6d7b7;
  opacity: 0.8;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.billing-info {
  font-size: 11px;
  color: #e6d7b7;
  opacity: 0.7;
}

.status-text {
  font-size: 10px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
}

.status-text.status-active {
  background: rgba(0, 212, 170, 0.2);
  color: #00d4aa;
}

.status-text.status-warning {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
}

.status-text.status-inactive {
  background: rgba(108, 117, 125, 0.2);
  color: #6c757d;
}
</style>
