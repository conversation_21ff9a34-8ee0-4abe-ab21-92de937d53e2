import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// 获取API目标地址，支持环境变量配置
const getApiTarget = () => {
  // 优先使用环境变量
  if (process.env.VITE_API_BASE_URL) {
    return process.env.VITE_API_BASE_URL
  }

  // 服务器环境检测
  const isServerEnv = process.env.NODE_ENV === 'production' ||
                      process.env.SERVER_ENV === 'true' ||
                      process.platform === 'linux'

  // 根据环境选择合适的地址
  if (isServerEnv) {
    return 'http://127.0.0.1:8000'
  } else {
    return 'http://localhost:8000'
  }
}

const apiTarget = getApiTarget()
console.log('API代理目标:', apiTarget)

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    host: '0.0.0.0',
    port: 3000,
    // 添加允许的主机
    allowedHosts: ['mrtan.pro', 'www.mrtan.pro'],
    proxy: {
      '/api': {
        target: apiTarget,
        changeOrigin: true,
        secure: false,
        timeout: 10000,
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.error('代理错误:', err.message);
            console.error('目标地址:', apiTarget);
            console.error('请求URL:', req.url);
          });
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('代理请求:', req.method, req.url, '->', apiTarget + req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('代理响应:', proxyRes.statusCode, req.url);
          });
        }
      }
    }
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets'
  }
})

