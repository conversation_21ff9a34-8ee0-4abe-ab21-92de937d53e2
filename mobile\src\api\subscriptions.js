import api from './index'

export const subscriptionsAPI = {
  // 获取订阅列表
  getSubscriptions() {
    return api.get('/subscriptions/')
  },

  // 获取单个订阅
  getSubscription(id) {
    return api.get(`/subscriptions/${id}`)
  },

  // 创建订阅
  createSubscription(data) {
    return api.post('/subscriptions/', data)
  },

  // 更新订阅
  updateSubscription(id, data) {
    return api.put(`/subscriptions/${id}`, data)
  },

  // 删除订阅
  deleteSubscription(id) {
    return api.delete(`/subscriptions/${id}`)
  },

  // 获取订阅统计
  getStats() {
    return api.get('/subscriptions/stats')
  },

  // 获取即将到期的订阅
  getDueSoon(days = 7) {
    return api.get('/subscriptions/due-soon', { params: { days } })
  },

  // 续费订阅
  renewSubscription(id) {
    return api.post(`/subscriptions/${id}/renew`)
  }
}
