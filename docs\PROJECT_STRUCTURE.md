# 个人财务管理系统 - 项目结构

## 📁 项目概览

本项目包含三个独立的应用版本，共享同一个数据库：

```
Project_3/
├── web/                    # Web版本 (Flask + HTML/CSS/JS)
├── mobile/                 # 移动版本 (Vue3 + Vant)
├── bot/                    # Telegram机器人
├── docs/                   # 项目文档
├── finance.db             # 共享SQLite数据库
├── requirements.txt       # 统一Python依赖 (Web + Bot)
├── venv/                  # 共享Python虚拟环境
└── 启动脚本
```

## 🌐 Web版本 (`web/`)

**技术栈**: Flask + HTML/CSS/JavaScript
**端口**: 8000
**启动**: `start_web.bat` 或 `python web/app.py`

```
web/
├── app.py                 # Flask主应用
├── requirements.txt       # Python依赖
├── blueprints/           # Flask蓝图（API路由）
│   ├── auth.py           # 用户认证
│   ├── transactions.py   # 交易管理
│   ├── accounts.py       # 账户管理
│   ├── budgets.py        # 预算管理
│   ├── goals.py          # 目标管理
│   └── subscriptions.py  # 订阅管理
├── templates/            # HTML模板
│   ├── index.html        # 主页
│   └── auth/            # 认证页面
├── static/              # 静态资源
│   ├── css/             # 样式文件
│   ├── js/              # JavaScript文件
│   └── images/          # 图片资源
├── logs/                # 日志文件
└── sessions/            # 会话文件
```

## 📱 移动版本 (`mobile/`)

**技术栈**: Vue3 + Vant + Pinia
**端口**: 3000
**启动**: `start_mobile.bat` 或 `npm run dev`

```
mobile/
├── package.json          # Node.js依赖配置
├── vite.config.js        # Vite构建配置
├── index.html           # 入口HTML
└── src/                 # 源代码
    ├── main.js          # 应用入口
    ├── App.vue          # 根组件
    ├── router/          # 路由配置
    ├── stores/          # Pinia状态管理
    ├── views/           # 页面组件
    ├── components/      # 通用组件
    └── assets/          # 静态资源
```

## 🤖 Telegram机器人 (`bot/`)

**技术栈**: Python + python-telegram-bot
**启动**: `start_bot.bat` 或 `python bot/bot.py`

```
bot/
├── bot.py               # 机器人主程序
├── database.py          # Bot专用数据库
├── website_auth.py      # 网站用户验证
├── bot.db              # Bot专用数据库文件
├── config/             # 配置文件
├── handlers/           # 消息处理器
└── utils/              # 工具函数
```

## 🗄️ 数据库设计

### 共享数据库 (`finance.db`)
- `users` - 用户表
- `transactions` - 交易记录
- `accounts` - 账户信息
- `budgets` - 预算设置
- `goals` - 理财目标
- `subscriptions` - 订阅管理

### Bot专用数据库 (`bot/bot.db`)
- `bot_users` - Telegram用户
- `message_logs` - 消息日志
- `notification_settings` - 通知设置

## 🚀 启动方式

### 单独启动
- **Web版本**: `start_web.bat`
- **移动版本**: `start_mobile.bat`
- **机器人**: `start_bot.bat`

### 统一启动
- **启动菜单**: `start_all.bat`

## 🔧 开发环境要求

- **Python 3.8+** (Web版本 + 机器人)
- **Node.js 16+** (移动版本)
- **SQLite** (数据库)

## 📝 重要说明

### 🔧 虚拟环境策略
- **Web版本和Bot共享一个虚拟环境** (`venv/`)
- **统一依赖管理** (`requirements.txt`)
- **原因**: 依赖无冲突，简化管理

### 🗄️ 数据库策略
- **唯一数据源**: 根目录的 `finance.db`
- **三个版本共享**: Web、Mobile、Bot都使用同一个数据库
- **Bot专用数据**: `bot/bot.db` (用户绑定、消息日志等)

### ⚠️ 注意事项
1. Web版本在端口8000，移动版本在端口3000
2. 机器人需要配置Telegram Bot Token
3. 首次运行会自动创建数据库和必要的目录
4. 所有Python组件使用同一个虚拟环境
