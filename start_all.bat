@echo off
chcp 65001 > nul 2>&1
echo ========================================
echo Personal Finance System - Startup Menu
echo ========================================
echo.
echo Please select service to start:
echo.
echo 1. Web Version (Port 8000)
echo 2. Mobile Version (Port 3000)
echo 3. Telegram Bot
echo 4. Start All Services
echo 5. Exit
echo.
set /p choice=Please enter choice (1-5):

if "%choice%"=="1" (
    echo Starting Web version...
    call start_web.bat
) else if "%choice%"=="2" (
    echo Starting Mobile version...
    call start_mobile.bat
) else if "%choice%"=="3" (
    echo Starting Telegram bot...
    call start_bot.bat
) else if "%choice%"=="4" (
    echo Starting all services...
    start "Web Server" cmd /c start_web.bat
    timeout /t 2 /nobreak > nul
    start "Mobile Server" cmd /c start_mobile.bat
    timeout /t 2 /nobreak > nul
    start "Telegram Bot" cmd /c start_bot.bat
    echo All services started!
    pause
) else if "%choice%"=="5" (
    echo Exiting...
    exit /b 0
) else (
    echo Invalid choice, please run script again
    pause
    goto :eof
)

pause
