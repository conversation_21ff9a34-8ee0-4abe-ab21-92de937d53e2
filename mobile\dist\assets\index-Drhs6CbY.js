import{u as vt,a as C,l as P,r as mt,m as pt,c as f,e as d,d as e,w as g,f as b,t as y,n as w,p as x,q as D,g as gt,o as r,i as z,F as V,k as H,v as yt}from"./index--MNqwREY.js";import{u as ht}from"./transactions-F5TqZ8Qm.js";import{useBudgetsStore as ft}from"./budgets-DJh-v5Y8.js";import{B as _t}from"./BaseChart-BEnmQZd1.js";import{f as $}from"./format-wz8GKlWC.js";import{_ as xt}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./transactions-CrdnwW_k.js";const kt={class:"dashboard"},Ct={class:"nav-actions"},bt={class:"page-container"},wt={class:"welcome-card"},St={class:"welcome-header"},$t={class:"greeting-section"},Mt={class:"date-info"},Dt={class:"welcome-avatar"},zt={class:"avatar-circle"},Et={class:"quick-stats"},Yt={class:"stat-item"},Bt={class:"stat-content"},Ft={class:"stat-value"},Rt={key:1},Tt={class:"stat-item"},It={class:"stat-content"},Nt={key:1},Wt={class:"stats-grid"},Lt={class:"stat-card income"},Pt={class:"stat-content"},Ot={class:"stat-value"},Ut={key:1},jt={class:"stat-card expense"},qt={class:"stat-content"},At={class:"stat-value"},Gt={key:1},Jt={class:"stat-card net-worth"},Vt={class:"stat-content"},Ht={class:"stat-value"},Kt={key:1},Qt={class:"quick-actions"},Xt={class:"action-grid"},Zt={class:"chart-section"},te={class:"chart-container"},ee={class:"chart-header"},se={class:"chart-content"},ne={key:1,class:"chart-loading"},oe={class:"recent-transactions"},ae={class:"section-header"},le={key:0,class:"loading-container"},ie={key:1,class:"empty-container"},re={class:"search-container"},ce={class:"currency-picker"},de={class:"picker-header"},ue={__name:"index",setup(ve){const O=gt(),K=vt(),E=ht(),Y=ft(),F=C(!1),h=C(!1),B=C(!1),Q=C(!1),M=C(!1),k=C(localStorage.getItem("dashboard-currency")||"MYR"),X=C([{code:"MYR",name:"马来西亚林吉特",symbol:"RM"},{code:"USD",name:"美元",symbol:"$"},{code:"CNY",name:"人民币",symbol:"¥"},{code:"EUR",name:"欧元",symbol:"€"},{code:"GBP",name:"英镑",symbol:"£"},{code:"JPY",name:"日元",symbol:"¥"},{code:"SGD",name:"新加坡元",symbol:"S$"}]),Z=P(()=>{const s=new Date,t={year:"numeric",month:"long",day:"numeric",weekday:"long"};return s.toLocaleDateString("zh-CN",t)}),p=P(()=>h.value?lt(k.value):{todayExpense:0,monthlyBudgetRemaining:0,totalIncome:0,totalExpense:0,netWorth:0,incomeChange:0,expenseChange:0,netWorthChange:0}),U=P(()=>E.recentTransactions),R=mt({incomeExpense:null});pt(async()=>{await tt()});const tt=async()=>{F.value=!0,h.value=!1;try{await Promise.all([E.fetchTransactions(),Y.fetchBudgets()]),et(),h.value=!0}catch(s){console.error("加载数据失败:",s)}finally{F.value=!1}},et=()=>{console.log("仪表板: 开始生成图表数据...");const s=st();console.log("仪表板: 月度数据:",s),R.incomeExpense={title:{show:!1},tooltip:{trigger:"axis",formatter:function(t){let a=t[0].name+"<br/>";return t.forEach(c=>{a+=`${c.seriesName}: RM ${c.value.toFixed(2)}<br/>`}),a}},legend:{data:["收入","支出"],bottom:0},grid:{top:20,left:40,right:20,bottom:40},xAxis:{type:"category",data:s.months,axisLabel:{fontSize:12}},yAxis:{type:"value",axisLabel:{fontSize:12,formatter:"RM {value}"}},series:[{name:"收入",type:"line",data:s.income,itemStyle:{color:"#07c160"},lineStyle:{color:"#07c160"},smooth:!0},{name:"支出",type:"line",data:s.expense,itemStyle:{color:"#ee0a24"},lineStyle:{color:"#ee0a24"},smooth:!0}]}},st=()=>{const s=E.transactions,t=new Date,a={};for(let l=5;l>=0;l--){const m=new Date(t.getFullYear(),t.getMonth()-l,1),u=`${m.getFullYear()}-${String(m.getMonth()+1).padStart(2,"0")}`,S=`${m.getMonth()+1}月`;a[u]={label:S,income:0,expense:0}}s.forEach(l=>{const m=new Date(l.date),u=`${m.getFullYear()}-${String(m.getMonth()+1).padStart(2,"0")}`;a[u]&&(l.type==="income"?a[u].income+=l.amount:l.type==="expense"&&(a[u].expense+=l.amount))});const c=Object.values(a).map(l=>l.label),_=Object.values(a).map(l=>l.income),v=Object.values(a).map(l=>l.expense);return{months:c,income:_,expense:v}},nt=s=>{O.push(`/transaction/edit/${s.id}`)},ot=()=>{O.push("/statistics")},T=s=>s===0?"0%":`${s>0?"+":""}${s}%`,I=(s,t=!1)=>s===0?"neutral":t?s>0?"negative":"positive":s>0?"positive":"negative",at=s=>{k.value=s,localStorage.setItem("dashboard-currency",s),M.value=!1},lt=s=>{const t=E.transactions.filter(n=>n.currency===s),a=new Date,c=a.getMonth(),_=a.getFullYear(),v=new Date(a.getFullYear(),a.getMonth(),a.getDate()),l=t.filter(n=>{const i=new Date(n.date);return new Date(i.getFullYear(),i.getMonth(),i.getDate()).getTime()===v.getTime()}),m=t.filter(n=>{const i=new Date(n.date);return i.getMonth()===c&&i.getFullYear()===_}),u=c===0?11:c-1,S=c===0?_-1:_,o=t.filter(n=>{const i=new Date(n.date);return i.getMonth()===u&&i.getFullYear()===S}),W=l.filter(n=>n.type==="expense").reduce((n,i)=>n+i.amount,0),j=t.filter(n=>n.type==="income").reduce((n,i)=>n+i.amount,0),q=t.filter(n=>n.type==="expense").reduce((n,i)=>n+i.amount,0),A=m.filter(n=>n.type==="income").reduce((n,i)=>n+i.amount,0),L=m.filter(n=>n.type==="expense").reduce((n,i)=>n+i.amount,0),G=o.filter(n=>n.type==="income").reduce((n,i)=>n+i.amount,0),J=o.filter(n=>n.type==="expense").reduce((n,i)=>n+i.amount,0),rt=N(G,A),ct=N(J,L),dt=N(G-J,A-L),ut=it(s,L);return{todayExpense:W,monthlyBudgetRemaining:ut,totalIncome:j,totalExpense:q,netWorth:j-q,incomeChange:rt,expenseChange:ct,netWorthChange:dt}},N=(s,t)=>s===0?t>0?100:0:Math.round((t-s)/s*100),it=(s,t)=>{try{const a=new Date,c=a.getFullYear(),_=a.getMonth()+1,v=`${c}-${String(_).padStart(2,"0")}-${String(a.getDate()).padStart(2,"0")}`;let l=0;return Y.budgets&&Y.budgets.length>0&&Y.budgets.forEach(u=>{u.currency===s&&u.start_date<=v&&u.end_date>=v&&(l+=u.amount||0)}),l-t}catch(a){return console.error("计算本月预算剩余失败:",a),0}};return(s,t)=>{var S;const a=b("van-button"),c=b("van-icon"),_=b("van-nav-bar"),v=b("van-loading"),l=b("van-cell"),m=b("van-cell-group"),u=b("van-popup");return r(),f("div",kt,[d(_,{title:"仪表盘",fixed:"",placeholder:"","safe-area-inset-top":""},{right:g(()=>[e("div",Ct,[d(a,{size:"mini",type:"primary",plain:"",onClick:t[0]||(t[0]=o=>M.value=!0),class:"currency-btn"},{default:g(()=>[z(y(k.value),1)]),_:1}),d(c,{name:"search",size:"18",onClick:t[1]||(t[1]=o=>B.value=!0)})])]),_:1}),e("div",bt,[e("div",wt,[e("div",St,[e("div",$t,[e("h3",null,"你好，"+y(((S=w(K).user)==null?void 0:S.display_name)||"test"),1),e("p",Mt,y(Z.value),1)]),e("div",Dt,[e("div",zt,[d(c,{name:"user-o",size:"24"})]),t[12]||(t[12]=e("div",{class:"status-indicator"},null,-1))])]),e("div",Et,[e("div",Yt,[e("div",Bt,[t[13]||(t[13]=e("span",{class:"stat-label"},"今日支出",-1)),e("span",Ft,[h.value?(r(),f("span",Rt,y(w($)(p.value.todayExpense||0,k.value)),1)):(r(),x(v,{key:0,size:"16px",color:"#1e40af"}))])]),t[14]||(t[14]=e("div",{class:"stat-icon expense"}," 💸 ",-1))]),e("div",Tt,[e("div",It,[t[15]||(t[15]=e("span",{class:"stat-label"},"本月预算剩余",-1)),e("span",{class:D(["stat-value",{"budget-exceeded":p.value.monthlyBudgetRemaining<0}])},[h.value?(r(),f("span",Nt,y(w($)(p.value.monthlyBudgetRemaining||0,k.value)),1)):(r(),x(v,{key:0,size:"16px",color:"#1e40af"}))],2)]),t[16]||(t[16]=e("div",{class:"stat-icon budget"}," 📊 ",-1))])])]),e("div",Wt,[e("div",Lt,[t[18]||(t[18]=e("div",{class:"stat-icon"},"💰",-1)),e("div",Pt,[t[17]||(t[17]=e("div",{class:"stat-label"},"总收入",-1)),e("div",Ot,[h.value?(r(),f("span",Ut,y(w($)(p.value.totalIncome,k.value)),1)):(r(),x(v,{key:0,size:"16px"}))]),e("div",{class:D(["stat-change",I(p.value.incomeChange)])},y(h.value?T(p.value.incomeChange):"--"),3)])]),e("div",jt,[t[20]||(t[20]=e("div",{class:"stat-icon"},"💸",-1)),e("div",qt,[t[19]||(t[19]=e("div",{class:"stat-label"},"总支出",-1)),e("div",At,[h.value?(r(),f("span",Gt,y(w($)(p.value.totalExpense,k.value)),1)):(r(),x(v,{key:0,size:"16px"}))]),e("div",{class:D(["stat-change",I(p.value.expenseChange,!0)])},y(h.value?T(p.value.expenseChange):"--"),3)])]),e("div",Jt,[t[22]||(t[22]=e("div",{class:"stat-icon"},"📊",-1)),e("div",Vt,[t[21]||(t[21]=e("div",{class:"stat-label"},"净资产",-1)),e("div",Ht,[h.value?(r(),f("span",Kt,y(w($)(p.value.netWorth,k.value)),1)):(r(),x(v,{key:0,size:"16px"}))]),e("div",{class:D(["stat-change",I(p.value.netWorthChange)])},y(h.value?T(p.value.netWorthChange):"--"),3)])])]),e("div",Qt,[t[27]||(t[27]=e("h4",null,"快速操作",-1)),e("div",Xt,[e("div",{class:"action-item",onClick:t[2]||(t[2]=o=>s.$router.push("/transaction/add"))},[d(c,{name:"plus",size:"20"}),t[23]||(t[23]=e("span",null,"记账",-1))]),e("div",{class:"action-item",onClick:t[3]||(t[3]=o=>s.$router.push("/account/add"))},[d(c,{name:"credit-pay",size:"20"}),t[24]||(t[24]=e("span",null,"添加账户",-1))]),e("div",{class:"action-item",onClick:ot},[d(c,{name:"chart-trending-o",size:"20"}),t[25]||(t[25]=e("span",null,"统计",-1))]),e("div",{class:"action-item",onClick:t[4]||(t[4]=o=>s.$router.push("/budgets"))},[d(c,{name:"bag-o",size:"20"}),t[26]||(t[26]=e("span",null,"预算",-1))])])]),e("div",Zt,[e("div",te,[e("div",ee,[t[29]||(t[29]=e("h4",null,"收支趋势",-1)),d(a,{size:"mini",type:"primary",plain:"",onClick:t[5]||(t[5]=o=>Q.value=!0)},{default:g(()=>t[28]||(t[28]=[z(" 最近6个月 ")])),_:1,__:[28]})]),e("div",se,[R.incomeExpense?(r(),x(_t,{key:0,options:R.incomeExpense,height:"200px"},null,8,["options"])):(r(),f("div",ne,[d(v,{size:"24px"}),t[30]||(t[30]=e("span",null,"加载中...",-1))]))])])]),e("div",oe,[e("div",ae,[t[32]||(t[32]=e("h4",null,"最近交易",-1)),d(a,{size:"mini",type:"primary",plain:"",onClick:t[6]||(t[6]=o=>s.$router.push("/transactions"))},{default:g(()=>t[31]||(t[31]=[z(" 查看全部 ")])),_:1,__:[31]})]),F.value?(r(),f("div",le,[d(v,{size:"24px"}),t[33]||(t[33]=e("span",null,"加载中...",-1))])):U.value.length===0?(r(),f("div",ie,[t[35]||(t[35]=e("div",{class:"empty-icon"},"📝",-1)),t[36]||(t[36]=e("p",null,"暂无交易记录",-1)),d(a,{type:"primary",size:"small",onClick:t[7]||(t[7]=o=>s.$router.push("/transaction/add"))},{default:g(()=>t[34]||(t[34]=[z(" 立即记账 ")])),_:1,__:[34]})])):(r(),x(m,{key:2,inset:""},{default:g(()=>[(r(!0),f(V,null,H(U.value,o=>(r(),x(l,{key:o.id,title:o.description,label:o.date,value:w($)(o.amount,o.currency),"value-class":o.type==="income"?"amount income":"amount expense","is-link":"",onClick:W=>nt(o)},{icon:g(()=>[e("div",{class:D(["transaction-icon",o.type])},y(o.type==="income"?"💰":"💸"),3)]),_:2},1032,["title","label","value","value-class","onClick"]))),128))]),_:1}))])]),d(u,{show:B.value,"onUpdate:show":t[9]||(t[9]=o=>B.value=o),position:"top",style:{height:"100%"}},{default:g(()=>[e("div",re,[d(_,{title:"搜索交易","left-arrow":"",onClickLeft:t[8]||(t[8]=o=>B.value=!1)})])]),_:1},8,["show"]),d(u,{show:M.value,"onUpdate:show":t[11]||(t[11]=o=>M.value=o),position:"bottom",round:""},{default:g(()=>[e("div",ce,[e("div",de,[t[38]||(t[38]=e("h3",null,"选择货币",-1)),d(a,{size:"mini",type:"primary",onClick:t[10]||(t[10]=o=>M.value=!1)},{default:g(()=>t[37]||(t[37]=[z(" 确定 ")])),_:1,__:[37]})]),d(m,null,{default:g(()=>[(r(!0),f(V,null,H(X.value,o=>(r(),x(l,{key:o.code,title:o.name,label:o.code,value:o.symbol,clickable:"",onClick:W=>at(o.code)},{"right-icon":g(()=>[k.value===o.code?(r(),x(c,{key:0,name:"success",color:"#1989fa"})):yt("",!0)]),_:2},1032,["title","label","value","onClick"]))),128))]),_:1})])]),_:1},8,["show"])])}}},ke=xt(ue,[["__scopeId","data-v-d292ed4e"]]);export{ke as default};
