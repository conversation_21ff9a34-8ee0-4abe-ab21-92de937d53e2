# 📊 钱管家 - 个人财务管理系统

一个现代化的个人财务管理系统，支持**Web版本**、**移动版本**和**Telegram机器人**三个平台，让财务管理变得简单高效。

## 🚀 快速开始

### 🎯 一键启动
```bash
# 启动所有服务（推荐）
start_all.bat

# 或单独启动
start_web.bat      # Web版本 (端口 8000)
start_mobile.bat   # 移动版本 (端口 3000)
start_bot.bat      # Telegram机器人
```

### 📋 环境要求
- **Python 3.8+** (Web版本 + 机器人)
- **Node.js 16+** (移动版本)
- **SQLite** (数据库)

## ✨ 主要功能

### 💰 财务管理
- **收支记录**: 支持多种收入和支出类别
- **账户管理**: 银行账户、现金、信用卡等多账户支持
- **多货币支持**: MYR、CNY、USD、EUR等8种货币

### 📊 预算管理
- **预算设置**: 按类别设置月度预算
- **实时监控**: 预算使用情况实时跟踪
- **智能提醒**: 80%预警，100%危险提醒

### 🎯 财务目标
- **目标设置**: 设定储蓄和投资目标
- **进度跟踪**: 目标完成进度可视化

### 🤖 Telegram机器人
- **交互式记账**: 内联键盘操作，简单直观
- **账户选择**: 支持多账户记账
- **查询功能**: 余额查询、预算查询

## 🛠️ 技术栈

- **Web版本**: Python Flask + HTML/CSS/JS + Chart.js
- **移动版本**: Vue 3 + Vant + Pinia + ECharts
- **Telegram机器人**: Python + Telegram Bot API
- **数据库**: SQLite (三版本共享)

## 📁 项目结构

```
Project_3/
├── web/                    # Web版本
├── mobile/                 # 移动版本
├── bot/                    # Telegram机器人
├── data/                   # 数据文件
│   ├── finance.db         # 主数据库
│   └── bot.db             # Bot专用数据库
├── docs/                   # 项目文档
├── .env                    # 环境配置
├── requirements.txt        # Python依赖
└── 启动脚本
```

## 🎮 使用方式

- **Web版本**: `http://localhost:8000` - 完整的桌面端功能
- **移动版本**: `http://localhost:3000` - 移动端优化界面
- **Telegram机器人**: 发送 `/start` 开始使用

## 📚 文档

详细文档请查看 [docs/README.md](docs/README.md)

## 🌟 项目亮点

- ✅ **三平台统一** - 数据实时同步
- ✅ **现代化技术栈** - 最新前端技术
- ✅ **智能自适应** - 设备检测和端口切换
- ✅ **用户友好** - 多种交互方式

---

**钱管家** - 让财务管理变得简单高效！ 💰✨
