@echo off
chcp 65001 > nul 2>&1
echo ========================================
echo Starting Personal Finance System - Mobile Version
echo ========================================

cd /d "%~dp0"

:: Check if Node.js is installed
node --version > nul 2>&1
if errorlevel 1 (
    echo Error: Node.js not detected, please install Node.js first
    echo Download: https://nodejs.org/
    pause
    exit /b 1
)

:: Enter mobile directory
cd mobile
if errorlevel 1 (
    echo Error: Cannot find mobile directory
    pause
    exit /b 1
)

:: Check if dependencies are installed
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if errorlevel 1 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
)

:: Start mobile application
echo Starting mobile server...
npm run dev

pause
