/**
 * 服务图标配置
 * 统一管理所有服务的图标和信息
 */

// 服务图标映射配置
export const serviceIconConfig = {
  // AI工具
  'chatgpt_pro': {
    name: 'ChatGPT Pro',
    icon: 'chatgpt',
    category: 'ai',
    color: '#74aa9c'
  },
  'chatgpt_plus': {
    name: 'ChatGPT Plus',
    icon: 'chatgpt',
    category: 'ai',
    color: '#74aa9c'
  },
  'chatgpt_team': {
    name: 'ChatGPT Team',
    icon: 'chatgpt',
    category: 'ai',
    color: '#74aa9c'
  },
  // 兼容旧数据格式
  'chatgpt': {
    name: 'ChatGPT Plus',
    icon: 'chatgpt',
    category: 'ai',
    color: '#74aa9c'
  },
  'ai': {
    name: 'AI工具',
    icon: 'chatgpt',
    category: 'ai',
    color: '#74aa9c'
  },
  'augment_code': {
    name: 'Augment Code',
    icon: 'chatgpt',
    category: 'ai',
    color: '#74aa9c'
  },
  'cursor': {
    name: 'Cursor Pro',
    icon: 'chatgpt',
    category: 'ai',
    color: '#74aa9c'
  },

  // 云服务
  'icloud_plus': {
    name: 'iCloud+',
    icon: 'icloud',
    category: 'cloud',
    color: '#007AFF'
  },

  // 视频平台
  'youtube_premium': {
    name: 'YouTube Premium',
    icon: 'youtube',
    category: 'streaming',
    color: '#FF0000'
  },
  'youtube_music': {
    name: 'YouTube Music',
    icon: 'youtube',
    category: 'music',
    color: '#FF0000'
  }
}

// 服务类别配置
export const serviceCategoryConfig = {
  'ai': {
    label: 'AI工具',
    icon: '🤖',
    color: '#74aa9c'
  },
  'cloud': {
    label: '云服务',
    icon: '☁️',
    color: '#007AFF'
  },
  'streaming': {
    label: '视频平台',
    icon: '📺',
    color: '#FF0000'
  },
  'music': {
    label: '音乐',
    icon: '🎵',
    color: '#FF0000'
  },
  'other': {
    label: '其他',
    icon: '📱',
    color: '#6B7280'
  }
}

// 级联选择器配置
export const cascaderOptions = [
  {
    text: '🤖 ChatGPT',
    value: 'chatgpt',
    children: [
      { text: 'Pro', value: 'chatgpt_pro' },
      { text: 'Plus', value: 'chatgpt_plus' },
      { text: 'Team', value: 'chatgpt_team' }
    ]
  },
  {
    text: '☁️ iCloud',
    value: 'icloud',
    children: [
      { text: 'iCloud+', value: 'icloud_plus' }
    ]
  },
  {
    text: '📺 YouTube',
    value: 'youtube',
    children: [
      { text: 'Premium', value: 'youtube_premium' },
      { text: 'Music', value: 'youtube_music' }
    ]
  }
]

// 工具函数
export const getServiceConfig = (serviceType) => {
  return serviceIconConfig[serviceType] || {
    name: '未知服务',
    icon: 'default',
    category: 'other',
    color: '#6B7280'
  }
}

export const getCategoryConfig = (category) => {
  return serviceCategoryConfig[category] || serviceCategoryConfig.other
}

export const getServiceIcon = (serviceType) => {
  const config = getServiceConfig(serviceType)
  return config.icon
}

export const getServiceName = (serviceType) => {
  const config = getServiceConfig(serviceType)
  return config.name
}

export const getServiceCategory = (serviceType) => {
  const config = getServiceConfig(serviceType)
  return getCategoryConfig(config.category)
}
