<template>
  <div class="subscriptions-page">
    <van-nav-bar
      title="我的订阅"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="$router.back()"
    >
      <template #right>
        <div class="nav-actions">
          <van-button
            size="small"
            type="primary"
            plain
            @click="showTemplateSelector = true"
            class="template-btn"
          >
            模板
          </van-button>
          <van-button
            size="small"
            type="primary"
            plain
            @click="$router.push('/subscription/add')"
          >
            添加订阅
          </van-button>
        </div>
      </template>
    </van-nav-bar>

    <div class="page-container">
      <!-- 订阅统计卡片 -->
      <div class="stats-card">
        <div class="stats-header">
          <h3>订阅概览</h3>
        </div>

        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-icon">📱</div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.total_subscriptions }}</div>
              <div class="stat-label">总订阅数</div>
            </div>
          </div>

          <div class="stat-item">
            <div class="stat-icon">💰</div>
            <div class="stat-info">
              <div class="stat-value">¥{{ stats.monthly_cost.toFixed(2) }}</div>
              <div class="stat-label">月费用</div>
            </div>
          </div>

          <div class="stat-item">
            <div class="stat-icon">⚠️</div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.due_soon_count }}</div>
              <div class="stat-label">即将到期</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 即将到期提醒 -->
      <van-notice-bar
        v-if="dueSoonSubscriptions.length > 0"
        left-icon="warning-o"
        :text="`您有 ${dueSoonSubscriptions.length} 个订阅即将到期，请及时续费`"
        color="#dc2626"
        background="#fef2f2"
        @click="showDueSoonList = true"
      />

      <!-- 订阅列表 -->
      <div class="subscriptions-list">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="onLoad"
          >
            <div v-if="subscriptions.length === 0 && !loading" class="empty-state">
              <van-empty
                image="search"
                description="暂无订阅记录"
              >
                <van-button
                  type="primary"
                  size="small"
                  @click="$router.push('/subscription/add')"
                >
                  添加第一个订阅
                </van-button>
              </van-empty>
            </div>

            <!-- 动态模板渲染 -->
            <component
              v-else
              :is="currentTemplate"
              :subscriptions="subscriptions"
              @click-subscription="goToDetail"
            />
          </van-list>
        </van-pull-refresh>
      </div>
    </div>

    <!-- 即将到期列表弹窗 -->
    <van-popup 
      v-model:show="showDueSoonList" 
      position="bottom" 
      :style="{ height: '60%' }"
      round
    >
      <div class="due-soon-popup">
        <div class="popup-header">
          <h3>即将到期的订阅</h3>
          <van-button 
            type="primary" 
            size="small" 
            text
            @click="showDueSoonList = false"
          >
            关闭
          </van-button>
        </div>
        
        <van-cell-group>
          <van-cell
            v-for="subscription in dueSoonSubscriptions"
            :key="subscription.id"
            :title="subscription.name"
            :label="`${subscription.days_until_billing}天后到期`"
            :value="`¥${subscription.amount}`"
            is-link
            @click="goToDetail(subscription.id)"
          >
            <template #icon>
              <div class="subscription-icon">
                {{ getServiceIcon(subscription.service_type) }}
              </div>
            </template>
          </van-cell>
        </van-cell-group>
      </div>
    </van-popup>

    <!-- 模板选择器 -->
    <van-popup
      v-model:show="showTemplateSelector"
      position="bottom"
      :style="{ height: '50%' }"
      round
    >
      <div class="template-selector">
        <div class="popup-header">
          <h3>选择显示模板</h3>
          <van-button
            type="primary"
            size="small"
            text
            @click="showTemplateSelector = false"
          >
            关闭
          </van-button>
        </div>

        <div class="template-grid">
          <div
            v-for="template in templates"
            :key="template.name"
            class="template-item"
            :class="{ active: currentTemplateName === template.name }"
            @click="selectTemplate(template.name)"
          >
            <div class="template-preview">
              <div class="preview-icon">{{ template.icon }}</div>
              <div class="template-info">
                <h4>{{ template.title }}</h4>
                <p>{{ template.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useSubscriptionsStore } from '@/stores/subscriptions'
import { showToast } from 'vant'
import ServiceLogo from '@/components/ServiceLogo.vue'

// 导入模板组件
import TimelineTemplate from './templates/TimelineTemplate.vue'
import TimelineModernTemplate from './templates/TimelineModernTemplate.vue'
import TimelineWarmTemplate from './templates/TimelineWarmTemplate.vue'

const router = useRouter()
const subscriptionsStore = useSubscriptionsStore()

const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const showDueSoonList = ref(false)
const showTemplateSelector = ref(false)
const currentTemplateName = ref('modern')

const subscriptions = computed(() => subscriptionsStore.subscriptions || [])
const stats = computed(() => subscriptionsStore.stats || {
  total_subscriptions: 0,
  monthly_cost: 0,
  yearly_cost: 0,
  due_soon_count: 0,
  service_types: {}
})
const dueSoonSubscriptions = computed(() => subscriptionsStore.dueSoonSubscriptions || [])

// 模板配置
const templates = ref([
  {
    name: 'timeline',
    title: '原版时间轴',
    description: '黑金VIP风格时间轴',
    icon: '⏰',
    component: TimelineTemplate
  },
  {
    name: 'modern',
    title: '现代简约',
    description: '浅色主题，简洁现代',
    icon: '🤍',
    component: TimelineModernTemplate
  },
  {
    name: 'warm',
    title: '暖色温馨',
    description: '米色主题，温馨舒适',
    icon: '�',
    component: TimelineWarmTemplate
  }
])

// 当前模板组件
const currentTemplate = computed(() => {
  const template = templates.value.find(t => t.name === currentTemplateName.value)
  return template ? template.component : TimelineTemplate
})

// 选择模板
const selectTemplate = (templateName) => {
  currentTemplateName.value = templateName
  showTemplateSelector.value = false
  showToast(`已切换到${templates.value.find(t => t.name === templateName)?.title}`)
}

// 获取服务图标 (使用新的配置系统)
const getServiceIcon = (serviceType) => {
  // 这个函数现在主要用于即将到期弹窗中的图标显示
  // 其他地方应该使用 ServiceLogo 组件
  const iconMap = {
    'chatgpt_pro': '🤖',
    'chatgpt_plus': '🤖',
    'chatgpt_team': '🤖',
    'icloud_plus': '☁️',
    'youtube_premium': '📺',
    'youtube_music': '🎵'
  }
  return iconMap[serviceType] || '📱'
}

// 获取服务类型标签 (使用新的配置系统)
const getServiceTypeLabel = (serviceType) => {
  const typeLabels = {
    'chatgpt_pro': 'AI工具',
    'chatgpt_plus': 'AI工具',
    'chatgpt_team': 'AI工具',
    'icloud_plus': '云服务',
    'youtube_premium': '视频平台',
    'youtube_music': '音乐'
  }
  return typeLabels[serviceType] || '其他'
}

// 获取状态样式类
const getStatusClass = (subscription) => {
  if (subscription.is_due_soon) {
    return 'status-warning'
  } else if (!subscription.is_active) {
    return 'status-inactive'
  } else {
    return 'status-active'
  }
}

// 获取状态文本
const getStatusText = (subscription) => {
  if (subscription.is_due_soon) {
    return `${subscription.days_until_billing}天后到期`
  } else if (!subscription.is_active) {
    return '已暂停'
  } else {
    return '正常'
  }
}

// 获取周期标签
const getCycleLabel = (cycle) => {
  const cycleMap = {
    'weekly': '周',
    'monthly': '月',
    'quarterly': '季',
    'yearly': '年'
  }
  return cycleMap[cycle] || '月'
}

// 格式化日期
const formatDate = (dateString) => {
  try {
    const date = new Date(dateString)
    return `${date.getMonth() + 1}月${date.getDate()}日`
  } catch {
    return '未知'
  }
}

// 跳转到详情页
const goToDetail = (id) => {
  router.push(`/subscription/detail/${id}`)
}

// 下拉刷新
const onRefresh = async () => {
  try {
    await subscriptionsStore.fetchSubscriptions()
    await subscriptionsStore.fetchStats()
    await subscriptionsStore.fetchDueSoon()
    showToast('刷新成功')
  } catch (error) {
    showToast('刷新失败')
  } finally {
    refreshing.value = false
  }
}

// 加载更多（这里暂时不需要分页）
const onLoad = () => {
  finished.value = true
}

// 组件挂载时获取数据
onMounted(async () => {
  loading.value = true
  try {
    await subscriptionsStore.fetchSubscriptions()
    await subscriptionsStore.fetchStats()
    await subscriptionsStore.fetchDueSoon()
  } catch (error) {
    showToast('获取数据失败')
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.subscriptions-page {
  background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
  min-height: 100vh;
}

.page-container {
  padding: 16px;
}

.stats-card {
  background: #ffffff;
  color: #1f2937;
  padding: 20px;
  border-radius: 16px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stats-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: #f1f5f9;
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.stat-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 16px;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
}

.subscriptions-list {
  margin-top: 16px;
}

.subscription-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.subscription-card {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 16px;
  padding: 16px;
  border: 1px solid rgba(212, 175, 55, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
}

.subscription-card:active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.service-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.service-logo {
  margin-right: 12px;
}

.service-details {
  flex: 1;
}

.service-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #ffd700;
}

.service-type {
  margin: 0;
  font-size: 12px;
  color: #e6d7b7;
  opacity: 0.8;
}

.renewal-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 500;
}

.status-active {
  background: rgba(0, 212, 170, 0.2);
  color: #00d4aa;
  border: 1px solid rgba(0, 212, 170, 0.3);
}

.status-warning {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-inactive {
  background: rgba(108, 117, 125, 0.2);
  color: #6c757d;
  border: 1px solid rgba(108, 117, 125, 0.3);
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-info {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.amount {
  font-size: 18px;
  font-weight: 700;
  color: #ffd700;
}

.cycle {
  font-size: 12px;
  color: #e6d7b7;
  opacity: 0.8;
}

.next-billing {
  text-align: right;
}

.billing-text {
  font-size: 11px;
  color: #e6d7b7;
  opacity: 0.7;
}

.empty-state {
  padding: 40px 20px;
}

.due-soon-popup {
  padding: 20px;
  background: #ffffff;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.popup-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

/* 导航栏样式 */
.nav-actions {
  display: flex;
  gap: 8px;
}

.template-btn {
  margin-right: 8px;
}

/* 模板选择器样式 */
.template-selector {
  padding: 20px;
  background: #ffffff;
  color: #1f2937;
  height: 100%;
}

.template-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.template-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-item:active {
  transform: scale(0.98);
}

.template-item:hover {
  background: #f1f5f9;
  border-color: #3b82f6;
}

.template-item.active {
  border-color: #3b82f6;
  background: #dbeafe;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.template-preview {
  display: flex;
  align-items: center;
  gap: 12px;
}

.preview-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e2e8f0;
  border-radius: 8px;
}

.template-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.template-info p {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
}
</style>
