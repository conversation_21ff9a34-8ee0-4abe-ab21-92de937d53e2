/* 预算管理样式 */

.budgets-container {
    background-color: var(--color-card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.budgets-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.budgets-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--color-text);
}

/* 表单样式 */
.budget-form {
    background-color: var(--color-form-bg);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

/* 预算表格 */
.budget-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: var(--spacing-md);
    box-shadow: var(--shadow-sm);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.budget-table th {
    text-align: left;
    padding: 15px;
    background-color: var(--color-table-header);
    color: var(--color-text);
    font-weight: 600;
    font-size: var(--font-size-sm);
    border-bottom: 1px solid var(--color-border);
}

.budget-table td {
    padding: 15px;
    border-bottom: 1px solid var(--color-border);
    font-size: var(--font-size-base);
}

.budget-table tr:hover {
    background-color: var(--color-hover);
}

.budget-table tr:last-child td {
    border-bottom: none;
}

/* 预算行样式 */
.budget-category {
    font-weight: 500;
}

/* 进度条样式 */
.progress-container {
    position: relative;
    background-color: #e9ecef;
    border-radius: 12px;
    height: 24px;
    margin-bottom: 8px;
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    height: 100%;
    border-radius: 12px;
    transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        rgba(255, 255, 255, 0.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.2) 75%,
        transparent 75%);
    background-size: 20px 20px;
    animation: progress-shine 2s linear infinite;
}

@keyframes progress-shine {
    0% { transform: translateX(-20px); }
    100% { transform: translateX(20px); }
}

.progress-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.progress-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}

.progress-danger {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

.progress-unknown {
    background: linear-gradient(135deg, #6c757d, #adb5bd);
    animation: pulse 1.5s infinite;
    box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
}

@keyframes pulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

.progress-text {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 11px !important;
    font-weight: 700 !important;
    color: white !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
    letter-spacing: 0.5px !important;
    z-index: 10 !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    white-space: nowrap !important;
    pointer-events: none !important;
    visibility: visible !important;
    text-align: center !important;
}

.progress-amount {
    font-size: 11px;
    color: #495057;
    text-align: center;
    font-weight: 500;
    margin-top: 2px;
}

.budget-amount {
    color: var(--color-text);
    font-family: monospace;
    font-size: var(--font-size-base);
}

.budget-period {
    color: var(--color-text-light);
    font-size: var(--font-size-sm);
}

.budget-date-range {
    color: var(--color-text-light);
    font-size: var(--font-size-sm);
}

.budget-progress {
    width: 220px;
    min-width: 180px;
}

.budget-actions {
    text-align: right;
    white-space: nowrap;
}

/* 进度条悬停效果 */
.progress-container:hover {
    transform: translateY(-1px);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.progress-container:hover .progress-bar::before {
    animation-duration: 1s;
}

/* 进度条状态指示器 */
.progress-container::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #28a745;
    opacity: 0;
    transition: opacity 0.3s ease;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
}

.progress-container.progress-warning::after {
    background: #ffc107;
    opacity: 1;
    animation: warning-pulse 2s infinite;
}

.progress-container.progress-danger::after {
    background: #dc3545;
    opacity: 1;
    animation: danger-pulse 1.5s infinite;
}

@keyframes warning-pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
}

@keyframes danger-pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.3);
        opacity: 0.6;
    }
}

/* 进度条文字优化 - 重复定义已删除 */

/* 超支状态特殊样式 */
.progress-danger .progress-text {
    animation: text-flash 1s infinite;
}

@keyframes text-flash {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.7; }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .budget-table th:nth-child(3),
    .budget-table td:nth-child(3) {
        display: none;
    }

    .budget-progress {
        width: 160px;
        min-width: 140px;
    }

    .progress-text {
        font-size: 10px !important;
        /* 强制确保在小屏幕上文字仍然居中 */
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        z-index: 10 !important;
        color: white !important;
        font-weight: 700 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
    }

    .progress-amount {
        font-size: 10px;
    }
}

@media (max-width: 480px) {
    .budget-table th:nth-child(4),
    .budget-table td:nth-child(4) {
        display: none;
    }

    .budget-progress {
        width: 120px;
        min-width: 100px;
    }

    .progress-container {
        height: 20px;
    }

    .progress-text {
        font-size: 9px !important;
        /* 强制确保在最小屏幕上文字仍然居中 */
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        z-index: 10 !important;
        color: white !important;
        font-weight: 700 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
    }

    .progress-amount {
        font-size: 9px;
    }
}
