<template>
  <div class="service-logo-container" :class="`size-${size}`" :style="{ color: serviceConfig.color }">
    <div class="logo" :class="`${serviceConfig.icon}-logo`">
      <!-- 临时使用内联SVG，确保图标正确显示 -->
      <div v-if="serviceConfig.icon === 'chatgpt'" class="icon-svg">
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142.0852 4.783 2.7582a.7712.7712 0 0 0 .7806 0l5.8428-3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zM2.3408 7.8956a4.485 4.485 0 0 1 2.3655-1.9728V11.6a.7664.7664 0 0 0 .3879.6765l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0l-4.8303-2.7865A4.504 4.504 0 0 1 2.3408 7.872zm16.5963 3.8558L13.1038 8.364 15.1192 7.2a.0757.0757 0 0 1 .071 0l4.8303 2.7913a4.4944 4.4944 0 0 1-.6765 8.1042v-5.6772a.79.79 0 0 0-.407-.667zm2.0107-3.0231l-.142-.0852-4.7735-2.7818a.7759.7759 0 0 0-.7854 0L9.409 9.2297V6.8974a.0662.0662 0 0 1 .0284-.0615l4.8303-2.7866a4.4992 4.4992 0 0 1 6.6802 4.66zM8.3065 12.863l-2.02-1.1638a.0804.0804 0 0 1-.038-.0567V6.0742a4.4992 4.4992 0 0 1 7.3757-3.4537l-.142.0805L8.704 5.459a.7948.7948 0 0 0-.3927.6813zm1.0976-2.3654l2.602-1.4998 2.6069 1.4998v2.9994l-2.5974 1.4997-2.6067-1.4997Z" fill="currentColor"/>
        </svg>
      </div>
      <div v-else-if="serviceConfig.icon === 'icloud'" class="icon-svg">
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M18.5 9.5C18.5 5.91 15.59 3 12 3C8.41 3 5.5 5.91 5.5 9.5C3.57 9.5 2 11.07 2 13C2 14.93 3.57 16.5 5.5 16.5H18.5C20.43 16.5 22 14.93 22 13C22 11.07 20.43 9.5 18.5 9.5Z" fill="currentColor"/>
        </svg>
      </div>
      <div v-else-if="serviceConfig.icon === 'youtube'" class="icon-svg">
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect width="24" height="17" y="3.5" rx="4" fill="currentColor"/>
          <path d="M10 8.5V15.5L16 12L10 8.5Z" fill="white"/>
        </svg>
      </div>
      <div v-else class="icon-svg">
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="10" fill="currentColor"/>
          <path d="M12 8V16M8 12H16" stroke="white" stroke-width="2" stroke-linecap="round"/>
        </svg>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { getServiceConfig } from '@/config/serviceIcons'

const props = defineProps({
  serviceName: {
    type: String,
    required: true
  },
  serviceType: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  }
})

// 获取服务配置
const serviceConfig = computed(() => {
  // 优先使用 serviceType，如果没有则尝试从 serviceName 推断
  let type = props.serviceType

  // 如果没有serviceType，尝试从serviceName推断
  if (!type && props.serviceName) {
    const name = props.serviceName.toLowerCase()
    if (name.includes('chatgpt')) {
      if (name.includes('plus')) type = 'chatgpt_plus'
      else if (name.includes('pro')) type = 'chatgpt_pro'
      else if (name.includes('team')) type = 'chatgpt_team'
      else type = 'chatgpt_plus' // 默认为plus
    }
    else if (name.includes('augment')) type = 'augment_code'
    else if (name.includes('cursor')) type = 'cursor'
    else if (name.includes('icloud')) type = 'icloud_plus'
    else if (name.includes('youtube')) {
      if (name.includes('music')) type = 'youtube_music'
      else type = 'youtube_premium'
    }
  }

  // 获取配置，如果找不到则使用默认配置
  const config = getServiceConfig(type)

  // 如果仍然是未知服务，尝试更智能的匹配
  if (config.name === '未知服务' && props.serviceName) {
    const name = props.serviceName.toLowerCase()
    // 根据服务名称提供更好的默认值
    if (name.includes('ai') || name.includes('gpt') || name.includes('augment') || name.includes('cursor')) {
      return {
        name: props.serviceName,
        icon: 'chatgpt',
        category: 'ai',
        color: '#74aa9c'
      }
    }
    else if (name.includes('cloud') || name.includes('storage')) {
      return {
        name: props.serviceName,
        icon: 'icloud',
        category: 'cloud',
        color: '#007AFF'
      }
    }
    else if (name.includes('video') || name.includes('youtube') || name.includes('netflix')) {
      return {
        name: props.serviceName,
        icon: 'youtube',
        category: 'streaming',
        color: '#FF0000'
      }
    }
  }

  return config
})


</script>

<style scoped>
.service-logo-container {
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.3);
  transition: all 0.3s ease;
}

.service-logo-container:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 尺寸变体 */
.size-small {
  width: 32px;
  height: 32px;
  border-radius: 8px;
}

.size-medium {
  width: 48px;
  height: 48px;
  border-radius: 12px;
}

.size-large {
  width: 64px;
  height: 64px;
  border-radius: 16px;
}

.logo {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, currentColor 0%, color-mix(in srgb, currentColor 80%, black) 100%);
}

.icon-svg {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-svg svg {
  width: 70%;
  height: 70%;
  color: white;
}

/* 确保SVG图标正确显示 */
.icon-svg svg [fill="currentColor"] {
  fill: white;
}

.icon-svg svg [stroke="currentColor"] {
  stroke: white;
}
</style>
