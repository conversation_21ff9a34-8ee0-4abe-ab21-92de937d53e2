import { defineStore } from 'pinia'
import { transactionsAPI } from '@/api/transactions'

export const useTransactionsStore = defineStore('transactions', {
  state: () => ({
    transactions: [],
    loading: false,
    stats: {
      totalIncome: 0,
      totalExpense: 0,
      netWorth: 0,
      incomeChange: 0,
      expenseChange: 0,
      netWorthChange: 0,
      todayExpense: 0,
      monthlyBudgetRemaining: 0
    },
    filters: {
      currency: 'ALL',
      type: 'ALL',
      category: 'ALL',
      dateRange: null
    }
  }),

  getters: {
    // 获取筛选后的交易
    filteredTransactions: (state) => {
      let filtered = [...state.transactions]
      
      if (state.filters.currency !== 'ALL') {
        filtered = filtered.filter(t => t.currency === state.filters.currency)
      }
      
      if (state.filters.type !== 'ALL') {
        filtered = filtered.filter(t => t.type === state.filters.type)
      }
      
      if (state.filters.category !== 'ALL') {
        filtered = filtered.filter(t => t.category === state.filters.category)
      }
      
      return filtered.sort((a, b) => new Date(b.date) - new Date(a.date))
    },

    // 获取最近交易（前10条）
    recentTransactions: (state) => {
      return state.transactions
        .sort((a, b) => new Date(b.date) - new Date(a.date))
        .slice(0, 10)
    },

    // 按类型分组的交易
    transactionsByType: (state) => {
      const income = state.transactions.filter(t => t.type === 'income')
      const expense = state.transactions.filter(t => t.type === 'expense')
      return { income, expense }
    },

    // 按分类统计支出
    expensesByCategory: (state) => {
      const expenses = state.transactions.filter(t => t.type === 'expense')
      const categoryStats = {}
      
      expenses.forEach(transaction => {
        const category = transaction.category
        if (!categoryStats[category]) {
          categoryStats[category] = {
            category,
            amount: 0,
            count: 0
          }
        }
        categoryStats[category].amount += transaction.amount
        categoryStats[category].count += 1
      })
      
      return Object.values(categoryStats).sort((a, b) => b.amount - a.amount)
    }
  },

  actions: {
    // 获取交易列表
    async fetchTransactions(params = {}) {
      this.loading = true
      try {
        console.log('Store: 开始调用 transactionsAPI.getTransactions()')
        const response = await transactionsAPI.getTransactions(params)
        console.log('Store: API响应:', response)

        // 检查响应格式
        let transactions = []
        if (response && response.transactions) {
          transactions = response.transactions
        } else if (Array.isArray(response)) {
          transactions = response
        } else if (response && response.data) {
          transactions = response.data
        }

        console.log('Store: 处理后的交易数据:', transactions)
        this.transactions = Array.isArray(transactions) ? transactions : []
        console.log('Store: 设置到store的交易数据:', this.transactions)

        // 计算统计数据
        await this.calculateStats()

        return this.transactions
      } catch (error) {
        console.error('获取交易列表失败:', error)
        this.transactions = []
        throw error
      } finally {
        this.loading = false
      }
    },

    // 创建交易
    async createTransaction(data) {
      try {
        console.log('Store: 创建交易数据:', data)
        const response = await transactionsAPI.createTransaction(data)
        console.log('Store: 创建交易响应:', response)

        // 创建成功后重新获取交易列表
        if (response && response.success) {
          await this.fetchTransactions()
        }

        return response
      } catch (error) {
        console.error('创建交易失败:', error)
        throw error
      }
    },

    // 创建KDI出金双向交易
    async createKDIWithdrawTransaction(kdiTransactionData, withdrawAccountId) {
      try {
        console.log('Store: 创建KDI出金双向交易:', { kdiTransactionData, withdrawAccountId })

        // 获取账户信息
        const { useAccountsStore } = await import('@/stores/accounts')
        const accountsStore = useAccountsStore()

        // 确保账户数据已加载
        if (!accountsStore.accounts || accountsStore.accounts.length === 0) {
          await accountsStore.fetchAccounts()
        }

        const withdrawAccount = accountsStore.accounts.find(acc => acc.id === withdrawAccountId)
        if (!withdrawAccount) {
          throw new Error('出金账户不存在')
        }

        // 1. 创建KDI账户的支出记录（出金）
        const kdiResponse = await transactionsAPI.createTransaction(kdiTransactionData)
        console.log('Store: KDI出金记录创建响应:', kdiResponse)

        // 2. 创建目标账户的收入记录（来自KDI出金）
        const targetTransactionData = {
          type: 'income',
          amount: kdiTransactionData.amount,
          description: `来自KDI出金 - ${kdiTransactionData.description}`,
          date: kdiTransactionData.date,
          category: '投资收益', // 或者可以是其他合适的收入分类
          account: withdrawAccountId,
          currency: withdrawAccount.currency,
          attachment: kdiTransactionData.attachment
        }

        const targetResponse = await transactionsAPI.createTransaction(targetTransactionData)
        console.log('Store: 目标账户收入记录创建响应:', targetResponse)

        // 创建成功后重新获取交易列表
        if (kdiResponse && kdiResponse.success && targetResponse && targetResponse.success) {
          await this.fetchTransactions()
        }

        return {
          success: true,
          kdiTransaction: kdiResponse,
          targetTransaction: targetResponse
        }
      } catch (error) {
        console.error('创建KDI出金双向交易失败:', error)
        throw error
      }
    },

    // 创建账户转移交易
    async createAccountTransfer(transferData) {
      try {
        console.log('Store: 创建账户转移交易:', transferData)

        // 获取账户信息
        const { useAccountsStore } = await import('@/stores/accounts')
        const accountsStore = useAccountsStore()

        // 确保账户数据已加载
        if (!accountsStore.accounts || accountsStore.accounts.length === 0) {
          await accountsStore.fetchAccountsWithBalances()
        }

        const fromAccount = accountsStore.accounts.find(acc => acc.id === transferData.fromAccount)
        const toAccount = accountsStore.accounts.find(acc => acc.id === transferData.toAccount)

        if (!fromAccount) {
          throw new Error('转出账户不存在')
        }
        if (!toAccount) {
          throw new Error('转入账户不存在')
        }

        // 验证货币一致性
        if (fromAccount.currency !== toAccount.currency) {
          throw new Error('转出账户和转入账户的货币类型必须一致')
        }

        // 验证余额充足
        const fromBalance = fromAccount.current_balance || fromAccount.initial_balance || 0
        if (fromBalance < transferData.amount) {
          throw new Error(`转出账户余额不足，当前余额：${fromBalance}`)
        }

        // 1. 创建转出账户的支出记录
        const fromTransactionData = {
          type: 'expense',
          amount: transferData.amount,
          description: `转账至${toAccount.name} - ${transferData.description}`,
          date: transferData.date,
          category: '转账支出',
          account: transferData.fromAccount,
          currency: transferData.currency,
          attachment: transferData.attachment
        }

        const fromResponse = await transactionsAPI.createTransaction(fromTransactionData)
        console.log('Store: 转出账户记录创建响应:', fromResponse)

        // 2. 创建转入账户的收入记录
        const toTransactionData = {
          type: 'income',
          amount: transferData.amount,
          description: `来自${fromAccount.name}转账 - ${transferData.description}`,
          date: transferData.date,
          category: '转账收入',
          account: transferData.toAccount,
          currency: transferData.currency,
          attachment: transferData.attachment
        }

        const toResponse = await transactionsAPI.createTransaction(toTransactionData)
        console.log('Store: 转入账户记录创建响应:', toResponse)

        // 创建成功后重新获取交易列表和账户余额
        if (fromResponse && fromResponse.success && toResponse && toResponse.success) {
          await this.fetchTransactions()
          await accountsStore.fetchAccountsWithBalances()
        }

        return {
          success: true,
          fromTransaction: fromResponse,
          toTransaction: toResponse
        }
      } catch (error) {
        console.error('创建账户转移交易失败:', error)
        throw error
      }
    },

    // 更新交易
    async updateTransaction(id, data) {
      try {
        const response = await transactionsAPI.updateTransaction(id, data)
        const updatedTransaction = response.data || response

        if (updatedTransaction) {
          // 更新本地列表
          const index = this.transactions.findIndex(t => t.id === id)
          if (index !== -1) {
            this.transactions[index] = updatedTransaction
          }
          // 重新计算统计数据
          await this.calculateStats()
        }

        return updatedTransaction
      } catch (error) {
        console.error('更新交易失败:', error)
        throw error
      }
    },

    // 删除交易
    async deleteTransaction(id) {
      try {
        await transactionsAPI.deleteTransaction(id)
        
        // 从本地列表移除
        this.transactions = this.transactions.filter(t => t.id !== id)
        // 重新计算统计数据
        await this.calculateStats()
      } catch (error) {
        console.error('删除交易失败:', error)
        throw error
      }
    },

    // 计算统计数据
    async calculateStats() {
      const now = new Date()
      const currentMonth = now.getMonth()
      const currentYear = now.getFullYear()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

      // 今日的数据
      const todayTransactions = this.transactions.filter(t => {
        const date = new Date(t.date)
        const transactionDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
        return transactionDate.getTime() === today.getTime()
      })

      // 当前月份的数据
      const currentMonthTransactions = this.transactions.filter(t => {
        const date = new Date(t.date)
        return date.getMonth() === currentMonth && date.getFullYear() === currentYear
      })

      // 上个月的数据
      const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1
      const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear
      const lastMonthTransactions = this.transactions.filter(t => {
        const date = new Date(t.date)
        return date.getMonth() === lastMonth && date.getFullYear() === lastMonthYear
      })

      // 计算今日支出
      const todayExpense = todayTransactions
        .filter(t => t.type === 'expense')
        .reduce((sum, t) => sum + t.amount, 0)

      // 计算当前总数据
      const totalIncome = this.transactions
        .filter(t => t.type === 'income')
        .reduce((sum, t) => sum + t.amount, 0)

      const totalExpense = this.transactions
        .filter(t => t.type === 'expense')
        .reduce((sum, t) => sum + t.amount, 0)

      // 计算当前月数据
      const currentMonthIncome = currentMonthTransactions
        .filter(t => t.type === 'income')
        .reduce((sum, t) => sum + t.amount, 0)

      const currentMonthExpense = currentMonthTransactions
        .filter(t => t.type === 'expense')
        .reduce((sum, t) => sum + t.amount, 0)

      // 计算上个月数据
      const lastMonthIncome = lastMonthTransactions
        .filter(t => t.type === 'income')
        .reduce((sum, t) => sum + t.amount, 0)

      const lastMonthExpense = lastMonthTransactions
        .filter(t => t.type === 'expense')
        .reduce((sum, t) => sum + t.amount, 0)

      // 计算变化百分比
      const incomeChange = this.calculatePercentageChange(lastMonthIncome, currentMonthIncome)
      const expenseChange = this.calculatePercentageChange(lastMonthExpense, currentMonthExpense)
      const netWorthChange = this.calculatePercentageChange(
        lastMonthIncome - lastMonthExpense,
        currentMonthIncome - currentMonthExpense
      )

      // 计算本月预算剩余
      const monthlyBudgetRemaining = await this.calculateMonthlyBudgetRemaining(currentMonthExpense)

      // 更新统计数据
      this.stats.todayExpense = todayExpense
      this.stats.monthlyBudgetRemaining = monthlyBudgetRemaining
      this.stats.totalIncome = totalIncome
      this.stats.totalExpense = totalExpense
      this.stats.netWorth = totalIncome - totalExpense
      this.stats.incomeChange = incomeChange
      this.stats.expenseChange = expenseChange
      this.stats.netWorthChange = netWorthChange

      console.log('统计数据计算完成:', {
        todayExpense,
        monthlyBudgetRemaining,
        totalIncome,
        totalExpense,
        netWorth: totalIncome - totalExpense,
        currentMonth: { income: currentMonthIncome, expense: currentMonthExpense },
        lastMonth: { income: lastMonthIncome, expense: lastMonthExpense },
        changes: { incomeChange, expenseChange, netWorthChange }
      })
    },

    // 计算百分比变化
    calculatePercentageChange(oldValue, newValue) {
      if (oldValue === 0) {
        return newValue > 0 ? 100 : 0
      }
      return Math.round(((newValue - oldValue) / oldValue) * 100)
    },

    // 计算本月预算剩余
    async calculateMonthlyBudgetRemaining(currentMonthExpense) {
      try {
        // 获取预算数据
        const { useBudgetsStore } = await import('@/stores/budgets')
        const budgetsStore = useBudgetsStore()

        // 确保预算数据已加载
        if (!budgetsStore.budgets || budgetsStore.budgets.length === 0) {
          await budgetsStore.fetchBudgets()
        }

        // 获取当前月份信息
        const now = new Date()
        const currentYear = now.getFullYear()
        const currentMonth = now.getMonth() + 1 // 1-12
        const currentDateStr = `${currentYear}-${String(currentMonth).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`

        // 计算当前月份的预算总额
        let monthlyBudgetTotal = 0

        if (budgetsStore.budgets && budgetsStore.budgets.length > 0) {
          budgetsStore.budgets.forEach((budget) => {
            // 检查预算期间是否包含当前日期
            if (budget.start_date <= currentDateStr && budget.end_date >= currentDateStr) {
              monthlyBudgetTotal += budget.amount || 0
            }
          })
        }

        // 计算剩余预算 (允许负数表示超支)
        const remaining = monthlyBudgetTotal - currentMonthExpense
        return remaining
      } catch (error) {
        console.error('计算本月预算剩余失败:', error)
        return 0
      }
    },

    // 设置筛选条件
    setFilter(key, value) {
      this.filters[key] = value
    },

    // 清除筛选条件
    clearFilters() {
      this.filters = {
        currency: 'ALL',
        type: 'ALL',
        category: 'ALL',
        dateRange: null
      }
    }
  }
})
