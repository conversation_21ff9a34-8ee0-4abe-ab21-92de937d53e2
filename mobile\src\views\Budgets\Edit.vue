<template>
  <div class="edit-budget-page">
    <van-nav-bar
      title="编辑预算"
      left-arrow
      @click-left="$router.back()"
      fixed
      placeholder
    />

    <div v-if="loading" class="loading-container">
      <van-loading size="24px" />
      <span>加载中...</span>
    </div>

    <div v-else-if="!budget" class="error-container">
      <div class="error-icon">❌</div>
      <p>预算不存在或已被删除</p>
      <van-button type="primary" size="small" @click="$router.back()">
        返回
      </van-button>
    </div>

    <div v-else class="page-container">
      <van-form @submit="handleSubmit">
        <!-- 基本信息 -->
        <van-cell-group inset title="基本信息">
          <van-field
            v-model="form.project_name"
            name="project_name"
            label="项目名称"
            placeholder="请输入预算项目名称"
            :rules="[{ required: true, message: '请输入项目名称' }]"
          />

          <van-field
            v-model="form.category"
            name="category"
            label="预算类别"
            placeholder="选择支出类别"
            readonly
            is-link
            @click="showCategoryPicker = true"
            :rules="[{ required: true, message: '请选择预算类别' }]"
          />
          
          <van-field
            v-model="form.amount"
            name="amount"
            label="预算金额"
            placeholder="请输入预算金额"
            type="number"
            :rules="[
              { required: true, message: '请输入预算金额' },
              { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入有效的金额' }
            ]"
          >
            <template #left-icon>
              <span class="currency-symbol">{{ getCurrencySymbol(form.currency) }}</span>
            </template>
          </van-field>

          <van-field
            v-model="form.currency"
            name="currency"
            label="货币类型"
            placeholder="选择货币"
            readonly
            is-link
            @click="showCurrencyPicker = true"
            :rules="[{ required: true, message: '请选择货币类型' }]"
          />
        </van-cell-group>

        <!-- 预算周期 -->
        <van-cell-group inset title="预算周期">
          <van-field
            v-model="budgetPeriodDisplay"
            name="period"
            label="预算月份"
            placeholder="选择年月"
            readonly
            is-link
            @click="showDatePicker = true"
            :rules="[{ required: true, message: '请选择预算月份' }]"
          />
        </van-cell-group>

        <!-- 预算描述 -->
        <van-cell-group inset title="其他信息">
          <van-field
            v-model="form.description"
            name="description"
            label="预算描述"
            placeholder="添加预算说明（可选）"
            type="textarea"
            rows="3"
            maxlength="200"
            show-word-limit
          />
        </van-cell-group>

        <!-- 提交按钮 -->
        <div class="submit-container">
          <van-button
            type="primary"
            native-type="submit"
            block
            round
            :loading="submitLoading"
          >
            更新预算
          </van-button>
        </div>
      </van-form>
    </div>

    <!-- 类别选择器 -->
    <van-popup v-model:show="showCategoryPicker" position="bottom">
      <van-picker
        :columns="categoryColumns"
        @confirm="onCategoryConfirm"
        @cancel="showCategoryPicker = false"
      />
    </van-popup>

    <!-- 货币选择器 -->
    <van-popup v-model:show="showCurrencyPicker" position="bottom">
      <van-picker
        :columns="currencyColumns"
        @confirm="onCurrencyConfirm"
        @cancel="showCurrencyPicker = false"
      />
    </van-popup>

    <!-- 年月选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-picker
        :columns="[yearColumns, monthColumns]"
        :default-index="[yearColumns.findIndex(y => y.value === form.year), monthColumns.findIndex(m => m.value === form.month)]"
        title="选择年月"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useBudgetsStore } from '@/stores/budgets'
import { showToast, showSuccessToast } from 'vant'
import { budgetCategories, getCategoryOptions } from '@/config/categories'
import { getCurrencyOptionsForSubscriptions, getCurrencySymbol } from '@/config/currencies'

const router = useRouter()
const route = useRoute()
const budgetsStore = useBudgetsStore()

// 响应式数据
const loading = ref(true)
const submitLoading = ref(false)
const budget = ref(null)
const showCategoryPicker = ref(false)
const showCurrencyPicker = ref(false)
const showDatePicker = ref(false)

// 表单数据
const form = ref({
  project_name: '',
  category: '',
  amount: '',
  currency: 'MYR',
  year: new Date().getFullYear(),
  month: new Date().getMonth() + 1,
  description: ''
})



// 计算属性
const budgetPeriodDisplay = computed(() => {
  if (!form.value.year || !form.value.month) {
    return '选择年月'
  }
  return `${form.value.year}年${form.value.month}月`
})

// 选择器选项 - 使用配置文件中的分类
const categoryColumns = getCategoryOptions('budget')

// 年份选择器选项
const currentYear = new Date().getFullYear()
const yearColumns = []
for (let i = currentYear - 2; i <= currentYear + 5; i++) {
  yearColumns.push({ text: `${i}年`, value: i })
}

// 月份选择器选项
const monthColumns = [
  { text: '01月', value: 1 },
  { text: '02月', value: 2 },
  { text: '03月', value: 3 },
  { text: '04月', value: 4 },
  { text: '05月', value: 5 },
  { text: '06月', value: 6 },
  { text: '07月', value: 7 },
  { text: '08月', value: 8 },
  { text: '09月', value: 9 },
  { text: '10月', value: 10 },
  { text: '11月', value: 11 },
  { text: '12月', value: 12 }
]

const currencyColumns = getCurrencyOptionsForSubscriptions()

const periodColumns = [
  { text: '每月', value: 'monthly' },
  { text: '每周', value: 'weekly' },
  { text: '自定义', value: 'custom' }
]

// 计算属性
const periodDisplay = computed(() => {
  return formatPeriod(form.value.period)
})

// 格式化周期
const formatPeriod = (period) => {
  const periodMap = {
    monthly: '每月',
    weekly: '每周',
    custom: '自定义'
  }
  return periodMap[period] || period
}

// 获取货币符号 - 使用统一配置
// getCurrencySymbol 已从配置文件导入

// 初始化
onMounted(async () => {
  await loadBudget()
})

// 加载预算数据
const loadBudget = async () => {
  loading.value = true
  try {
    const budgetId = route.params.id
    const budgetData = await budgetsStore.fetchBudget(budgetId)

    if (budgetData) {
      budget.value = budgetData

      // 填充表单数据
      const startDate = new Date(budgetData.start_date)
      form.value = {
        project_name: budgetData.project_name || '',
        category: budgetData.category,
        amount: budgetData.amount.toString(),
        currency: budgetData.currency,
        year: startDate.getFullYear(),
        month: startDate.getMonth() + 1,
        description: budgetData.description || ''
      }




    }
  } catch (error) {
    console.error('加载预算失败:', error)
    showToast({
      message: '加载预算失败',
      type: 'fail'
    })
  } finally {
    loading.value = false
  }
}

// 选择器确认事件
const onCategoryConfirm = ({ selectedOptions }) => {
  form.value.category = selectedOptions[0].value
  showCategoryPicker.value = false
}

const onCurrencyConfirm = ({ selectedOptions }) => {
  form.value.currency = selectedOptions[0].value
  showCurrencyPicker.value = false
}

const onDateConfirm = ({ selectedOptions }) => {
  form.value.year = selectedOptions[0].value
  form.value.month = selectedOptions[1].value
  showDatePicker.value = false
}

// 提交表单
const handleSubmit = async () => {
  submitLoading.value = true
  try {
    // 验证年月
    if (!form.value.year || !form.value.month) {
      showToast({
        message: '请选择预算年月',
        type: 'fail'
      })
      return
    }

    // 根据年月计算开始和结束日期
    const year = form.value.year
    const month = form.value.month

    // 使用字符串格式避免时区问题
    const startDate = `${year}-${String(month).padStart(2, '0')}-01`
    const lastDay = new Date(year, month, 0).getDate() // 获取该月的最后一天
    const endDate = `${year}-${String(month).padStart(2, '0')}-${String(lastDay).padStart(2, '0')}`

    // 转换金额为数字并构建预算数据
    const budgetData = {
      ...form.value,
      amount: parseFloat(form.value.amount),
      period: 'monthly',
      start_date: startDate,
      end_date: endDate
    }

    await budgetsStore.updateBudget(route.params.id, budgetData)

    showSuccessToast('预算更新成功')
    router.back()
  } catch (error) {
    console.error('更新预算失败:', error)
    showToast({
      message: error.message || '更新预算失败',
      type: 'fail'
    })
  } finally {
    submitLoading.value = false
  }
}
</script>

<style scoped>
.edit-budget-page {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #969799;
  gap: 16px;
}

.error-icon {
  font-size: 48px;
}

.page-container {
  padding-bottom: 20px;
}

.currency-symbol {
  color: #969799;
  margin-right: 4px;
}

.submit-container {
  margin: 24px 16px;
}
</style>
