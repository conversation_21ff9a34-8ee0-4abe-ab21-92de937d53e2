#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户认证相关API蓝图
提供注册、登录、登出功能
"""

from flask import Blueprint, request, jsonify, session, g
import datetime
import uuid
import logging
import sqlite3
import bcrypt
import re
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.database_config import MAIN_DB_PATH

# 创建蓝图
bp = Blueprint('auth', __name__)
logger = logging.getLogger(__name__)

def get_db_connection():
    """获取数据库连接"""
    if 'db' not in g:
        g.db = sqlite3.connect(MAIN_DB_PATH)
        g.db.row_factory = sqlite3.Row
        # 启用外键约束
        g.db.execute('PRAGMA foreign_keys = ON')
    return g.db

def validate_email(email):
    """验证邮箱格式"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_password(password):
    """验证密码强度"""
    if len(password) < 6:
        return False, "密码长度至少6位"
    if not re.search(r'[A-Za-z]', password):
        return False, "密码必须包含字母"
    if not re.search(r'\d', password):
        return False, "密码必须包含数字"
    return True, "密码符合要求"

def hash_password(password):
    """加密密码"""
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')

def verify_password(password, hashed):
    """验证密码"""
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

@bp.route('/register', methods=['POST'])
def register():
    """用户注册"""
    try:
        data = request.json
        logger.info(f"用户注册请求: {data.get('username', 'unknown')}")

        # 验证必填字段
        required_fields = ['username', 'email', 'password']
        for field in required_fields:
            if field not in data or not data[field].strip():
                return jsonify({'success': False, 'error': f'缺少必填字段: {field}'}), 400

        username = data['username'].strip()
        email = data['email'].strip().lower()
        password = data['password']
        display_name = data.get('display_name', username).strip()

        # 验证用户名长度
        if len(username) < 3 or len(username) > 20:
            return jsonify({'success': False, 'error': '用户名长度必须在3-20位之间'}), 400

        # 验证邮箱格式
        if not validate_email(email):
            return jsonify({'success': False, 'error': '邮箱格式不正确'}), 400

        # 验证密码强度
        is_valid, message = validate_password(password)
        if not is_valid:
            return jsonify({'success': False, 'error': message}), 400

        conn = get_db_connection()

        # 检查用户名是否已存在
        existing_user = conn.execute(
            'SELECT id FROM users WHERE username = ? OR email = ?',
            (username, email)
        ).fetchone()

        if existing_user:
            return jsonify({'success': False, 'error': '用户名或邮箱已存在'}), 409

        # 创建新用户
        user_id = str(uuid.uuid4())
        hashed_password = hash_password(password)
        now = datetime.datetime.now().isoformat()

        conn.execute('''
            INSERT INTO users (id, username, email, password_hash, display_name,
                             is_active, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (user_id, username, email, hashed_password, display_name, True, now, now))

        conn.commit()
        logger.info(f"用户注册成功: {username}")

        return jsonify({
            'success': True,
            'message': '注册成功',
            'user': {
                'id': user_id,
                'username': username,
                'email': email,
                'display_name': display_name
            }
        })

    except Exception as e:
        logger.error(f"用户注册失败: {e}")
        return jsonify({'success': False, 'error': '注册失败，请稍后重试'}), 500

@bp.route('/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.json
        logger.info(f"用户登录请求: {data.get('username', 'unknown')}")

        # 验证必填字段
        if not data.get('username') or not data.get('password'):
            return jsonify({'success': False, 'error': '用户名和密码不能为空'}), 400

        username = data['username'].strip()
        password = data['password']

        conn = get_db_connection()

        # 查找用户（支持用户名或邮箱登录）
        user = conn.execute('''
            SELECT id, username, email, password_hash, display_name, is_active, last_login_at
            FROM users
            WHERE (username = ? OR email = ?) AND is_active = 1
        ''', (username, username.lower())).fetchone()

        if not user:
            return jsonify({'success': False, 'error': '用户名或密码错误'}), 401

        # 验证密码
        if not verify_password(password, user['password_hash']):
            return jsonify({'success': False, 'error': '用户名或密码错误'}), 401

        # 更新最后登录时间
        now = datetime.datetime.now().isoformat()
        conn.execute(
            'UPDATE users SET last_login_at = ?, updated_at = ? WHERE id = ?',
            (now, now, user['id'])
        )
        conn.commit()

        # 设置会话
        session['user_id'] = user['id']
        session['username'] = user['username']
        session['display_name'] = user['display_name']
        session['logged_in'] = True

        logger.info(f"用户登录成功: {user['username']}")

        return jsonify({
            'success': True,
            'message': '登录成功',
            'user': {
                'id': user['id'],
                'username': user['username'],
                'email': user['email'],
                'display_name': user['display_name'],
                'last_login_at': user['last_login_at']
            }
        })

    except Exception as e:
        logger.error(f"用户登录失败: {e}")
        return jsonify({'success': False, 'error': '登录失败，请稍后重试'}), 500

@bp.route('/logout', methods=['POST'])
def logout():
    """用户登出"""
    try:
        username = session.get('username', 'unknown')

        # 清除会话
        session.clear()

        logger.info(f"用户登出: {username}")
        return jsonify({'success': True, 'message': '登出成功'})

    except Exception as e:
        logger.error(f"用户登出失败: {e}")
        return jsonify({'success': False, 'error': '登出失败'}), 500

@bp.route('/profile', methods=['GET'])
def get_profile():
    """获取当前用户信息"""
    try:
        if not session.get('logged_in'):
            return jsonify({'success': False, 'error': '未登录'}), 401

        user_id = session.get('user_id')
        conn = get_db_connection()

        user = conn.execute('''
            SELECT id, username, email, display_name, created_at, last_login_at
            FROM users WHERE id = ? AND is_active = 1
        ''', (user_id,)).fetchone()

        if not user:
            session.clear()
            return jsonify({'success': False, 'error': '用户不存在'}), 401

        return jsonify({
            'success': True,
            'user': dict(user)
        })

    except Exception as e:
        logger.error(f"获取用户信息失败: {e}")
        return jsonify({'success': False, 'error': '获取用户信息失败'}), 500

@bp.route('/check', methods=['GET'])
def check_login():
    """检查登录状态"""
    try:
        # 首先检查session中是否有登录信息
        if not session.get('logged_in') or not session.get('user_id'):
            return jsonify({
                'success': True,
                'logged_in': False
            })

        user_id = session.get('user_id')

        # 检查数据库文件是否存在
        if not os.path.exists(MAIN_DB_PATH):
            logger.warning("数据库文件不存在，清除会话")
            session.clear()
            return jsonify({
                'success': True,
                'logged_in': False
            })

        # 验证数据库中用户是否仍然存在且有效
        try:
            conn = get_db_connection()
            user = conn.execute('''
                SELECT id, username, display_name, is_active
                FROM users
                WHERE id = ? AND is_active = 1
            ''', (user_id,)).fetchone()

            if not user:
                # 用户不存在或已被禁用，清除会话
                logger.warning(f"用户 {user_id} 不存在或已被禁用，清除会话")
                session.clear()
                return jsonify({
                    'success': True,
                    'logged_in': False
                })

            # 用户存在且有效，返回登录状态
            return jsonify({
                'success': True,
                'logged_in': True,
                'user': {
                    'id': user['id'],
                    'username': user['username'],
                    'display_name': user['display_name']
                }
            })

        except sqlite3.Error as db_error:
            logger.error(f"数据库查询失败: {db_error}")
            # 数据库错误，清除会话
            session.clear()
            return jsonify({
                'success': True,
                'logged_in': False
            })

    except Exception as e:
        logger.error(f"检查登录状态失败: {e}")
        # 发生错误时清除会话
        session.clear()
        return jsonify({'success': False, 'error': '检查登录状态失败'}), 500
