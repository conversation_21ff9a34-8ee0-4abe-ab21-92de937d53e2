import api from './index'

export const accountsAPI = {
  // 获取账户列表
  getAccounts() {
    return api.get('/accounts/')
  },

  // 获取单个账户
  getAccount(id) {
    return api.get(`/accounts/${id}`)
  },

  // 创建账户
  createAccount(data) {
    return api.post('/accounts/', data)
  },

  // 更新账户
  updateAccount(id, data) {
    return api.put(`/accounts/${id}`, data)
  },

  // 删除账户
  deleteAccount(id) {
    return api.delete(`/accounts/${id}`)
  },

  // 获取账户余额
  getAccountBalance(id) {
    return api.get(`/accounts/${id}/balance`)
  },

  // 获取所有账户余额汇总
  getAllBalances() {
    return api.get('/accounts/balances')
  }
}
