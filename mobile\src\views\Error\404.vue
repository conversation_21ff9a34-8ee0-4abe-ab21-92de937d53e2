<template>
  <div class="not-found-page">
    <div class="content">
      <div class="icon">🔍</div>
      <h2>页面不存在</h2>
      <p>抱歉，您访问的页面不存在</p>
      <van-button type="primary" @click="goHome">
        返回首页
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.replace('/')
}
</script>

<style scoped>
.not-found-page {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f7f8fa;
}

.content {
  text-align: center;
  padding: 40px;
}

.icon {
  font-size: 64px;
  margin-bottom: 24px;
}

h2 {
  margin: 0 0 12px 0;
  color: #323233;
  font-size: 24px;
}

p {
  margin: 0 0 24px 0;
  color: #969799;
  font-size: 16px;
}
</style>
