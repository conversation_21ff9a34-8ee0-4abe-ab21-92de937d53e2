/* 设置页面关于部分样式 */
.settings-section {
  background-color: var(--color-card-bg);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

.settings-section h3 {
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-md);
  color: var(--color-text);
  border-bottom: 1px solid var(--color-border);
  padding-bottom: var(--spacing-sm);
}

.settings-about {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.about-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: var(--spacing-xl);
}

.about-logo .logo-icon {
  width: 60px;
  height: 60px;
  background-color: var(--color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  margin-bottom: var(--spacing-sm);
  font-weight: bold;
  font-size: 30px;
}

.about-app-name {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-text);
}

.about-description {
  flex: 1;
}

.about-description p {
  margin: var(--spacing-sm) 0;
  color: var(--color-text-light);
  line-height: 1.5;
}

.settings-actions {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.settings-btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-size: var(--font-size-sm);
  transition: background-color 0.3s;
}

.settings-btn:hover {
  background-color: var(--color-primary-dark);
}

.settings-btn.danger {
  background-color: var(--color-danger);
}

.settings-btn.danger:hover {
  background-color: var(--color-danger-dark);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-about {
    flex-direction: column;
    text-align: center;
  }
  
  .about-logo {
    margin-right: 0;
    margin-bottom: var(--spacing-lg);
  }
}
