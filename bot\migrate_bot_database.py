#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
机器人数据库迁移脚本
添加用户绑定相关字段
"""

import sqlite3
import logging
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def migrate_bot_database():
    """迁移机器人数据库"""
    try:
        # 确定数据库路径
        current_dir = os.path.dirname(__file__)  # bot
        db_path = os.path.join(current_dir, 'bot.db')
        
        logger.info(f"开始迁移机器人数据库: {db_path}")
        
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查是否需要添加新字段
        cursor.execute("PRAGMA table_info(bot_users)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # 添加缺失的字段
        if 'website_user_id' not in columns:
            logger.info("添加 website_user_id 字段")
            cursor.execute('ALTER TABLE bot_users ADD COLUMN website_user_id TEXT')
        
        if 'website_username' not in columns:
            logger.info("添加 website_username 字段")
            cursor.execute('ALTER TABLE bot_users ADD COLUMN website_username TEXT')
        
        if 'is_bound' not in columns:
            logger.info("添加 is_bound 字段")
            cursor.execute('ALTER TABLE bot_users ADD COLUMN is_bound BOOLEAN DEFAULT FALSE')
        
        # 移除 is_admin 字段（如果存在）
        if 'is_admin' in columns:
            logger.info("移除 is_admin 字段（重建表）")
            
            # 创建新表
            cursor.execute('''
                CREATE TABLE bot_users_new (
                    chat_id INTEGER PRIMARY KEY,
                    username TEXT,
                    first_name TEXT,
                    last_name TEXT,
                    website_user_id TEXT,
                    website_username TEXT,
                    is_bound BOOLEAN DEFAULT FALSE,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 复制数据（排除 is_admin 字段）
            cursor.execute('''
                INSERT INTO bot_users_new (
                    chat_id, username, first_name, last_name, 
                    website_user_id, website_username, is_bound,
                    is_active, created_at, updated_at
                )
                SELECT 
                    chat_id, username, first_name, last_name,
                    website_user_id, website_username, is_bound,
                    is_active, created_at, updated_at
                FROM bot_users
            ''')
            
            # 删除旧表，重命名新表
            cursor.execute('DROP TABLE bot_users')
            cursor.execute('ALTER TABLE bot_users_new RENAME TO bot_users')
        
        # 提交更改
        conn.commit()
        logger.info("机器人数据库迁移完成")
        
    except Exception as e:
        logger.error(f"迁移机器人数据库失败: {e}")
        if 'conn' in locals():
            conn.rollback()
        raise
    finally:
        if 'conn' in locals():
            conn.close()

def create_bot_database_if_not_exists():
    """如果机器人数据库不存在则创建"""
    try:
        current_dir = os.path.dirname(__file__)  # bot
        db_path = os.path.join(current_dir, 'bot.db')
        
        if not os.path.exists(db_path):
            logger.info("创建新的机器人数据库")
            
            # 导入数据库类并初始化
            from bot.database import bot_db
            bot_db.init_database()
            
            logger.info("机器人数据库创建完成")
        else:
            logger.info("机器人数据库已存在，执行迁移")
            migrate_bot_database()
            
    except Exception as e:
        logger.error(f"创建/迁移机器人数据库失败: {e}")
        raise

if __name__ == '__main__':
    create_bot_database_if_not_exists()
