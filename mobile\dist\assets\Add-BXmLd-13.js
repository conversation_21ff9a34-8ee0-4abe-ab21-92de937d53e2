import{a as p,l as I,c as P,e as l,d as n,f as r,w as s,g as F,o as G,t as d,n as V,i as T,s as $,H as A}from"./index--MNqwREY.js";import{useBudgetsStore as H}from"./budgets-DJh-v5Y8.js";import{f as L}from"./format-wz8GKlWC.js";import{g as z}from"./categories-Zg0JKh5g.js";import{_ as J}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./transactions-CrdnwW_k.js";const K={class:"add-budget-page"},O={class:"page-container"},Q={class:"currency-symbol"},W={class:"budget-preview"},X={class:"preview-card"},Z={class:"preview-header"},ee={class:"category-info"},te={class:"category-icon"},ae={class:"category-name"},oe={class:"amount"},le={class:"preview-details"},ne={class:"detail-item"},se={class:"value"},ue={class:"detail-item"},re={class:"value"},de={class:"submit-container"},ie={__name:"Add",setup(ve){const D=F(),h=H(),f=p(!1),i=p(!1),v=p(!1),m=p(!1),t=p({project_name:"",category:"",amount:"",currency:"MYR",year:new Date().getFullYear(),month:new Date().getMonth()+1,description:""}),k=z("budget"),U=[{text:"马来西亚令吉 (MYR)",value:"MYR"},{text:"人民币 (CNY)",value:"CNY"},{text:"美元 (USD)",value:"USD"},{text:"欧元 (EUR)",value:"EUR"},{text:"新加坡元 (SGD)",value:"SGD"}],w=new Date().getFullYear(),y=[];for(let o=w-2;o<=w+5;o++)y.push({text:`${o}年`,value:o});const x=[{text:"01月",value:1},{text:"02月",value:2},{text:"03月",value:3},{text:"04月",value:4},{text:"05月",value:5},{text:"06月",value:6},{text:"07月",value:7},{text:"08月",value:8},{text:"09月",value:9},{text:"10月",value:10},{text:"11月",value:11},{text:"12月",value:12}],S=I(()=>!t.value.year||!t.value.month?"选择年月":`${t.value.year}年${String(t.value.month).padStart(2,"0")}月`),R=()=>!t.value.year||!t.value.month?"未选择时间":`${t.value.year}年${t.value.month}月`,Y=({selectedOptions:o})=>{t.value.category=o[0].value,i.value=!1},M=({selectedOptions:o})=>{t.value.currency=o[0].value,v.value=!1},N=({selectedOptions:o})=>{t.value.year=o[0].value,t.value.month=o[1].value,m.value=!1},j=o=>({储蓄:"💰",固定:"🏠",流动:"🛒",债务:"💳"})[o]||"💰",q=o=>({MYR:"RM",CNY:"¥",USD:"$",EUR:"€",SGD:"S$"})[o]||"RM",B=async()=>{f.value=!0;try{if(!t.value.year||!t.value.month){$({message:"请选择预算年月",type:"fail"});return}const o=t.value.year,e=t.value.month,g=`${o}-${String(e).padStart(2,"0")}-01`,u=new Date(o,e,0).getDate(),c=`${o}-${String(e).padStart(2,"0")}-${String(u).padStart(2,"0")}`,_={...t.value,amount:parseFloat(t.value.amount),period:"monthly",start_date:g,end_date:c};await h.createBudget(_),A("预算创建成功"),D.back()}catch(o){console.error("创建预算失败:",o),$({message:o.message||"创建预算失败",type:"fail"})}finally{f.value=!1}};return(o,e)=>{const g=r("van-nav-bar"),u=r("van-field"),c=r("van-cell-group"),_=r("van-button"),E=r("van-form"),b=r("van-picker"),C=r("van-popup");return G(),P("div",K,[l(g,{title:"添加预算","left-arrow":"",onClickLeft:e[0]||(e[0]=a=>o.$router.back()),fixed:"",placeholder:""}),n("div",O,[l(E,{onSubmit:B},{default:s(()=>[l(c,{inset:"",title:"基本信息"},{default:s(()=>[l(u,{modelValue:t.value.project_name,"onUpdate:modelValue":e[1]||(e[1]=a=>t.value.project_name=a),name:"project_name",label:"项目名称",placeholder:"请输入预算项目名称",rules:[{required:!0,message:"请输入项目名称"}]},null,8,["modelValue"]),l(u,{modelValue:t.value.category,"onUpdate:modelValue":e[2]||(e[2]=a=>t.value.category=a),name:"category",label:"预算类别",placeholder:"选择支出类别",readonly:"","is-link":"",onClick:e[3]||(e[3]=a=>i.value=!0),rules:[{required:!0,message:"请选择预算类别"}]},null,8,["modelValue"]),l(u,{modelValue:t.value.amount,"onUpdate:modelValue":e[4]||(e[4]=a=>t.value.amount=a),name:"amount",label:"预算金额",placeholder:"请输入预算金额",type:"number",rules:[{required:!0,message:"请输入预算金额"},{pattern:/^\d+(\.\d{1,2})?$/,message:"请输入有效的金额"}]},{"left-icon":s(()=>[n("span",Q,d(q(t.value.currency)),1)]),_:1},8,["modelValue"]),l(u,{modelValue:t.value.currency,"onUpdate:modelValue":e[5]||(e[5]=a=>t.value.currency=a),name:"currency",label:"货币类型",placeholder:"选择货币",readonly:"","is-link":"",onClick:e[6]||(e[6]=a=>v.value=!0),rules:[{required:!0,message:"请选择货币类型"}]},null,8,["modelValue"])]),_:1}),l(c,{inset:"",title:"预算周期"},{default:s(()=>[l(u,{modelValue:S.value,"onUpdate:modelValue":e[7]||(e[7]=a=>S.value=a),name:"period",label:"预算月份",placeholder:"选择年月",readonly:"","is-link":"",onClick:e[8]||(e[8]=a=>m.value=!0),rules:[{required:!0,message:"请选择预算月份"}]},null,8,["modelValue"])]),_:1}),l(c,{inset:"",title:"其他信息"},{default:s(()=>[l(u,{modelValue:t.value.description,"onUpdate:modelValue":e[9]||(e[9]=a=>t.value.description=a),name:"description",label:"预算描述",placeholder:"添加预算说明（可选）",type:"textarea",rows:"3",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1}),n("div",W,[e[18]||(e[18]=n("h3",null,"预算预览",-1)),n("div",X,[n("div",Z,[n("div",ee,[n("span",te,d(j(t.value.category)),1),n("span",ae,d(t.value.category||"未选择类别"),1)]),n("div",oe,d(V(L)(t.value.amount||0)),1)]),n("div",le,[n("div",ne,[e[16]||(e[16]=n("span",{class:"label"},"项目：",-1)),n("span",se,d(t.value.project_name||"未填写"),1)]),n("div",ue,[e[17]||(e[17]=n("span",{class:"label"},"时间：",-1)),n("span",re,d(R()),1)])])])]),n("div",de,[l(_,{type:"primary","native-type":"submit",block:"",round:"",loading:f.value},{default:s(()=>e[19]||(e[19]=[T(" 创建预算 ")])),_:1,__:[19]},8,["loading"])])]),_:1})]),l(C,{show:i.value,"onUpdate:show":e[11]||(e[11]=a=>i.value=a),position:"bottom"},{default:s(()=>[l(b,{columns:V(k),onConfirm:Y,onCancel:e[10]||(e[10]=a=>i.value=!1)},null,8,["columns"])]),_:1},8,["show"]),l(C,{show:v.value,"onUpdate:show":e[13]||(e[13]=a=>v.value=a),position:"bottom"},{default:s(()=>[l(b,{columns:U,onConfirm:M,onCancel:e[12]||(e[12]=a=>v.value=!1)})]),_:1},8,["show"]),l(C,{show:m.value,"onUpdate:show":e[15]||(e[15]=a=>m.value=a),position:"bottom"},{default:s(()=>[l(b,{columns:[y,x],"default-index":[y.findIndex(a=>a.value===t.value.year),x.findIndex(a=>a.value===t.value.month)],title:"选择年月",onConfirm:N,onCancel:e[14]||(e[14]=a=>m.value=!1)},null,8,["columns","default-index"])]),_:1},8,["show"])])}}},_e=J(ie,[["__scopeId","data-v-0669e844"]]);export{_e as default};
