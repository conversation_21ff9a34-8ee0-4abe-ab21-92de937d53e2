"""
通知历史API蓝图
"""
import logging
import uuid
import sys
import os
from flask import Blueprint, request, jsonify, g
import sqlite3
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.database_config import MAIN_DB_PATH

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

bp = Blueprint('notifications', __name__, url_prefix='/api')

def get_db_connection():
    """获取数据库连接"""
    if 'db' not in g:
        g.db = sqlite3.connect(MAIN_DB_PATH)
        g.db.row_factory = sqlite3.Row
    return g.db

@bp.route('/notifications', methods=['GET'])
def get_notifications():
    """获取所有通知历史"""
    try:
        conn = get_db_connection()
        notifications = conn.execute('''
            SELECT * FROM notifications
            ORDER BY created_at DESC
        ''').fetchall()
        conn.close()

        return jsonify({
            'success': True,
            'notifications': [dict(notification) for notification in notifications]
        })

    except Exception as e:
        logger.error(f"获取通知历史失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@bp.route('/notifications', methods=['POST'])
def create_notification():
    """创建新通知"""
    try:
        data = request.json
        logger.info(f"创建通知: {data.get('title', '未知')}")

        # 验证必填字段
        required_fields = ['type', 'title', 'message']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必填字段: {field}'}), 400

        # 生成ID和时间戳
        notification_id = str(uuid.uuid4())
        now = datetime.now().isoformat()

        conn = get_db_connection()

        # 插入通知记录
        conn.execute('''
            INSERT INTO notifications (id, type, title, message, data, is_read, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            notification_id,
            data['type'],
            data['title'],
            data['message'],
            str(data.get('data', {})),  # 将数据转换为字符串存储
            False,  # 默认未读
            data.get('created_at', now)
        ))

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'id': notification_id
        })

    except Exception as e:
        logger.error(f"创建通知失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@bp.route('/notifications/unread-count', methods=['GET'])
def get_unread_count():
    """获取未读通知数量"""
    try:
        conn = get_db_connection()
        result = conn.execute('''
            SELECT COUNT(*) as count FROM notifications
            WHERE is_read = 0
        ''').fetchone()
        conn.close()

        count = result['count'] if result else 0

        return jsonify({
            'success': True,
            'count': count
        })

    except Exception as e:
        logger.error(f"获取未读通知数量失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@bp.route('/notifications/<notification_id>/read', methods=['POST'])
def mark_notification_as_read(notification_id):
    """标记通知为已读"""
    try:
        conn = get_db_connection()

        # 检查通知是否存在
        notification = conn.execute(
            'SELECT id FROM notifications WHERE id = ?',
            (notification_id,)
        ).fetchone()

        if not notification:
            conn.close()
            return jsonify({'error': '通知不存在'}), 404

        # 标记为已读
        conn.execute('''
            UPDATE notifications
            SET is_read = 1
            WHERE id = ?
        ''', (notification_id,))

        conn.commit()
        conn.close()

        return jsonify({'success': True})

    except Exception as e:
        logger.error(f"标记通知为已读失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@bp.route('/notifications/mark-all-read', methods=['POST'])
def mark_all_notifications_as_read():
    """标记所有通知为已读"""
    try:
        conn = get_db_connection()

        # 标记所有通知为已读
        conn.execute('UPDATE notifications SET is_read = 1')

        conn.commit()
        conn.close()

        return jsonify({'success': True})

    except Exception as e:
        logger.error(f"标记所有通知为已读失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@bp.route('/notifications', methods=['DELETE'])
def clear_all_notifications():
    """清空所有通知"""
    try:
        conn = get_db_connection()

        # 删除所有通知
        conn.execute('DELETE FROM notifications')

        conn.commit()
        conn.close()

        return jsonify({'success': True})

    except Exception as e:
        logger.error(f"清空通知失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@bp.route('/notifications/<notification_id>', methods=['DELETE'])
def delete_notification(notification_id):
    """删除单个通知"""
    try:
        conn = get_db_connection()

        # 检查通知是否存在
        notification = conn.execute(
            'SELECT id FROM notifications WHERE id = ?',
            (notification_id,)
        ).fetchone()

        if not notification:
            conn.close()
            return jsonify({'error': '通知不存在'}), 404

        # 删除通知
        conn.execute('DELETE FROM notifications WHERE id = ?', (notification_id,))

        conn.commit()
        conn.close()

        return jsonify({'success': True})

    except Exception as e:
        logger.error(f"删除通知失败: {str(e)}")
        return jsonify({'error': str(e)}), 500
